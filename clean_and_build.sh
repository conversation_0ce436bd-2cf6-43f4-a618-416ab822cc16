#!/bin/bash

# <PERSON><PERSON>t to clean the project and build a release APK with optimized settings

echo "Cleaning Flutter project..."
flutter clean

echo "Cleaning Gradle cache..."
cd android
./gradlew clean
cd ..

echo "Deleting build directory..."
rm -rf build/

echo "Deleting Dart tool cache..."
rm -rf .dart_tool/

echo "Getting Flutter dependencies..."
flutter pub get

echo "Building release APK with optimized settings..."
flutter build apk --release --target-platform android-arm,android-arm64 --split-per-abi

echo "Build process completed!"
echo "Check the build/app/outputs/flutter-apk/ directory for your APKs"
