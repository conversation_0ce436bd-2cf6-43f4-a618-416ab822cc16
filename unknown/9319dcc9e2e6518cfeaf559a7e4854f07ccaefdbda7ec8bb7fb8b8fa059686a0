import 'package:bus/bloc/logout_cubit/logout_cubit.dart';
import 'package:bus/bloc/logout_cubit/logout_states.dart';
import 'package:bus/bloc/profile_cubit/profile_cubit.dart';
import 'package:bus/bloc/profile_cubit/profile_states.dart';
import 'package:bus/config/global_variable.dart';
import 'package:bus/config/theme_colors.dart';
import 'package:bus/constant/path_route_name.dart';
import 'package:bus/helper/cache_helper.dart';
import 'package:bus/helper/response_state.dart';
import 'package:bus/translations/local_keys.g.dart';
import 'package:bus/utils/sized_box.dart';
import 'package:bus/views/custom_widgets/custom_text.dart';
import 'package:bus/views/custom_widgets/notification_settings_bottom_sheet.dart';
import 'package:bus/views/screens/classrooms_screen/classrooms_screen.dart';
import 'package:bus/views/screens/help_screen/help_screen.dart';
import 'package:bus/views/screens/languages_screen/languages_screen.dart';
import 'package:bus/views/screens/login_screen/login_screen.dart';
import 'package:bus/views/screens/profile_screen/profile_screen.dart';
import 'package:bus/widgets/custom_appbar.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:bus/views/screens/contact_us_screen/contact_us.dart';
import 'package:bus/views/screens/pro_screen/pro_screen.dart';

class SettingScreen extends StatelessWidget {
  static const String routeName = PathRouteName.setting;
  const SettingScreen({super.key});

  @override
  Widget build(BuildContext context) {
    debugPrint(context.locale.toString());
    return SingleChildScrollView(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 1.sh,
            height: 120.w,
            child: CustomAppBar(
              titleWidget: CustomText(
                text: AppStrings.setting.tr(),
                fontSize: 18,
                textAlign: TextAlign.center,
                fontW: FontWeight.w600,
                color: TColor.white,
              ),
            ),
          ),
          BlocBuilder<ProfileCubit, ProfileStates>(
            builder: (context, states) {
              switch (states.rStates) {
                case ResponseState.loading:
                  return const CircularProgressIndicator(
                    color: TColor.mainColor,
                  );
                case ResponseState.success:
                  return Padding(
                    padding: EdgeInsets.symmetric(horizontal: 12.w),
                    child: Row(
                      children: [
                        CircleAvatar(
                          radius: 31.r,
                          backgroundColor: TColor.mainColor,
                          child: CircleAvatar(
                            radius: 30.r,
                            backgroundColor: TColor.white,
                            backgroundImage: states.userModel!.logoPath != null
                                ? NetworkImage(states.userModel!.logoPath!)
                                : const NetworkImage(
                                    "https://www.nj.com/resizer/iqV2J-QFgh0227ybHBor4exTVBk=/800x0/smart/cloudfront-us-east-1.images.arcpublishing.com/advancelocal/SJGKVE5UNVESVCW7BBOHKQCZVE.jpg"),
                          ),
                        ),
                        const SBox(w: 20),
                        Column(
                          mainAxisAlignment: MainAxisAlignment.start,
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            CustomText(
                              color: Colors.black54,
                              text: states.userModel!.name,
                              fontSize: 17,
                              fontW: FontWeight.w600,
                            ),
                            CustomText(
                              color: Colors.black54,
                              text: states.userModel!.typeAuth,
                              fontSize: 13,
                              fontW: FontWeight.w600,
                            ),
                          ],
                        ),
                      ],
                    ),
                  );
                case ResponseState.failure:
                  return const SizedBox();
                default:
                  return const SizedBox();
              }
            },
          ),
          const SBox(h: 15),
          Padding(
            padding: EdgeInsets.symmetric(horizontal: 12.w),
            child: const Divider(),
          ),
          ListTile(
            onTap: () {
              Navigator.pushNamed(context, ProfileScreen.routeName);
            },
            title: CustomText(
              color: Colors.black54,
              text: AppStrings.profile.tr(),
              fontSize: 15,
              fontW: FontWeight.w600,
            ),
            trailing: Icon(
              Icons.arrow_forward_ios_outlined,
              size: 16.sp,
              color: Colors.black54,
            ),
          ),
          ListTile(
            onTap: () {
              if (subscriptionStatus != null && subscriptionStatus == false) {
                debugPrint('subscriptionStatus: $subscriptionStatus');
                showSubscriptionAlert(context);
              } else {
                Navigator.push(context, MaterialPageRoute(builder: (builder) {
                  return const ClassroomsScreen();
                }));
              }
            },
            title: CustomText(
              color: Colors.black54,
              text: AppStrings.classrooms.tr(),
              fontSize: 15,
              fontW: FontWeight.w600,
            ),
            trailing: Icon(
              Icons.arrow_forward_ios_outlined,
              size: 16.sp,
              color: Colors.black54,
            ),
          ),
          ListTile(
            onTap: () {
              Navigator.pushNamed(context, LanguagesScreen.routeName);
            },
            title: CustomText(
              color: Colors.black54,
              text: AppStrings.languages.tr(),
              fontSize: 15,
              fontW: FontWeight.w600,
            ),
            trailing: Icon(
              Icons.arrow_forward_ios_outlined,
              size: 16.sp,
              color: Colors.black54,
            ),
          ),
          ListTile(
            onTap: () {
              Navigator.pushNamed(context, HelpScreen.routeName);
            },
            title: CustomText(
              text: AppStrings.help.tr(),
              fontSize: 15,
              color: Colors.black54,
              fontW: FontWeight.w600,
            ),
            trailing: Icon(
              Icons.arrow_forward_ios_outlined,
              size: 16.sp,
              color: Colors.black54,
            ),
          ),

          // Notification Settings ListTile
          ListTile(
            onTap: () {
              showModalBottomSheet(
                context: context,
                isScrollControlled: true,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.vertical(
                    top: Radius.circular(20.r),
                  ),
                ),
                builder: (context) => const NotificationSettingsBottomSheet(),
              );
            },
            title: CustomText(
              text: AppStrings.initializeNotifications.tr(),
              fontSize: 15,
              color: Colors.black54,
              fontW: FontWeight.w600,
            ),
            trailing: Icon(
              Icons.notifications_outlined,
              size: 20.sp,
              color: Colors.black54,
            ),
          ),

          // Add Contact Us ListTile here
          ListTile(
            onTap: () {
              Navigator.pushNamed(context, ContactUsScreen.routeName);
            },
            title: CustomText(
              text: AppStrings.contactUs.tr(),
              fontSize: 15,
              color: Colors.black54,
              fontW: FontWeight.w600,
            ),
            trailing: Icon(
              Icons.arrow_forward_ios_outlined,
              size: 16.sp,
              color: Colors.black54,
            ),
          ),

          // Add Pro screen ListTile
          ListTile(
            onTap: () {
              Navigator.pushNamed(context, PROScreen.routeName);
            },
            title: CustomText(
              text: AppStrings.pro.tr(),
              fontSize: 18,
              color: TColor.mainColor,
              fontW: FontWeight.w600,
            ),
            trailing: Icon(
              Icons.arrow_forward_ios_outlined,
              size: 18.sp,
              color: TColor.mainColor,
            ),
          ),

          BlocConsumer<LogoutCubit, LogoutStates>(
            listener: (context, states) {
              if (states.rStates == ResponseState.success) {
                CacheHelper.remove("token");
                token = null;
                Navigator.pushReplacementNamed(context, LoginScreen.routeName);
              } else if (states.rStates == ResponseState.failure) {
                debugPrint("error working");
              }
            },
            builder: (context, states) {
              if (states.rStates != ResponseState.loading) {
                return Column(
                  children: [
                    ListTile(
                      onTap: () {
                        context.read<LogoutCubit>().logout();
                        CacheHelper.remove("token");
                        token = null;
                      },
                      title: CustomText(
                        color: Colors.redAccent,
                        text: AppStrings.exit.tr(),
                        fontSize: 15,
                        fontW: FontWeight.w600,
                      ),
                      trailing: Icon(
                        Icons.arrow_forward_ios_outlined,
                        size: 16.sp,
                        color: Colors.redAccent,
                      ),
                    ),
                    ListTile(
                      onTap: () {
                        // Show confirmation dialog before deleting account
                        showDialog(
                          context: context,
                          builder: (context) => AlertDialog(
                            title: CustomText(
                              text: AppStrings.deleteAccountTitle.tr(),
                              fontSize: 16,
                              fontW: FontWeight.bold,
                            ),
                            content: Column(
                              mainAxisSize: MainAxisSize.min,
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                CustomText(
                                  text: AppStrings.deleteAccountConfirm.tr(),
                                  maxLine: 3,
                                  fontSize: 14,
                                ),
                                const SBox(h: 10),
                                CustomText(
                                  text: AppStrings.deleteAccountNote.tr(),
                                  maxLine: 3,
                                  fontSize: 14,
                                  color: Colors.red,
                                ),
                              ],
                            ),
                            actions: [
                              TextButton(
                                onPressed: () {
                                  Navigator.pop(context);
                                },
                                child: CustomText(
                                  text: AppStrings.cancel.tr(),
                                  fontSize: 14,
                                  color: Colors.grey,
                                ),
                              ),
                              TextButton(
                                onPressed: () {
                                  context.read<LogoutCubit>().logout();
                                  CacheHelper.remove("token");
                                  token = null;
                                },
                                child: CustomText(
                                  text: AppStrings.deleteAccount.tr(),
                                  fontSize: 14,
                                  color: Colors.red,
                                ),
                              ),
                            ],
                          ),
                        );
                      },
                      title: CustomText(
                        color: Colors.red,
                        text: AppStrings.deleteAccount.tr(),
                        fontSize: 15,
                        fontW: FontWeight.w600,
                      ),
                      trailing: Icon(
                        Icons.delete_forever,
                        size: 20.sp,
                        color: Colors.red,
                      ),
                    ),
                  ],
                );
              } else {
                return const CircularProgressIndicator(
                  color: TColor.mainColor,
                );
              }
            },
          ),
        ],
      ),
    );
  }
}
