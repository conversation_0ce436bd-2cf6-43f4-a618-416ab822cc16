import 'package:bus/bloc/bus_supervisor_available_cubit/bus_supervisor_available_states.dart';
import 'package:bus/data/repo/available_superviosre_bus_in_driver_repo.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

class BusSupervisorAvailableCubit extends Cubit<BusSupervisorAvailableStates> {
  final _busSupervisorAvailableRepo = AvailableSupervisorBusInDriverRepo();
  BusSupervisorAvailableCubit() : super(BusSupervisorAvailableInitialStates());

  Future<void> busSupervisorAvailable() async {
    emit(BusSupervisorAvailableLoadingStates());
    try {
      final response = await _busSupervisorAvailableRepo.repo();
      if (response.status == true) {
        emit(
          BusSupervisorAvailableSuccessStates(busAvailableModels: response),
        );
      } else {
        emit(BusSupervisorAvailableErrorStates(error: response.message));
      }
    } catch (e, s) {
      debugPrint("catch error $s");
      debugPrint("catch error $e");
      emit(BusSupervisorAvailableErrorStates(error: e.toString()));
    }
  }
}
