import 'package:bus/bloc/delete_driver_supervisor_cubit/delete_driver_supervisor_states.dart';
import 'package:bus/data/repo/delete_driver_supervisor_repo.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

class DeleteDriverSupervisorCubit extends Cubit<DeleteDriverSupervisorStates> {
  final _deleteDriverSupervisorRepo = DeleteDriverSupervisorRepo();
  DeleteDriverSupervisorCubit() : super(DeleteDriverSupervisorInitialStates());

  Future<void> deleteDriverSupervisor({
    int? id,
  }) async {
    emit(DeleteDriverSupervisorLoadingStates());
    try {
      final response = await _deleteDriverSupervisorRepo.repo(id: id);
      if (response.status == true) {
        emit(DeleteDriverSupervisorSuccessStates(
            deleteDriverSupervisorModels: response));
        // _driverCubit.getDriver();
      } else {
        emit(DeleteDriverSupervisorErrorStates(error: response.message));
      }
    } catch (e, s) {
      debugPrint("catch error $s");
      debugPrint("catch error $e");
      emit(DeleteDriverSupervisorErrorStates(error: e.toString()));
    }
  }
}
