import 'package:equatable/equatable.dart';
import 'package:json_annotation/json_annotation.dart';

part 'absences_student_models.g.dart';

@JsonSerializable()
class AbsencesStudentModels extends Equatable {
  final int? id;
  final String? name;
  final String? phone;
  final String? grade_id;
  final String? gender_id;
  final String? school_id;
  final String? religion_id;
  final String? type__blood_id;
  final String? classroom_id;
  final String? bus_id;
  final String? address;
  final String? city_name;
  final String? status;
  final String? trip_type;
  final String? attendant_driver_id;
  final String? attendant_admins_id;
  final String? parent_key;
  final String? parent_secret;
  final String? Date_Birth;
  final String? logo;
  final double? latitude;
  final double? longitude;
  final String? deleted_at;
  final String? created_at;
  final String? updated_at;
  final String? logo_path;

  const AbsencesStudentModels({
    this.address,
    this.logo_path,
    this.deleted_at,
    this.phone,
    this.status,
    this.id,
    this.created_at,
    this.updated_at,
    this.name,
    this.classroom_id,
    this.bus_id,
    this.school_id,
    this.attendant_admins_id,
    this.attendant_driver_id,
    this.type__blood_id,
    this.religion_id,
    this.gender_id,
    this.city_name,
    this.parent_key,
    this.latitude,
    this.longitude,
    this.grade_id,
    this.trip_type,
    this.parent_secret,
    this.Date_Birth,
    this.logo,
  });

  factory AbsencesStudentModels.fromJson(Map<String, dynamic> json) {
    return _$AbsencesStudentModelsFromJson(json);
  }

  Map<String, dynamic> toJson() => _$AbsencesStudentModelsToJson(this);

  @override
  List<Object?> get props => [
        address,
        logo_path,
        deleted_at,
        phone,
        status,
        id,
        created_at,
        updated_at,
        name,
        classroom_id,
        bus_id,
        school_id,
        attendant_admins_id,
        attendant_driver_id,
        type__blood_id,
        religion_id,
        gender_id,
        city_name,
        parent_key,
        latitude,
        longitude,
        grade_id,
        trip_type,
        parent_secret,
        Date_Birth,
        logo,
      ];
}
