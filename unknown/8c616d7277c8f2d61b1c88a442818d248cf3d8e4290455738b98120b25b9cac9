import 'package:bus/bloc/profile_cubit/profile_cubit.dart';
import 'package:bus/bloc/profile_cubit/profile_states.dart';
import 'package:bus/config/global_variable.dart';
import 'package:bus/config/theme_colors.dart';
import 'package:bus/constant/path_route_name.dart';
import 'package:bus/helper/cache_helper.dart';
import 'package:bus/helper/response_state.dart';
import 'package:bus/translations/local_keys.g.dart';
import 'package:bus/utils/assets_utils.dart';
import 'package:bus/utils/sized_box.dart';
import 'package:bus/views/custom_widgets/custom_button.dart';
import 'package:bus/views/custom_widgets/custom_text.dart';
import 'package:bus/views/screens/change_password_screen/change_password_screen.dart';
import 'package:bus/views/screens/create_password_screen/create_password_screen.dart';
import 'package:bus/views/screens/update_profile_screen/update_profile_screen.dart';
import 'package:bus/widgets/custom_appbar.dart';
import 'package:bus/widgets/student_data_widget/custom_student_c_w.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:geocoding/geocoding.dart';

class ProfileScreen extends StatefulWidget {
  static const String routeName = PathRouteName.profileScreen;
  const ProfileScreen({super.key});

  @override
  State<ProfileScreen> createState() => _ProfileScreenState();
}

class _ProfileScreenState extends State<ProfileScreen> {
  String? addressData;
  bool? googleSignInStatus;

  @override
  void initState() {
    super.initState();

    // Load Google sign-in flag from cache
    _loadGoogleSignInStatus();

    // Force reload after a short delay to ensure it's up-to-date
    Future.delayed(const Duration(milliseconds: 300), () {
      if (mounted) {
        _loadGoogleSignInStatus();
      }
    });

    // Ensure profile data is loaded
    WidgetsBinding.instance.addPostFrameCallback((_) {
        context.read<ProfileCubit>().getProfile();

        // Load Google sign-in status again after profile is loaded
        _loadGoogleSignInStatus();
    });
  }

  /// Load Google sign-in status from cache
  Future<void> _loadGoogleSignInStatus() async {
    try {
      // Get the value from cache
      final cachedValue = CacheHelper.getBool("isGoogleSignIn");

      // Update both the local and global variables
      if (mounted) {
        setState(() {
          googleSignInStatus = cachedValue;
          isGoogleSignIn = cachedValue;
        });
      }

      debugPrint("_loadGoogleSignInStatus - cachedValue: $cachedValue, googleSignInStatus: $googleSignInStatus, isGoogleSignIn: $isGoogleSignIn");
    } catch (e) {
      debugPrint("_loadGoogleSignInStatus - Error: $e");
    }
  }

  /// get address from lat and long
  Future<void> _getAddressFromLatLng({
    double? lat,
    double? long,
    BuildContext? context,
  }) async {
    if (lat == null || long == null) {
      debugPrint("Latitude or longitude is null");
      return;
    }

    try {
      List<Placemark>? placeMarket = await placemarkFromCoordinates(
        lat,
        long,
      );

      if (placeMarket.isNotEmpty) {
        Placemark place = placeMarket[0];
        addressData =
            "${place.administrativeArea} ; ${place.locality} ; ${place.street}";
        if (mounted) {
          setState(() {});
        }
      }
    } catch (e) {
      debugPrint("Error getting address: $e");
    }
  }

  @override
  Widget build(BuildContext context) {
    // Reload Google sign-in flag from cache to ensure it's up-to-date
    final cachedValue = CacheHelper.getBool("isGoogleSignIn");
    isGoogleSignIn = cachedValue ?? false;
    debugPrint("ProfileScreen build - isGoogleSignIn: $isGoogleSignIn, raw cached value: $cachedValue");

    return Scaffold(
      appBar: CustomAppBar(
        titleWidget: CustomText(
          text: AppStrings.profile.tr(),
          fontSize: 18,
          textAlign: TextAlign.center,
          fontW: FontWeight.w600,
          color: TColor.white,
        ),
        leftWidget: context.locale.toString() == "ar"
            ? InkWell(
                onTap: () {
                  Navigator.pop(context);
                },
                child: SvgPicture.asset(AppAssets.arrowBack),
              )
            : InkWell(
                onTap: () {
                  Navigator.pop(context);
                },
                child: SvgPicture.asset(
                  AppAssets.forwardArrow,
                  colorFilter:
                      const ColorFilter.mode(TColor.white, BlendMode.srcIn),
                  width: 25.w,
                  height: 25.w,
                ),
              ),
      ),
      body: BlocBuilder<ProfileCubit, ProfileStates>(
        builder: (context, states) {
          switch (states.rStates) {
            case ResponseState.loading:
              return const Center(
                child: CircularProgressIndicator(
                  color: TColor.mainColor,
                ),
              );
            case ResponseState.success:
              if (states.userModel?.latitude != null &&
                  states.userModel?.longitude != null) {
                try {
                  _getAddressFromLatLng(
                      lat: double.parse(states.userModel!.latitude!),
                      long: double.parse(states.userModel!.longitude!),
                      context: context);
                } catch (e) {
                  debugPrint("Error parsing coordinates: $e");
                }
              }
              return SingleChildScrollView(
                child: SizedBox(
                  height: 1.sh,
                  width: 1.sw,
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.center,
                    children: [
                      Padding(
                        padding: EdgeInsets.symmetric(horizontal: 37.w),
                        child: CircleAvatar(
                          radius: 50.r,
                          backgroundColor: TColor.borderContainer,
                          child: CircleAvatar(
                            radius: 49.r,
                            backgroundColor: TColor.white,
                            backgroundImage: states.userModel?.logoPath != null
                                ? NetworkImage(states.userModel!.logoPath!)
                                : const NetworkImage(
                                    "https://www.nj.com/resizer/iqV2J-QFgh0227ybHBor4exTVBk=/800x0/smart/cloudfront-us-east-1.images.arcpublishing.com/advancelocal/SJGKVE5UNVESVCW7BBOHKQCZVE.jpg"),
                          ),
                        ),
                      ),
                      const SBox(h: 30),
                      Padding(
                        padding: EdgeInsets.symmetric(horizontal: 37.w),
                        child: Column(
                          children: [
                            CustomStudentCW(
                              label: AppStrings.schoolName.tr(),
                              isLabel: true,
                              name: states.userModel?.name ?? '',
                            ),
                            const SBox(h: 10),
                            CustomStudentCW(
                              label: AppStrings.address.tr(),
                              isLabel: true,
                              name: states.userModel?.address ?? '',
                            ),
                            // const SBox(h: 10),
                            // CustomStudentCW(
                            //   label: AppStrings.location.tr(),
                            //   isLabel: true,
                            //   name: "$addressData",
                            // ),
                            const SBox(h: 10),
                            CustomStudentCW(
                              label: AppStrings.email.tr(),
                              isLabel: true,
                              name: states.userModel?.email ?? '',
                            ),
                            const SBox(h: 10),
                            CustomStudentCW(
                              label: AppStrings.phoneNumber.tr(),
                              isLabel: true,
                              name: states.userModel?.phone ?? '',
                            ),
                            // const SBox(h: 10),
                            // CustomStudentCW(
                            //   label: AppStrings.city.tr(),
                            //   isLabel: true,
                            //   name: states.userModel!.city_name ??
                            //       AppStrings.city.tr(),
                            // ),
                            const SBox(h: 40),
                            CustomButton(
                              text: AppStrings.updateProfile.tr(),
                              onTap: () {
                                if (states.userModel != null) {
                                  Navigator.push(
                                    context,
                                    MaterialPageRoute(
                                      builder: (context) => UpdateProfileScreen(
                                        userModel: states.userModel,
                                      ),
                                    ),
                                  );
                                } else {
                                  ScaffoldMessenger.of(context).showSnackBar(
                                    const SnackBar(
                                        content:
                                            Text('Profile data not available')),
                                  );
                                }
                              },
                              width: 428,
                              height: 45,
                              radius: 15,
                              borderColor: TColor.mainColor,
                              bgColor: TColor.mainColor,
                            ),
                            const SBox(h: 10),
                            CustomButton(
                              text: googleSignInStatus == true
                                  ? AppStrings.createPassword.tr()
                                  : AppStrings.changePassword.tr(),
                              onTap: () {
                                // Store the context locally
                                final currentContext = context;

                                // Reload the Google sign-in status synchronously
                                final isGoogle = CacheHelper.getBool("isGoogleSignIn") ?? false;
                                debugPrint("Password button tap - isGoogle from cache: $isGoogle");

                                if (isGoogle == true) {
                                  // Navigate to create password screen for Google users
                                  debugPrint("Navigating to CreatePasswordScreen");
                                  Navigator.pushNamed(
                                      currentContext, CreatePasswordScreen.routeName);
                                } else {
                                  // Navigate to change password screen for regular users
                                  debugPrint("Navigating to ChangePasswordScreen");
                                  Navigator.pushNamed(
                                      currentContext, ChangePasswordScreen.routeName);
                                }
                              },
                              width: 428,
                              height: 45,
                              radius: 15,
                              borderColor: TColor.mainColor,
                              bgColor: TColor.mainColor,
                            ),
                          ],
                        ),
                      )
                    ],
                  ),
                ),
              );
            case ResponseState.failure:
              return const SizedBox();
            default:
              return const SizedBox();
          }
        },
      ),
    );
  }
}
