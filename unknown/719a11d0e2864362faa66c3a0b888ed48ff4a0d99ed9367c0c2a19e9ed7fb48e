import 'package:bus/data/models/add_driver_models/add_driver_models.dart';


abstract class AddSupervisorStates {}

class AddSupervisorInitialStates extends AddSupervisorStates {}

class AddSupervisorLoadingStates extends AddSupervisorStates {}

class AddSupervisorSuccessStates extends AddSupervisorStates {
  final AddDriverModels? addDriverModels;
  AddSupervisorSuccessStates({
    this.addDriverModels,
  });
}

class AddSupervisorErrorStates extends AddSupervisorStates {
  final String? error;
  AddSupervisorErrorStates({
    this.error,
  });
}
