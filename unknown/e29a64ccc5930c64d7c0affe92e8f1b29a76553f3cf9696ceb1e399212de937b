import 'package:bus/bloc/supervisor_cubit/supervisor_states.dart';
import 'package:bus/data/models/supervisor_mdodels/supervisor_info_models.dart';
import 'package:bus/data/repo/supervisor_repo.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

class SupervisorCubit extends Cubit<SupervisorStates> {
  final _supervisorRepo = SupervisorRepo();
  SupervisorCubit() : super(SupervisorInitialStates());

  List<SupervisorInfoModels> data = [];
  int _page = 1;
  int? last_pages, total, currentPage;
  bool? hasMoreData = false;

  void initScrollController({
    bool? isFirst = false,
    ScrollController? scrollController,
    Function()? setStates,
  }) {
    scrollController!.addListener(() {
      if (scrollController.position.maxScrollExtent ==
          scrollController.offset) {
        if (currentPage! < last_pages!) {
          hasMoreData = true;
          _page++;
          getSupervisor(
            pageNumber: _page,
            isFirst: false,
          );
          setStates!();
        } else {
          hasMoreData = false;
          setStates!();
        }
      }
    });
  }

  Future<void> getSupervisor({bool? isFirst = false, int? pageNumber}) async {
    emit(SupervisorLoadingStates());
    if (isFirst == true) {
      data = [];
      _page = 1;
    }
    try {
      final response = await _supervisorRepo.repo(pageNumber: pageNumber);
      if (response.status == true) {
        last_pages = response.data!.last_page;
        currentPage = response.data!.current_page;
        data.addAll(response.data!.data!);
        emit(SupervisorSuccessStates(supervisorModels: response));
      } else {
        emit(SupervisorErrorStates(error: response.message));
      }
    } catch (e) {
      debugPrint("catch error onsupervisor cubit$e");
      emit(SupervisorErrorStates(error: e.toString()));
    }
  }
}
