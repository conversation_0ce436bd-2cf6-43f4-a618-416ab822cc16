import 'package:bus/config/theme_colors.dart';
import 'package:bus/constant/path_route_name.dart';
import 'package:bus/translations/local_keys.g.dart';
import 'package:bus/utils/assets_utils.dart';
import 'package:bus/views/custom_widgets/custom_text.dart';
import 'package:bus/views/screens/requests_address_change_screen/request_type_tab_screen.dart';
import 'package:bus/widgets/custom_appbar.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';

class AllRequestScreen extends StatefulWidget {
  static const String routeName = PathRouteName.allRequest;
  const AllRequestScreen({Key? key}) : super(key: key);

  @override
  State<AllRequestScreen> createState() => _AllRequestScreenState();
}

class _AllRequestScreenState extends State<AllRequestScreen> with Ticker<PERSON>roviderStateMixin {
  late TabController _tabController;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 4, vsync: this);

    // Listen to tab changes to update the UI
    _tabController.addListener(() {
      if (_tabController.indexIsChanging) {
        setState(() {});
      }
    });
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: CustomAppBar(
        titleWidget: CustomText(
          text: AppStrings.requestsChangeAddress.tr(),
          fontSize: 18,
          textAlign: TextAlign.center,
          fontW: FontWeight.w600,
          color: TColor.white,
        ),
        // Simplified back button logic
        leftWidget: IconButton(
          onPressed: () => Navigator.pop(context),
          icon: SvgPicture.asset(
            context.locale.toString() == "ar"
                ? AppAssets.arrowBack
                : AppAssets.forwardArrow,
            colorFilter: const ColorFilter.mode(TColor.white, BlendMode.srcIn),
            width: 25.w,
            height: 25.w,
          ),
          tooltip: 'Back',
          splashRadius: 24.r,
        ),
      ),
      body: SafeArea(
        child: Column(
          children: [
            // Custom TabBar
            Padding(
              padding: EdgeInsets.only(top: 16.h, left: 4.w, right: 4.w),
              child: Container(
                width: double.infinity, // Ancho completo
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(25.r),
                  boxShadow: [
                    const BoxShadow(
                      color: Color.fromRGBO(158, 158, 158, 0.2),
                      blurRadius: 5,
                      offset: Offset(0, 2),
                    ),
                  ],
                ),
                child: Padding(
                  padding: EdgeInsets.symmetric(vertical: 5.h), // Solo padding vertical
                  child: TabBar(
                    controller: _tabController,
                    labelColor: TColor.white,
                    unselectedLabelColor: Colors.grey[700],
                    indicator: BoxDecoration(
                      borderRadius: BorderRadius.circular(20.r),
                      color: _getTabColor(_tabController.index),
                    ),
                    // إزالة الخط أسفل التاب بار
                    indicatorColor: Colors.transparent,
                    dividerColor: Colors.transparent,
                    indicatorSize: TabBarIndicatorSize.tab,
                    labelPadding: EdgeInsets.symmetric(horizontal: 2.w), // Reducido aún más para ajustar sin desplazamiento
                    isScrollable: false, // Desactivar el desplazamiento horizontal
                    padding: EdgeInsets.zero, // Sin padding adicional
                    tabs: [
                      _buildTab(AppStrings.newRequests.tr(), 0),
                      _buildTab(AppStrings.allRequests.tr(), 1),
                      _buildTab(AppStrings.acceptRequest.tr(), 2),
                      _buildTab(AppStrings.refusalRequest.tr(), 3),
                    ],
                  ),
                ),
              ),
            ),

            // TabBarView with smooth transitions
            Expanded(
              child: TabBarView(
                controller: _tabController,
                physics: const BouncingScrollPhysics(), // إضافة تأثير انتقالي مرن
                children: [
                  // New Requests Tab
                  RequestTypeTabScreen(
                    status: 'new',
                    appBarTitle: AppStrings.newRequests.tr(),
                    tabColor: TColor.newRequest,
                  ),

                  // All Requests Tab
                  RequestTypeTabScreen(
                    status: '',
                    appBarTitle: AppStrings.allRequests.tr(),
                    tabColor: TColor.waitRequest,
                  ),

                  // Accepted Requests Tab
                  RequestTypeTabScreen(
                    status: 'accept',
                    appBarTitle: AppStrings.acceptRequest.tr(),
                    tabColor: TColor.acceptRequest,
                  ),

                  // Refused Requests Tab
                  RequestTypeTabScreen(
                    status: 'unaccept',
                    appBarTitle: AppStrings.refusalRequest.tr(),
                    tabColor: TColor.refusalRequest,
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  // Get the appropriate color for the tab based on index
  Color _getTabColor(int index) {
    switch (index) {
      case 0:
        return TColor.newRequest;
      case 1:
        return TColor.waitRequest;
      case 2:
        return TColor.acceptRequest;
      case 3:
        return TColor.refusalRequest;
      default:
        return TColor.mainColor;
    }
  }

  // Build a tab with the given title and index
  Widget _buildTab(String title, int index) {
    return Tab(
      height: 40.h,
      child: Container(
        padding: EdgeInsets.symmetric(horizontal: 2.w), // Reducido aún más el padding horizontal
        alignment: Alignment.center,
        child: FittedBox( // Usar FittedBox para ajustar automáticamente el texto al espacio disponible
          fit: BoxFit.scaleDown,
          child: CustomText(
            text: title,
            fontSize: 12, // Reducido a 12 para mejor ajuste
            fontW: FontWeight.w600,
            color: _tabController.index == index ? TColor.white : Colors.grey[700] ?? Colors.grey,
            textAlign: TextAlign.center,
            maxLine: 1,
          ),
        ),
      ),
    );
  }
}
