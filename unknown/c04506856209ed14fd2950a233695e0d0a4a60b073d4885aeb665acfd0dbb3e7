import 'package:bus/data/models/absences_models/absences_info_models.dart';
import 'package:bus/data/models/student_models/student_links_models.dart';
import 'package:equatable/equatable.dart';
import 'package:json_annotation/json_annotation.dart';

part 'absences_data_models.g.dart';

@JsonSerializable()
class AbsencesDataModels extends Equatable {
  final int? current_page;
  final String? first_page_url;
  final int? from;
  final int? last_page;
  final String? last_page_url;
  final String? next_page_url;
  final String? path;
  final int? per_page;
  final String? prev_page_url;
  final int? to;
  final int? total;
  final List<StudentLinksModels>? links;
  final List<AbsencesInfoModels>? data;

  const AbsencesDataModels({
    this.last_page,
    this.links,
    this.to,
    this.prev_page_url,
    this.per_page,
    this.next_page_url,
    this.last_page_url,
    this.first_page_url,
    this.from,
    this.path,
    this.current_page,
    this.total,
    this.data,
  });
  factory AbsencesDataModels.fromJson(Map<String, dynamic> json) {
    return _$AbsencesDataModelsFromJson(json);
  }

  Map<String, dynamic> toJson() => _$AbsencesDataModelsToJson(this);

  @override
  List<Object?> get props => [
        last_page,
        links,
        to,
        prev_page_url,
        per_page,
        next_page_url,
        last_page_url,
        first_page_url,
        from,
        path,
        current_page,
        total,
        data,
      ];
}
