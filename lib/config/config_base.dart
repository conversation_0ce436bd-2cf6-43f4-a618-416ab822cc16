class ConfigBase {
  static const String fcmUrl = "https://fcm.googleapis.com/fcm/send";
  static const String socketUrl = "https://node.busatyapp.com/";
  static const String firebase = "auth/firebase-login";

  // static const String base = "https://stage.busatyapp.com/";
  static const String base = "https://test.busatyapp.com/";
  static const String baseUrl =
      "${base}api/schools/"; //https://stage.busatyapp.com/api/schools/students/download
  static const String register = "register";
  static const String login = "login";
  static const String forgotPassword = "forgot-password";
  static const String resetPassword = "reset-password";
  static const String profile = "user";
  static const String updateProfile = "general/data/update";
  static const String changePassword = "general/password/update";
  static const String createPassword = "general/password/create";
  static const String logout = "logout";
  static const String student = "students";
  static const String driver = "attendants/all/driver";
  static const String buses = "buses/all";
  // ignore: constant_identifier_names
  static const String buslocationtracker = "trips/map/show/";
  // static const String buslocationtracker = "general/buses/show/";
//https://test.busatyapp.com/api/schools/addresses/index
  static const String change_address_requests = "addresses/index";
  static const String acceptChangeAddressRequests = "addresses/accepted/";
  static const String refuseChangeAddressRequests = "addresses/unaccepted/";
  static const String temporaryAddresses = "temporary-addresses";
  static const String absences = "absences/index";
  static const String supervisor = "attendants/all/admins";
  static const String deleteStudent = "students/destroy/";
  static const String deleteBus = "buses/destroy/";
  static const String grade = "grades/all";
  static const String religion = "general/religion";
  static const String gender = "general/gender";
  static const String typeBlood = "general/type/blood";
  static const String downloadStudentsExampleFile = "students/download";
  static const String downloadStudentsPDFFile = "buses/export/data/pdf";
  static const String parentShow = "parents/show";
  static const String availableBusInDriver = "buses/show/availableAdd/driver";
  static const String availableBusInDriverSupervisor =
      "buses/show/availableAdd/admins";
  static const String deleteSupervisorAndDriver = "attendants/destroy";
  static const String coupon = "subscriptions/coupon";
  // Notifications
  static const String allParentsFcmTokens =
      "notifications/allParentFirebaseTokens";
  static const String parentFcmTokens = "notifications/parentFirebaseTokens";
  //https://test.busatyapp.com/api/parents/notifications?page=1&limit=10
  static const String showNotifications = "parents/notifications";
  static const String supervisorsFcmTokens =
      "notifications/attendantFirebaseTokens";
  static const String storeBusNotification =
      "notifications/storeBusNotification";
  static const String notificationSettings = "general/notification/settings";

  // Ads
  static const String ads = "general/ades";
  static const String completeProfile = "data/complete";

  /// deep link url
  // static const deepLink = "https://busaty-app.web.app/";
  static const deepLink = "https://test-5c820.web.app/";

  /// current trip in schools
  static const String schoolCurrentTrips = "trips/current";

  /// previous trips in schools
  static const String schoolPreviousTrips = "trips/previous/index";

  /// trip route details
  static const String tripRouteDetails = "general/trips/routes/";

  /// trip absent students
  static const String tripAbsentStudents = "general/trips/absents/";

  /// trip attendant students
  static const String tripAttendantStudents = "general/trips/attendants/";

  /// question helper
  static const String questionHelp = "question";

  /// download file excel
  // static const String fileNameAndExtension = "students/download/show";
  ///
  static const String fileNameAndExtension = "students/download/show";

  /// send code verification url
  static const String verificationCode = "verify";
  static String get statusUrl => "${base}api/app/status";
}
