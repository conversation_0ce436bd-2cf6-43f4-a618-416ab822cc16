import 'package:bus/views/screens/add_bus_screen/add_bus_screen.dart';
import 'package:bus/views/screens/add_driver_screen/add_driver_screen.dart';
import 'package:bus/views/screens/add_student_bus_file_screen/add_student_bus_file_screen.dart';
import 'package:bus/views/screens/add_student_file_screen/add_student_file_screen.dart';
import 'package:bus/views/screens/add_student_screen/add_student_screen.dart';
import 'package:bus/views/screens/add_student_to_bus_from_file_screen/add_student_to_bus_from_file_screen.dart';
import 'package:bus/views/screens/add_student_to_bus_manually_screen/add_student_to_bus_manually_screen.dart';
import 'package:bus/views/screens/add_supervisor_screen/add_supervisor_screen.dart';
import 'package:bus/views/screens/all_bus_screen/all_bus_screen.dart';
import 'package:bus/views/screens/all_driver_screen/all_driver_screen.dart';
import 'package:bus/views/screens/all_request_screen/all_request_screen.dart';
import 'package:bus/views/screens/all_supervisor_screen/all_supervisor_screen.dart';
import 'package:bus/views/screens/change_password_screen/change_password_screen.dart';
import 'package:bus/views/screens/create_password_screen/create_password_screen.dart';
import 'package:bus/views/screens/contact_us_screen/contact_us.dart';
import 'package:bus/views/screens/coupon_screen/coupon_screen.dart';
import 'package:bus/views/screens/done_screen/done_screen.dart';
import 'package:bus/views/screens/driver_data_screen/driver_data_screen.dart';
import 'package:bus/views/screens/forget_password_screen/forget_password_screen.dart';
import 'package:bus/views/screens/help_screen/help_screen.dart';
import 'package:bus/views/screens/home_screen/home_screen.dart';
import 'package:bus/views/screens/languages_screen/languages_screen.dart';
import 'package:bus/views/screens/layout_screen/layout_screen.dart';
import 'package:bus/views/screens/login_screen/login_screen.dart';
import 'package:bus/views/screens/new_password_screen/new_password_screen.dart';
import 'package:bus/views/screens/open_trip_screen/open_trip_screen.dart';
import 'package:bus/views/screens/parent_data_screen/parent_data_screen.dart';
import 'package:bus/views/screens/parent_screen/parent_screen.dart';
import 'package:bus/views/screens/pro_screen/pro_screen.dart';
import 'package:bus/views/screens/profile_screen/profile_screen.dart';
import 'package:bus/views/screens/reason_refusal_screen/reason_refusal_screen.dart';
import 'package:bus/views/screens/request_change_address_screen/request_change_address_screen.dart';
import 'package:bus/views/screens/requests_absences/requests_absences_screen.dart';
import 'package:bus/views/screens/requests_address_change_screen/requests_address_change_screen.dart';
import 'package:bus/views/screens/search_screen/search_screen.dart';
import 'package:bus/views/screens/send_code_screen/send_code_screen.dart';
import 'package:bus/views/screens/setting_screen/setting_screen.dart';
import 'package:bus/views/screens/signup_screen/signup_screen.dart';
import 'package:bus/views/screens/student_data_screen/student_data_screen.dart';
import 'package:bus/views/screens/student_screen/student_screen.dart';
import 'package:bus/views/screens/supervisor_data_screen/supervisor_data_screen.dart';
import 'package:bus/views/screens/update_profile_screen/update_profile_screen.dart';
import 'package:bus/views/screens/complete_profile_screen/complete_profile_screen.dart';
import 'package:bus/views/screens/previous_trips_screen/previous_trips_screen.dart';

import 'package:flutter/material.dart';

import '../views/screens/all_request_screen/table_request_change_address_screen.dart';
import '../views/screens/languages_screen/on_boarding_language_screen.dart';
import '../views/screens/notifications_screen/notifications_screen.dart';
import '../views/screens/student_address_screen/student_address_screen.dart';

Map<String, Widget Function(BuildContext)> routes = {
  OnBoardingLanguageScreen.routeName: (context) =>
      const OnBoardingLanguageScreen(),
  LayoutScreen.routeName: (context) => const LayoutScreen(),
  HomeScreen.routeName: (context) => const HomeScreen(),
  SearchScreen.routeName: (context) => const SearchScreen(),
  SettingScreen.routeName: (context) => const SettingScreen(),
  LoginScreen.routeName: (context) => const LoginScreen(),
  SignupScreen.routeName: (context) => const SignupScreen(),
  ForgetPasswordScreen.routeName: (context) => const ForgetPasswordScreen(),
  NewPasswordScreen.routeName: (context) => const NewPasswordScreen(),
  DoneScreen.routeName: (context) => const DoneScreen(),
  SendCodeScreen.routeName: (context) => const SendCodeScreen(),
  StudentScreen.routeName: (context) => const StudentScreen(),
  AddStudentScreen.routeName: (context) => AddStudentScreen(isEdit: false),
  AddStudentFileScreen.routeName: (context) => const AddStudentFileScreen(),
  StudentDataScreen.routeName: (context) => const StudentDataScreen(),
  AddSupervisorScreen.routeName: (context) =>
      const AddSupervisorScreen(isEdit: false),
  AddDriverScreen.routeName: (context) => const AddDriverScreen(isEdit: false),
  SupervisorDataScreen.routeName: (context) => const SupervisorDataScreen(),
  DriverDataScreen.routeName: (context) => const DriverDataScreen(),
  AllDriverScreen.routeName: (context) => const AllDriverScreen(),
  AllSupervisorScreen.routeName: (context) => const AllSupervisorScreen(),
  AddBusScreen.routeName: (context) => const AddBusScreen(isEdit: false),
  AllBuScreen.routeName: (context) => const AllBuScreen(),
  AddStudentBusFileScreen.routeName: (context) =>
      const AddStudentBusFileScreen(),
  AddStudentToBusFromFileScreen.routeName: (context) =>
      const AddStudentToBusFromFileScreen(),
  AddStudentToBusManuallyScreen.routeName: (context) =>
      const AddStudentToBusManuallyScreen(),
  LanguagesScreen.routeName: (context) => const LanguagesScreen(),
  ProfileScreen.routeName: (context) => const ProfileScreen(),
  ParentScreen.routeName: (context) => const ParentScreen(),
  ParentDataScreen.routeName: (context) => const ParentDataScreen(),
  UpdateProfileScreen.routeName: (context) => const UpdateProfileScreen(),
  ChangePasswordScreen.routeName: (context) => const ChangePasswordScreen(),
  CreatePasswordScreen.routeName: (context) => const CreatePasswordScreen(),
  AllRequestScreen.routeName: (context) => const AllRequestScreen(),
  RequestChangeAddressScreen.routeName: (context) =>
      const RequestChangeAddressScreen(),
  ReasonRefusalScreen.routeName: (context) => const ReasonRefusalScreen(),
  TableRequestChangeAddressScreen.routeName: (context) =>
      const TableRequestChangeAddressScreen(),
  RequestsAddressChangeScreen.routeName: (context) =>
      const RequestsAddressChangeScreen(),
  // BusDataScreen.routeName: (context) =>  BusDataScreen(),
  RequestsAbsencesScreen.routeName: (context) => const RequestsAbsencesScreen(),
  StudentAddressScreen.routeName: (context) => const StudentAddressScreen(),
  NotificationsScreen.routeName: (context) => const NotificationsScreen(),
  OpenTripScreen.routeName: (context) => const OpenTripScreen(),
  // No se pueden agregar rutas directas para MorningTripScreen y EveningTripScreen
  // porque requieren parámetros en el constructor
  HelpScreen.routeName: (context) => const HelpScreen(),
  ContactUsScreen.routeName: (context) => const ContactUsScreen(),
  PROScreen.routeName: (context) => const PROScreen(),
  CouponScreen.routeName: (context) => const CouponScreen(),
  PreviousTripsScreen.routeName: (context) => const PreviousTripsScreen(),

  CompleteProfileScreen.routeName: (context) => const CompleteProfileScreen(),
};
