import 'package:bus/helper/cache_helper.dart';
import 'package:flutter/material.dart';
import '../widgets/custom_alert_dialog.dart';
import 'package:easy_localization/easy_localization.dart';
import '../translations/local_keys.g.dart';
import '../config/theme_colors.dart';
import '../views/screens/pro_screen/pro_screen.dart';

String? token;
String? tempToken;
String? fCMToken;
String? userName;
String? userEmail;
String? userImageUrl;
String? userAddress;
String? userPhone;
bool? subscriptionStatus;
bool? isGoogleSignIn; // Flag to indicate if user signed in with Google

void showSubscriptionAlert(BuildContext context) {
  // Make sure we're using a valid context
  if (!context.mounted) return;

  // Use a local variable to capture the context
  final currentContext = context;

  globalAlertDialogue(
    AppStrings.subscriptionRequired.tr(),
    title2: AppStrings.subscriptionMessage.tr(),
    buttonText: AppStrings.subscribe.tr(),
    buttonText2: AppStrings.cancel.tr(),
    iconData: Icons.workspace_premium,
    iconDataColor: TColor.mainColor,
    iconBackColor: TColor.fillFormFieldB,
    canCancel: true,
    onCancel: () {
      // Use the captured context
      Navigator.of(currentContext).pushNamed(PROScreen.routeName);
    },
  );
}
final GlobalKey<ScaffoldMessengerState> snackBarKey = GlobalKey<ScaffoldMessengerState>();
const String socketToken = "71e456b873f87f214f139799878b911a";
const String firebaseServerKey = "AAAAD2sLMEM:APA91bHKGl6SMCq8kG9YMf0TZV5L_h13xBlc2iGDwcY8pqUPy6mh5HF4Q1ctRYBQP9lOrZfcW4gL-Ii0sMIl1BmoYizP2x5LMKJgN6ghjRq9sO5rf46tpNG_6zDOd_EXm6qUxR_NljXo";
bool checkSchoolFoundOrNot = false;

void initializeFCMToken() async {
  fCMToken = CacheHelper.getString("fcmToken");
  debugPrint("---------------------------------------------------------------");
  debugPrint("fcmToken: $fCMToken");
  debugPrint("---------------------------------------------------------------");
}
