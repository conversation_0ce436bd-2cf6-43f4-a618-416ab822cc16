import 'dart:async';
import 'dart:convert';
import 'dart:io';
import 'package:bus/config/config_base.dart';
import 'package:bus/config/global_variable.dart';
import 'package:bus/helper/network_serviecs.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter_inapp_purchase/flutter_inapp_purchase.dart';
import 'package:logger/logger.dart';



class PaymentService {
  PaymentService._internal();

  static final PaymentService instance = PaymentService._internal();

  late StreamSubscription<ConnectionResult> _connectionSubscription;
  late StreamSubscription<PurchasedItem?> _purchaseUpdatedSubscription;
  late StreamSubscription<PurchaseResult?> _purchaseErrorSubscription;

  final List<String> productIdsItem = Platform.isIOS
      ? [
    'com.School.yearly_subscription'
  ]
      : ["com.school.yearly_subscription",];

  List<IAPItem> allProducts = [];
  final ObserverList<Function> _proStatusChangedListeners =
  ObserverList<Function>();
  final ObserverList<Function> _errorListeners = ObserverList<Function>();
  bool _isProUser = false;

  bool get isProUser => _isProUser;

  void addToProStatusChangedListeners(Function callback) {
    _proStatusChangedListeners.add(callback);
  }

  void removeFromProStatusChangedListeners(Function callback) {
    _proStatusChangedListeners.remove(callback);
  }

  void addToErrorListeners(Function callback) {
    _errorListeners.add(callback);
  }

  void removeFromErrorListeners(Function callback) {
    _errorListeners.remove(callback);
  }

  void _callProStatusChangedListeners() {
    for (var callback in _proStatusChangedListeners) {
      callback();
    }
  }

  void _callErrorListeners(String? error) {
    for (var callback in _errorListeners) {
      callback(error);
    }
  }

  Future<void> initConnection() async {
    try {
      await FlutterInappPurchase.instance.initialize();
      _connectionSubscription =
          FlutterInappPurchase.connectionUpdated.listen((_) {});
      _purchaseUpdatedSubscription =
          FlutterInappPurchase.purchaseUpdated.listen(_handlePurchaseUpdate);
      _purchaseErrorSubscription =
          FlutterInappPurchase.purchaseError.listen(_handlePurchaseError);
      await _getItems();
      await _getPastPurchases();
    } catch (e) {
      Logger().e('Error initializing in-app purchase: $e');
      _callErrorListeners('Error initializing in-app purchase');
    }
  }

  void dispose() {
    _connectionSubscription.cancel();
    _purchaseErrorSubscription.cancel();
    _purchaseUpdatedSubscription.cancel();
    FlutterInappPurchase.instance.finalize();
  }

  void _handlePurchaseError(PurchaseResult? purchaseError) {
    _callErrorListeners(purchaseError?.message);
  }

  void _handlePurchaseUpdate(PurchasedItem? productItem) async {
    if (Platform.isAndroid) {
      await _handlePurchaseUpdateAndroid(productItem);
    } else {
      await _handlePurchaseUpdateIOS(productItem);
    }
  }

  Future<void> _handlePurchaseUpdateIOS(PurchasedItem? purchasedItem) async {
    switch (purchasedItem?.transactionStateIOS) {
      case TransactionState.failed:
        _callErrorListeners("Transaction Failed");
        FlutterInappPurchase.instance.finishTransaction(purchasedItem!);
        break;
      case TransactionState.purchased:
        await _verifyAndFinishTransaction(purchasedItem);
        break;
      case TransactionState.restored:
        FlutterInappPurchase.instance.finishTransaction(purchasedItem!);
        break;
      default:
    }
  }

  Future<void> _handlePurchaseUpdateAndroid(
      PurchasedItem? purchasedItem) async {
    if (purchasedItem?.purchaseStateAndroid == PurchaseState.purchased &&
        purchasedItem?.isAcknowledgedAndroid == false) {
      await _verifyAndFinishTransaction(purchasedItem);
    } else {
      _callErrorListeners("Something went wrong");
    }
  }

  Future<void> _verifyAndFinishTransaction(PurchasedItem? purchasedItem) async {
    bool isValid = await _validatePurchase(purchasedItem);
    try {
      if (isValid) {
        FlutterInappPurchase.instance.finishTransaction(purchasedItem!);
        _isProUser = true;
        _callProStatusChangedListeners();
      } else {
        _callErrorListeners("Verification failed");
      }
    } catch (_) {
      _callErrorListeners("Something went wrong");
    }
  }

  Future<bool> _validatePurchase(PurchasedItem? purchasedItem) async {
    try {
      if (purchasedItem?.transactionReceipt == null) {
        Logger().e('Purchase validation failed: No transaction receipt');
        return false;
      }

      // For iOS, the receipt is a base64 encoded string
      // For Android, it's a JSON string containing purchase details
      final receipt = Platform.isIOS
          ? purchasedItem!.transactionReceipt!
          : json.decode(purchasedItem!.transactionReceipt!);

      // Send the receipt to your backend for validation
      final response = await NetworkService().post(
        url: '${ConfigBase.baseUrl}/api/v1/validate-purchase',
        body: {
          'receipt': receipt,
          'platform': Platform.isIOS ? 'ios' : 'android',
          'productId': purchasedItem.productId,
        },
        isAuth: true,
      );

      if (response.statusCode == 200) {
        final responseData = json.decode(response.data);
        subscriptionStatus = true;
        return responseData['isValid'] ?? false;
      }

      Logger().e('Purchase validation failed: Invalid server response');
      return false;
    } catch (e) {
      Logger().e('Purchase validation error: $e');
      return false;
    }
  }

  Future<List<IAPItem>> get products async {
    if (allProducts.isEmpty) {
      await _getItems();
    }
    return allProducts;
  }

  Future<void> _getItems() async {
    Logger().e('Fetching products...');
    List<IAPItem> items =
    await FlutterInappPurchase.instance.getSubscriptions(productIdsItem);
    allProducts = List.from(items);
    Logger().e('Fetched products: ${allProducts.length}');
    for (var item in allProducts) {
      Logger().e(
          'Product: ${item.title}, Price: ${item.price}, Currency: ${item.currency}');
    }
  }

  Future<void> _getPastPurchases() async {
    List<PurchasedItem>? purchasedItems =
    await FlutterInappPurchase.instance.getAvailablePurchases();

    if (purchasedItems == null || purchasedItems.isEmpty) return;

    for (var purchasedItem in purchasedItems) {
      if (Platform.isAndroid) {
        Map map = json.decode(purchasedItem.transactionReceipt!);
        if (!map['acknowledged']) {
          await FlutterInappPurchase.instance.finishTransaction(purchasedItem);
        }
      } else if (Platform.isIOS) {
        // iOS doesn't need the same acknowledgment check as Android
        // Just finish the transaction if needed
        await FlutterInappPurchase.instance.finishTransaction(purchasedItem);
      }

      // For both platforms, set the user as Pro
      _isProUser = true;
      _callProStatusChangedListeners();
      subscriptionStatus = true;
    }

  }

  Future<void> buyProduct(IAPItem item) async {
    try {
      await FlutterInappPurchase.instance.requestSubscription(item.productId!);
      _getPastPurchases();
    } catch (error) {
      _callErrorListeners(error.toString());
    }
  }
}
