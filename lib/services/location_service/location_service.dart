import 'package:geolocator/geolocator.dart';

import 'location_service_widgets.dart';

class LocationService {
  /// When the location services are not enabled or permissions
  /// are denied the `Future` will return null.
  static Future<Position?> getCurrentPosition() async {
    bool serviceEnabled;
    LocationPermission permission;
    serviceEnabled = await Geolocator.isLocationServiceEnabled();
    if (!serviceEnabled) {
      // Location services are not enabled don't continue
      // accessing the position and request users of the
      // App to enable the location services.
      await LocationServiceWidgets.enableLocationServiceAlert();
      return null;
    }
    permission = await Geolocator.checkPermission();
    if (permission == LocationPermission.denied) {
      permission = await Geolocator.requestPermission();
      if (permission == LocationPermission.denied) {

        // Permissions are denied, next time you could try
        // requesting permissions again (this is also where
        // Android's shouldShowRequestPermissionRationale
        // returned true. According to Android guidelines
        // your App should show an explanatory UI now.
        return null;
      }
    }
    if (permission == LocationPermission.deniedForever) {
      // Permissions are denied forever, handle appropriately.
      await LocationServiceWidgets.enableLocationAccessAlert();
      return null;
    }
    // When we reach here, permissions are granted and we can
    // continue accessing the position of the device.
    return Geolocator.getCurrentPosition();
  }

  static Future<bool> checkIfPermissionGranted() async {
    final checkPer = await Geolocator.checkPermission();
    if (checkPer == LocationPermission.always ||
        checkPer == LocationPermission.whileInUse) {
      return true;
    } else {
      return false;
    }
  }
}
