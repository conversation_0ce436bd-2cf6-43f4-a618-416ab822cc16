import 'package:bus/config/theme_colors.dart';
import 'package:bus/utils/sized_box.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:bus/translations/local_keys.g.dart';
import 'student_widgets/custom_container_dialog_w.dart';

void customShowDialogW(BuildContext context) {
  showDialog(
      context: context,
      builder: (context) {
        return AlertDialog(
          backgroundColor: Colors.transparent,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.all(
              Radius.circular(10.0.r),
            ),
          ),
          content: Builder(
            builder: (context) {
              return Container(
                width: 210.w,
                height: 308.w,
                decoration: BoxDecoration(
                  color: TColor.white,
                  border: Border.all(color: TColor.tabColors, width: 1),
                  borderRadius: BorderRadius.circular(10.0.r),
                ),
                child: <PERSON>umn(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    CustomContainerDialogW(
                      icons: Icons.visibility,
                      name: AppStrings.show.tr(),
                      onTap: () {},
                    ),
                    const SBox(h:5),
                    CustomContainerDialogW(
                      icons: Icons.edit,
                      name: AppStrings.edit.tr(),
                      onTap: () {},
                    ),
                    const SBox(h:5),
                    CustomContainerDialogW(
                      icons: Icons.delete_outline_outlined,
                      name: AppStrings.delete.tr(),
                      onTap: () {},
                    ),
                    const SBox(h:5),
                    CustomContainerDialogW(
                      icons: Icons.groups_outlined,
                      name: AppStrings.addByHand.tr(),
                      onTap: () {},
                    ),
                    const SBox(h:5),
                    CustomContainerDialogW(
                      icons: Icons.file_upload_outlined,
                      name: AppStrings.addByFile.tr(),
                      onTap: () {},
                    ),
                    const SBox(h:5),
                    CustomContainerDialogW(
                      icons: Icons.visibility_outlined,
                      name: AppStrings.showStudent.tr(),
                      onTap: () {},
                    ),
                  ],
                ),
              );
            },
          ),
        );
      });
}
