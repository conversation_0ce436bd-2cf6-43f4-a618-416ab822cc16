import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:bus/translations/local_keys.g.dart';
import '../config/theme_colors.dart';
import '../utils/get_it_injection.dart';
import '../utils/helper.dart';
import '../utils/navigation_helper.dart';
import '../views/custom_widgets/custom_text.dart';
import 'package:url_launcher/url_launcher.dart';

globalAlertDialogue(
  String title1, {
  String? title2,
  VoidCallback? onCancel,
  VoidCallback? onOk,
  String? buttonText,
  String? buttonText2,
  IconData? iconData,
  Color? iconDataColor,
  Color? iconBackColor,
  bool canCancel = false,
}) {
  showDialog(
    context: getIt<NavHelper>().navigatorKey.currentState!.context,
    barrierDismissible: false,
    builder: (BuildContext context) {
      return SizedBox(
        height: 250,
        width: 450.w,
        child: AlertDialog(
          contentPadding: EdgeInsets.symmetric(horizontal: 24.w, vertical: 16),
          insetPadding:
              const EdgeInsets.symmetric(horizontal: 16.0, vertical: 24.0),
          alignment: Alignment.center,
          backgroundColor: Colors.white,
          title: Container(
            width: 72.w,
            height: 72.h,
            decoration: BoxDecoration(
                color: iconBackColor ?? const Color(0xffFDEEEE),
                shape: BoxShape.circle),
            child: Center(
              child: Icon(
                iconData ?? Icons.info,
                color: iconDataColor ?? TColor.redAccent,
                size: 35,
              ),
            ),
          ),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Text(
                title1,
                textAlign: TextAlign.center,
                style: TextStyle(
                  // color: AppColors.blue,
                  fontSize: 20.sp,
                ),
              ),
              title2 == null
                  ? const SizedBox()
                  : Text(
                      title2,
                      textAlign: TextAlign.center,
                      style: TextStyle(
                        color: TColor.greenSuccess,
                        fontSize: 18.sp,
                        fontFamily: fontRegular,
                      ),
                    ),
              const SizedBox(
                height: 24,
              ),
              canCancel
                  ? Row(
                      mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                      children: [
                        Expanded(
                          child: ElevatedButton(
                            onPressed: onCancel ??
                                () {
                                  Navigator.pop(context);
                                },
                            style: ElevatedButton.styleFrom(
                              backgroundColor: TColor.white,
                              shape: RoundedRectangleBorder(
                                  borderRadius: BorderRadius.circular(8.0),
                                  side: const BorderSide(color: TColor.black)),
                            ),
                            child: Center(
                              child: Text(
                                buttonText ?? AppStrings.yes.tr(),
                                textAlign: TextAlign.center,
                                style: TextStyle(
                                    color: TColor.black, fontSize: 18.sp),
                              ),
                            ),
                          ),
                        ),
                        10.horizontalSpace,
                        Expanded(
                          child: ElevatedButton(
                            onPressed: onOk ??
                                () {
                                  Navigator.pop(context);
                                },
                            style: ElevatedButton.styleFrom(
                              backgroundColor: const Color(0xffEB5757),
                            ),
                            child: Center(
                              child: Text(
                                buttonText2 ?? AppStrings.yes.tr(),
                                textAlign: TextAlign.center,
                                style: TextStyle(
                                    color: TColor.white, fontSize: 18.sp),
                              ),
                            ),
                          ),
                        ),
                      ],
                    )
                  : ElevatedButton(
                      onPressed: onOk ??
                          () {
                            Navigator.pop(context);
                          },
                      style: ElevatedButton.styleFrom(
                        backgroundColor: TColor.mainColor,
                      ),
                      child: Center(
                        child: Text(
                          buttonText ?? AppStrings.yes.tr(),
                          textAlign: TextAlign.center,
                          style:
                              TextStyle(color: TColor.white, fontSize: 18.sp),
                        ),
                      ),
                    ),
            ],
          ),
        ),
      );
    },
  );
}

globalAlertDialogueWithDuration(
  String title1, {
  String? title2,
  IconData? iconData,
  int? seconds,
  required VoidCallback onOk,
}) {
  showDialog(
    context: getIt<NavHelper>().navigatorKey.currentState!.context,
    barrierDismissible: false,
    builder: (BuildContext context) {
      return SizedBox(
        height: 336.h,
        width: 348.w,
        child: AlertDialog(
          contentPadding: EdgeInsets.symmetric(horizontal: 24.w, vertical: 16),
          // insetPadding: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 24.0),
          alignment: Alignment.center,
          backgroundColor: TColor.white,
          title: Center(
            child: Icon(
              iconData ?? Icons.check_circle,
              color: TColor.greenSuccess,
              size: 46,
            ),
          ),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              CustomText(
                text: title1,
                fontSize: 24.sp,
                textAlign: TextAlign.center,
                fontW: FontWeight.w600,
              ),
              Text(
                title2 ?? "",
                textAlign: TextAlign.center,
                style: TextStyle(
                  color: TColor.greenSuccess,
                  fontSize: 18.sp,
                  fontFamily: fontRegular,
                ),
              ),
            ],
          ),
        ),
      );
    },
  );
  Future.delayed(Duration(seconds: seconds ?? 2), () {
    onOk();
  });
}

Future<void> showForceUpdateDialog(BuildContext context, String appUrl) async {
  // Add a small delay to ensure the widget tree is built
  await Future.delayed(const Duration(milliseconds: 100));

  if (!context.mounted) return;

  return showDialog(
    context: context,
    barrierDismissible: false,
    builder: (BuildContext dialogContext) {
      return AlertDialog(
        contentPadding: EdgeInsets.symmetric(horizontal: 24.w, vertical: 16),
        insetPadding:
            const EdgeInsets.symmetric(horizontal: 16.0, vertical: 24.0),
        alignment: Alignment.center,
        backgroundColor: Colors.white,
        title: Container(
          width: 72.w,
          height: 72.h,
          decoration: const BoxDecoration(
              color: Color(0xffFDEEEE), shape: BoxShape.circle),
          child: const Center(
            child: Icon(
              Icons.system_update,
              color: TColor.mainColor,
              size: 35,
            ),
          ),
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
              AppStrings.updateRequired.tr(),
              textAlign: TextAlign.center,
              style: TextStyle(
                fontSize: 20.sp,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              AppStrings.updateMessage.tr(),
              textAlign: TextAlign.center,
              style: TextStyle(
                fontSize: 16.sp,
                color: Colors.grey[600],
              ),
            ),
            const SizedBox(height: 24),
            ElevatedButton(
              onPressed: () async {
                final Uri url = Uri.parse(appUrl);
                if (await canLaunchUrl(url)) {
                  await launchUrl(url);
                }
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: TColor.mainColor,
                minimumSize: Size(double.infinity, 45.h),
              ),
              child: Text(
                AppStrings.update.tr(),
                style: TextStyle(
                  color: TColor.white,
                  fontSize: 18.sp,
                ),
              ),
            ),
          ],
        ),
      );
    },
  );
}
