import 'package:bus/config/theme_colors.dart';
import 'package:bus/data/models/pickup_location_local_models/pickup_location_local_models.dart';
import 'package:bus/translations/local_keys.g.dart';
import 'package:bus/views/custom_widgets/custom_button.dart';
import 'package:bus/views/custom_widgets/custom_text.dart';
import 'package:bus/widgets/custom_appbar.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:geocoding/geocoding.dart';
import 'package:geolocator/geolocator.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:open_street_map_search_and_pick/open_street_map_search_and_pick.dart';

class PickLocationWidget extends StatefulWidget {
  final double? latitude;
  final double? longitude;
  const PickLocationWidget({super.key, this.latitude, this.longitude});

  @override
  State<PickLocationWidget> createState() => _PickLocationWidgetState();
}

class _PickLocationWidgetState extends State<PickLocationWidget> {
  Position? myPosition;
  LatLong? position;
  GoogleMapController? googleMapController;
  Set<Marker> markerList = {};
  late TextEditingController place = TextEditingController();
  static CameraPosition initialCameraPosition = const CameraPosition(
    target: LatLng(23.893298884102258, 31.277979947626594),
    zoom: 14,
  );
  String? address;

  /// get address from lat and long
  Future<String> _getAddressFromLatLng({double? lat, double? long}) async {
    try {
      List<Placemark>? placeMarket = await placemarkFromCoordinates(
        lat!,
        long!,
      );
      Placemark place = placeMarket[0];
      return "${place.administrativeArea} ; ${place.locality} ; ${place.street}";
    } catch (e) {
      debugPrint("this is error $e");
      return "";
    }
  }

  Future<void> _determinePosition() async {
    bool serviceEnabled;
    LocationPermission permission;
    serviceEnabled = await Geolocator.isLocationServiceEnabled();
    if (!serviceEnabled) {
    
      return Future.error('Location services are disabled.');
    }

    permission = await Geolocator.checkPermission();
    if (permission == LocationPermission.denied) {
      permission = await Geolocator.requestPermission();
      if (permission == LocationPermission.denied) {
        return Future.error('Location permissions are denied');
      }
    }

    if (permission == LocationPermission.deniedForever) {
      return Future.error(
          'Location permissions are permanently denied, we cannot request permissions.');
    }
    myPosition = await Geolocator.getCurrentPosition();
    position = LatLong(myPosition!.latitude, myPosition!.longitude);
    if (myPosition != null) {
      initialCameraPosition = CameraPosition(
        target: LatLng(myPosition!.latitude, myPosition!.longitude),
        zoom: 14,
      );
      markerList.add(
        Marker(
          markerId: const MarkerId("0"),
          position: LatLng(myPosition!.latitude, myPosition!.longitude),
        ),
      );
      _getAddressFromLatLng(
        lat: myPosition!.latitude,
        long: myPosition!.longitude,
      ).then((value) {
        address = value;
        setState(() {});
      });
    }
    setState(() {});
  }

  @override
  void initState() {
    super.initState();
    _determinePosition();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: CustomAppBar(
        titleWidget: CustomText(
          text: AppStrings.setLocation.tr(),
          fontSize: 18,
          textAlign: TextAlign.center,
          fontW: FontWeight.w600,
          color: TColor.white,
        ),
      ),
      body: myPosition == null
          ? const Center(
              child: CircularProgressIndicator(
                color: TColor.mainColor,
              ),
            )
          : Stack(
              children: [
                GoogleMap(
                  onTap: (latLng) {
                    markerList.clear();
                    markerList.add(
                      Marker(
                        markerId: const MarkerId('0'),
                        position: latLng,
                      ),
                    );
                    _getAddressFromLatLng(
                      lat: latLng.latitude,
                      long: latLng.longitude,
                    ).then((value) {
                      address = value;
                      setState(() {});
                    });
                    position = LatLong(
                      latLng.latitude,
                      latLng.longitude,
                    );
                    setState(() {});
                  },
                  initialCameraPosition: initialCameraPosition,
                  markers: markerList,
                  zoomControlsEnabled: true,
                  onMapCreated: (GoogleMapController controller) {
                    googleMapController = controller;
                  },
                ),
                context.locale.toString() == "ar"
                    ? Positioned(
                        bottom: 130.w,
                        left: context.locale.toString() == "ar" ? 10 : 0,
                        child: InkWell(
                          onTap: () {
                            _determinePosition();
                          },
                          child: Container(
                            width: 50.w,
                            height: 50.w,
                            decoration: BoxDecoration(
                              color: TColor.white,
                              borderRadius: BorderRadius.circular(10.r),
                            ),
                            child: const Icon(
                              Icons.my_location_rounded,
                              color: TColor.mainColor,
                            ),
                          ),
                        ),
                      )
                    : Positioned(
                        bottom: 130.w,
                        right: context.locale.toString() == "ar" ? 0 : 10,
                        child: InkWell(
                          onTap: () {
                            _determinePosition();
                          },
                          child: Container(
                            width: 50.w,
                            height: 50.w,
                            decoration: BoxDecoration(
                              color: TColor.white,
                              borderRadius: BorderRadius.circular(10.r),
                            ),
                            child: const Icon(
                              Icons.my_location_rounded,
                              color: TColor.mainColor,
                            ),
                          ),
                        ),
                      ),
              ],
            ),
      bottomSheet: Container(
        padding: EdgeInsets.all(20.w),
        decoration: BoxDecoration(
          color: TColor.white,
          borderRadius: BorderRadius.only(
            topLeft: Radius.circular(20.r),
            topRight: Radius.circular(20.r),
          ),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.1),
              spreadRadius: 5,
              blurRadius: 7,
              offset: const Offset(0, 3),
            ),
          ],
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            if (address != null)
              Padding(
                padding: EdgeInsets.only(bottom: 10.w),
                child: CustomText(
                  text: address!,
                  fontSize: 14,
                  textAlign: TextAlign.center,
                  color: TColor.black,
                ),
              ),
            CustomButton(
              onTap: () {
                if (position != null) {
                  PickupLocationLocalModels pickedData =
                      PickupLocationLocalModels(
                          lat: position!.latitude,
                          long: position!.longitude,
                          address: address);
                  Navigator.pop(context, pickedData);
                }
              },
              text: AppStrings.save.tr(),
              fontSize: 16,
              radius: 20,
              fontWeight: FontWeight.w600,
              bgColor: TColor.mainColor,
              borderColor: TColor.mainColor,
            ),
          ],
        ),
      ),
    );
  }
}
