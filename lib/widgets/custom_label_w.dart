import 'package:bus/config/theme_colors.dart';
import 'package:bus/views/custom_widgets/custom_text.dart';
import 'package:flutter/material.dart';

class CustomLabelW extends StatelessWidget {
  final String? label;
  const CustomLabelW({
    Key? key,
    this.label = "",
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return CustomText(
      text: label,
      fontW: FontWeight.w500,
      fontSize: 15,
      color: TColor.namePersonal,
    );
  }
}
