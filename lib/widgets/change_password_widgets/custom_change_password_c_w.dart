import 'package:bus/config/theme_colors.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class CustomChangePasswordCW extends StatelessWidget {
  final Widget? child;
  const CustomChangePasswordCW({super.key,this.child,});

  @override
  Widget build(BuildContext context) {
    return Container(
      width: 354.w,
      height: 284.w,
      decoration: BoxDecoration(
        color: TColor.white,
        borderRadius: BorderRadius.circular(30.w),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.8),
            blurRadius: 2,
            offset: const  Offset(0, 2), // changes position of shadow
          ),
        ],
      ),
      child: child,
    );
  }
}
