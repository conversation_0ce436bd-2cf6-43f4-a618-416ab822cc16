import 'package:carousel_slider/carousel_slider.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import '../../config/theme_colors.dart';
import 'carousel_cubit/carousel_cubit.dart';

class CarouselWidget extends StatefulWidget {
  final List<Widget> items;
  const CarouselWidget({required this.items,Key? key}) : super(key: key);

  @override
  State<CarouselWidget> createState() => _CarouselWidgetState();
}

class _CarouselWidgetState extends State<CarouselWidget> {
  @override
  Widget build(BuildContext context) {
    return Stack(
      alignment: AlignmentDirectional.center,
      children: [
        CarouselSlider(
            items: widget.items,
            options: CarouselOptions(
              height: widget.items.isEmpty?0 :260.h,
              aspectRatio: 16/9,
              viewportFraction: .8,
              initialPage: 0,
              enableInfiniteScroll: true,
              reverse: false,
              autoPlay: true,
              autoPlayInterval: const Duration(seconds: 3),
              autoPlayAnimationDuration: const Duration(milliseconds: 800),
              autoPlayCurve: Curves.fastOutSlowIn,
              enlargeCenterPage: true,
              enlargeFactor: 0.3,
              onPageChanged: (index,reason){
                CarouselCubit.get(context).changeIndex(index);
              },
              scrollDirection: Axis.horizontal,
            )
        ),
        Positioned(
          bottom: 15.h,
          child: BlocBuilder<CarouselCubit, CarouselState>(
            builder: (context, state) {
              return state is CarouselInitial?Container(
                decoration: BoxDecoration(
                  color: TColor.white,
                  borderRadius: BorderRadius.circular(8.sp),
                ),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: List<Widget>.generate(
                    widget.items.length,
                        (index) => Padding(
                      padding: const EdgeInsets.all(3),
                      child: CircleAvatar(
                        radius: 3.5.sp,
                        backgroundColor: index==state.index?TColor.mainColor:TColor.underInput,
                      ),
                    ),
                  ),
                ),
              ):const SizedBox();
            },
          ),
        ),

      ],
    );
  }
}