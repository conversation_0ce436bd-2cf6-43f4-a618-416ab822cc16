import 'package:bus/config/theme_colors.dart';
import 'package:bus/views/custom_widgets/custom_text.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class CustomCRequestAddressW extends StatelessWidget {
  final String? name;
  final int? height;
  final textColor;
  const CustomCRequestAddressW({
    Key? key,
    this.name,
    this.textColor = TColor.dialogName,
    this.height = 53,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      width: 354.w,
      height: height!.w,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(15.r),
        color: TColor.inputRequestColor,
      ),
      child: Padding(
        padding: context.locale.toString() == "ar"
            ? EdgeInsets.only(
                top: height == 76 ? 12.w : 20.w,
                right: 15.w,
              )
            : EdgeInsets.only(
                top: height == 76 ? 12.w : 20.w,
                left: 15.w,
              ),
        child: CustomText(
          text: name,
          fontW: FontWeight.w400,
          fontSize: 14,
          color: textColor,
        ),
      ),
    );
  }
}
