import 'package:bus/config/theme_colors.dart';
import 'package:bus/views/custom_widgets/custom_text.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class CustomAcceptW extends StatelessWidget {
  final String? name;
  final Function()? onTap;
  const CustomAcceptW({Key? key, this.name,this.onTap,}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: onTap,
      child: Container(
        width: 172.w,
        height: 53.w,
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(15.r),
          color: TColor.borderContainer,
        ),
        child: Center(
          child: CustomText(
            text: name,
            fontSize: 20,
            fontW: FontWeight.w400,
            color: TColor.white,
          ),
        ),
      ),
    );
  }
}
