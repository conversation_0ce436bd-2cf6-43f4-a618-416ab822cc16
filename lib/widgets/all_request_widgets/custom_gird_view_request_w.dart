import 'package:badges/badges.dart' as badges;
import 'package:bus/config/theme_colors.dart';
import 'package:bus/data/models/request_models/request_models.dart';
import 'package:bus/translations/local_keys.g.dart';
import 'package:bus/views/custom_widgets/custom_text.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import '../../utils/helper.dart';
import '../../views/screens/requests_address_change_screen/requests_address_change_screen.dart';

class CustomGirdViewRequestW extends StatelessWidget {
  CustomGirdViewRequestW({super.key});

  final List<RequestModels> requestList = [
    RequestModels(
      id: 1,
      name: AppStrings.newRequests.tr(),
      color: TColor.newRequest,
      // number: 10,
    ),
    RequestModels(
      id: 2,
      name: AppStrings.allRequests.tr(),
      color: TColor.waitRequest,
    ),
    RequestModels(
      id: 3,
      name: AppStrings.acceptRequest.tr(),
      color: TColor.acceptRequest,
    ),
    RequestModels(
      id: 4,
      name: AppStrings.refusalRequest.tr(),
      color: TColor.refusalRequest,
    ),
  ];

  @override
  Widget build(BuildContext context) {
    return GridView.count(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      padding: EdgeInsets.only(left: 20.w, right: 20.w),
      crossAxisSpacing: 15,
      mainAxisSpacing: 4,
      crossAxisCount: 2,
      childAspectRatio: 2.0,
      children: List.generate(requestList.length, (index) {
        return CustomCGirdW(
          onTap: () {
            if (index == 0) {
              navigateTo(RequestsAddressChangeScreen(
                status: 'new',
                appBarTitle: AppStrings.newRequests.tr(),
              ));
            } else if (index == 1) {
              navigateTo(RequestsAddressChangeScreen(
                  appBarTitle: AppStrings.allRequests.tr()));
            } else if (index == 2) {
              navigateTo(RequestsAddressChangeScreen(
                status: 'accept',
                appBarTitle: AppStrings.acceptRequest.tr(),
              ));
            } else if (index == 3) {
              navigateTo(RequestsAddressChangeScreen(
                status: 'unaccept',
                appBarTitle: AppStrings.refusalRequest.tr(),
              ));
            }
          },
          name: requestList[index].name,
          number: requestList[index].number,
          color: requestList[index].color,
        );
      }),
    );
  }
}

class CustomCGirdW extends StatelessWidget {
  final String? name;
  final Color? color;
  final int? number;
  final Function()? onTap;

  const CustomCGirdW({
    Key? key,
    this.name,
    this.color,
    this.number,
    this.onTap,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Stack(
      clipBehavior: Clip.none,
      children: [
        InkWell(
          onTap: onTap,
          child: Container(
            width: 190.w,
            height: 81.w,
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(15.r),
              color: color,
            ),
            child: Center(
              child: CustomText(
                text: name,
                fontW: FontWeight.w400,
                fontSize: 14,
                color: TColor.white,
              ),
            ),
          ),
        ),
        number == null
            ? const SizedBox()
            : context.locale.toString() == "ar"
                ? Positioned(
                    right: -4,
                    top: -8,
                    child: badges.Badge(
                      badgeAnimation: const badges.BadgeAnimation.slide(),
                      badgeContent: CustomText(
                        text: number.toString(),
                        fontW: FontWeight.w500,
                        color: TColor.white,
                      ),
                    ),
                  )
                : Positioned(
                    left: -4,
                    top: -8,
                    child: badges.Badge(
                      badgeAnimation: const badges.BadgeAnimation.slide(),
                      badgeContent: CustomText(
                        text: number.toString(),
                        fontW: FontWeight.w500,
                        color: TColor.white,
                      ),
                    ),
                  )
      ],
    );
  }
}
