import 'package:bus/config/theme_colors.dart';
import 'package:bus/data/models/trip_models/previous_trip_model.dart';
import 'package:bus/translations/local_keys.g.dart';
import 'package:bus/views/custom_widgets/custom_text.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class PreviousTripCard extends StatelessWidget {
  final PreviousTripModel trip;
  final VoidCallback onViewDetailsPressed;

  const PreviousTripCard({
    Key? key,
    required this.trip,
    required this.onViewDetailsPressed,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    // Determine trip type color and text
    final bool isMorningTrip = trip.tripType == 'start_day';
    final Color tripTypeColor = isMorningTrip ? Colors.blue : Colors.orange;
    final String tripTypeText = isMorningTrip
        ? AppStrings.morningTrip.tr()
        : AppStrings.eveningTrip.tr();

    return Container(
      margin: EdgeInsets.symmetric(horizontal: 20.w, vertical: 10.h),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(15.r),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.15),
            spreadRadius: 1,
            blurRadius: 5,
            offset: const Offset(0, 3),
          ),
        ],
        color: Colors.white,
      ),
      child: Column(
        children: [
          // Header with bus name and trip type
          Container(
            padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 12.h),
            decoration: BoxDecoration(
              color: TColor.mainColor.withOpacity(0.1),
              borderRadius: BorderRadius.only(
                topLeft: Radius.circular(15.r),
                topRight: Radius.circular(15.r),
              ),
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Expanded(
                  child: Row(
                    children: [
                      Container(
                        padding: EdgeInsets.all(8.w),
                        decoration: const BoxDecoration(
                          color: Colors.white,
                          shape: BoxShape.circle,
                        ),
                        child: Icon(
                          Icons.directions_bus_rounded,
                          color: TColor.mainColor,
                          size: 20.sp,
                        ),
                      ),
                      SizedBox(width: 10.w),
                      Flexible(
                        child: CustomText(
                          text: trip.busName ?? AppStrings.notFound.tr(),
                          fontSize: 16,
                          fontW: FontWeight.w600,
                          color: TColor.mainColor,
                        ),
                      ),
                    ],
                  ),
                ),
                Container(
                  padding:
                      EdgeInsets.symmetric(horizontal: 12.w, vertical: 6.h),
                  decoration: BoxDecoration(
                    color: tripTypeColor.withOpacity(0.15),
                    borderRadius: BorderRadius.circular(20.r),
                    border: Border.all(
                      color: tripTypeColor.withOpacity(0.3),
                      width: 1,
                    ),
                  ),
                  child: CustomText(
                    text: tripTypeText,
                    fontSize: 12,
                    fontW: FontWeight.w600,
                    color: tripTypeColor,
                  ),
                ),
              ],
            ),
          ),

          // Trip details
          Padding(
            padding: EdgeInsets.all(16.w),
            child: Column(
              children: [
                // Trip times
                Container(
                  padding: EdgeInsets.all(12.w),
                  decoration: BoxDecoration(
                    color: Colors.grey.withOpacity(0.05),
                    borderRadius: BorderRadius.circular(12.r),
                  ),
                  child: IntrinsicHeight(
                    child: Row(
                      crossAxisAlignment: CrossAxisAlignment.stretch,
                      children: [
                        Expanded(
                          flex: 1,
                          child: _buildInfoItem(
                            icon: Icons.access_time,
                            title: AppStrings.tripStartTime.tr(),
                            value: trip.startTime ?? AppStrings.notFound.tr(),
                            iconColor: Colors.blue,
                          ),
                        ),
                        Container(
                          height: 40.h,
                          width: 1,
                          color: Colors.grey.withOpacity(0.2),
                        ),
                        Expanded(
                          flex: 1,
                          child: _buildInfoItem(
                            icon: Icons.access_time_filled,
                            title: AppStrings.tripEndTime.tr(),
                            value: trip.endTime ?? AppStrings.notFound.tr(),
                            iconColor: Colors.orange,
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
                SizedBox(height: 16.h),

                // Supervisor and Date
                IntrinsicHeight(
                  child: Row(
                    crossAxisAlignment: CrossAxisAlignment.stretch,
                    children: [
                      Expanded(
                        flex: 1,
                        child: _buildInfoCard(
                          icon: Icons.person,
                          title: AppStrings.supervisorName.tr(),
                          value:
                              trip.supervisorName ?? AppStrings.notFound.tr(),
                          iconColor: Colors.green,
                        ),
                      ),
                      SizedBox(width: 12.w),
                      Expanded(
                        flex: 1,
                        child: _buildInfoCard(
                          icon: Icons.calendar_today,
                          title: AppStrings.date.tr(),
                          value: trip.date ?? AppStrings.notFound.tr(),
                          iconColor: Colors.purple,
                        ),
                      ),
                    ],
                  ),
                ),
                SizedBox(height: 20.h),

                // Action button
                _buildActionButton(
                  icon: Icons.visibility,
                  label: AppStrings.viewDetails.tr(),
                  onPressed: onViewDetailsPressed,
                  color: TColor.mainColor,
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildInfoCard({
    required IconData icon,
    required String title,
    required String value,
    Color? iconColor,
  }) {
    final color = iconColor ?? TColor.mainColor;

    return Container(
      padding: EdgeInsets.all(12.w),
      decoration: BoxDecoration(
        color: Colors.grey.withOpacity(0.05),
        borderRadius: BorderRadius.circular(12.r),
      ),
      child: _buildInfoItem(
        icon: icon,
        title: title,
        value: value,
        iconColor: color,
      ),
    );
  }

  Widget _buildInfoItem({
    required IconData icon,
    required String title,
    required String value,
    Color? iconColor,
  }) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Icon(
          icon,
          size: 18.sp,
          color: iconColor ?? TColor.mainColor,
        ),
        SizedBox(width: 8.w),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              CustomText(
                text: title,
                fontSize: 12,
                fontW: FontWeight.w400,
                color: TColor.dialogName,
              ),
              SizedBox(height: 2.h),
              CustomText(
                text: value,
                fontSize: 14,
                fontW: FontWeight.w500,
                color: TColor.text,
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildActionButton({
    required IconData icon,
    required String label,
    required VoidCallback onPressed,
    required Color color,
  }) {
    return Container(
      width: double.infinity,
      height: 45.h,
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [color, color.withOpacity(0.8)],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(12.r),
        boxShadow: [
          BoxShadow(
            color: color.withOpacity(0.3),
            blurRadius: 8,
            offset: const Offset(0, 3),
          ),
        ],
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: onPressed,
          borderRadius: BorderRadius.circular(12.r),
          splashColor: Colors.white.withOpacity(0.1),
          highlightColor: Colors.transparent,
          child: Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                icon,
                size: 20.sp,
                color: Colors.white,
              ),
              SizedBox(width: 8.w),
              Text(
                label,
                style: TextStyle(
                  fontSize: 14.sp,
                  fontWeight: FontWeight.w600,
                  color: Colors.white,
                  letterSpacing: 0.5,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
