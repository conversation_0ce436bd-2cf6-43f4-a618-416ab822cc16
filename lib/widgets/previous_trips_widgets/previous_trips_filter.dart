import 'package:bus/bloc/buses_cubit/buses_cubit.dart';
import 'package:bus/bloc/buses_cubit/buses_states.dart';
import 'package:bus/bloc/previous_trips_cubit/previous_trips_cubit.dart';
import 'package:bus/config/theme_colors.dart';
import 'package:bus/translations/local_keys.g.dart';
import 'package:bus/views/custom_widgets/custom_text.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_datetime_picker_plus/flutter_datetime_picker_plus.dart'
    as date_picker;
import 'package:flutter_screenutil/flutter_screenutil.dart';

class PreviousTripsFilter extends StatefulWidget {
  const PreviousTripsFilter({super.key});

  @override
  State<PreviousTripsFilter> createState() => _PreviousTripsFilterState();
}

class _PreviousTripsFilterState extends State<PreviousTripsFilter> {
  String? selectedBusId;
  DateTime? selectedDate;
  bool isExpanded = false;

  @override
  Widget build(BuildContext context) {
    return AnimatedContainer(
      duration: const Duration(milliseconds: 300),
      margin: EdgeInsets.symmetric(horizontal: 20.w, vertical: 10.h),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(10.r),
        boxShadow: [
          BoxShadow(
            color: isExpanded
                ? TColor.mainColor.withAlpha(40) // Highlight when expanded
                : Colors.grey.withAlpha(26), // 0.1 * 255 = 25.5 ≈ 26
            spreadRadius: isExpanded ? 2 : 1,
            blurRadius: isExpanded ? 5 : 3,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        children: [
          // Filter header
          InkWell(
            onTap: () {
              setState(() {
                isExpanded = !isExpanded;
              });
            },
            child: Padding(
              padding: EdgeInsets.symmetric(horizontal: 15.w, vertical: 12.h),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  CustomText(
                    text: AppStrings.filterByBus.tr(),
                    fontSize: 16,
                    fontW: FontWeight.w600,
                    color: isExpanded ? TColor.mainColor : TColor.text,
                  ),
                  AnimatedSwitcher(
                    duration: const Duration(milliseconds: 300),
                    transitionBuilder:
                        (Widget child, Animation<double> animation) {
                      return RotationTransition(
                        turns: animation,
                        child: FadeTransition(opacity: animation, child: child),
                      );
                    },
                    child: Icon(
                      isExpanded
                          ? Icons.filter_list
                          : Icons.filter_alt_outlined,
                      key: ValueKey<bool>(isExpanded),
                      color: isExpanded ? TColor.mainColor : TColor.text,
                    ),
                  ),
                ],
              ),
            ),
          ),

          // Filter content
          if (isExpanded)
            Padding(
              padding: EdgeInsets.symmetric(horizontal: 15.w, vertical: 10.h),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Bus filter
                  CustomText(
                    text: AppStrings.filterByBus.tr(),
                    fontSize: 14,
                    fontW: FontWeight.w500,
                    color: TColor.text,
                  ),
                  SizedBox(height: 8.h),
                  _buildBusDropdown(),
                  SizedBox(height: 15.h),

                  // Date filter
                  CustomText(
                    text: AppStrings.filterByDate.tr(),
                    fontSize: 14,
                    fontW: FontWeight.w500,
                    color: TColor.text,
                  ),
                  SizedBox(height: 8.h),
                  _buildDatePicker(),
                  SizedBox(height: 20.h),

                  // Filter actions
                  Row(
                    children: [
                      Expanded(
                        child: Container(
                          width: double.infinity,
                          height: 40.h,
                          decoration: BoxDecoration(
                            color: Colors.white,
                            borderRadius: BorderRadius.circular(8.r),
                            border: Border.all(color: TColor.grey5),
                          ),
                          child: Material(
                            color: Colors.transparent,
                            child: InkWell(
                              onTap: _resetFilters,
                              borderRadius: BorderRadius.circular(8.r),
                              splashColor: TColor.grey5.withOpacity(0.1),
                              highlightColor: Colors.transparent,
                              child: Center(
                                child: Row(
                                  mainAxisAlignment: MainAxisAlignment.center,
                                  children: [
                                    Icon(
                                      Icons.refresh,
                                      size: 18.sp,
                                      color: TColor.text,
                                    ),
                                    SizedBox(width: 8.w),
                                    Text(
                                      AppStrings.resetFilters.tr(),
                                      style: TextStyle(
                                        fontSize: 14.sp,
                                        fontWeight: FontWeight.w500,
                                        color: TColor.text,
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                            ),
                          ),
                        ),
                      ),
                      SizedBox(width: 10.w),
                      Expanded(
                        child: Container(
                          width: double.infinity,
                          height: 40.h,
                          decoration: BoxDecoration(
                            gradient: LinearGradient(
                              colors: [
                                TColor.mainColor,
                                TColor.mainColor.withOpacity(0.8)
                              ],
                              begin: Alignment.topLeft,
                              end: Alignment.bottomRight,
                            ),
                            borderRadius: BorderRadius.circular(8.r),
                            boxShadow: [
                              BoxShadow(
                                color: TColor.mainColor.withOpacity(0.3),
                                blurRadius: 4,
                                offset: const Offset(0, 2),
                              ),
                            ],
                          ),
                          child: Material(
                            color: Colors.transparent,
                            child: InkWell(
                              onTap: _applyFilters,
                              borderRadius: BorderRadius.circular(8.r),
                              splashColor: Colors.white.withOpacity(0.1),
                              highlightColor: Colors.transparent,
                              child: Center(
                                child: Row(
                                  mainAxisAlignment: MainAxisAlignment.center,
                                  children: [
                                    Icon(
                                      Icons.filter_list,
                                      size: 18.sp,
                                      color: Colors.white,
                                    ),
                                    SizedBox(width: 8.w),
                                    Text(
                                      AppStrings.applyFilters.tr(),
                                      style: TextStyle(
                                        fontSize: 14.sp,
                                        fontWeight: FontWeight.w600,
                                        color: Colors.white,
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildBusDropdown() {
    return BlocBuilder<BusesCubit, BusesState>(
      builder: (context, state) {
        // Add a default "All" option
        List<DropdownMenuItem<String>> busItems = [
          DropdownMenuItem<String>(
            value: "0",
            child: Text(AppStrings.all.tr()),
          ),
        ];

        // Add buses from the BusesCubit if available
        if (state is BusesPaginationSuccessStates ||
            context.read<BusesCubit>().data.isNotEmpty) {
          final buses = context.read<BusesCubit>().data;
          busItems.addAll(
            buses.map((bus) {
              return DropdownMenuItem<String>(
                value: bus.id.toString(),
                child: Text(bus.name ?? AppStrings.notFound.tr()),
              );
            }).toList(),
          );
        } else if (state is BusesLoadingStates) {
          return const Center(
            child: Padding(
              padding: EdgeInsets.symmetric(vertical: 10.0),
              child: CircularProgressIndicator(color: TColor.mainColor),
            ),
          );
        }

        return Container(
          padding: EdgeInsets.symmetric(horizontal: 10.w),
          decoration: BoxDecoration(
            border: Border.all(color: TColor.grey5),
            borderRadius: BorderRadius.circular(8.r),
            color: Colors.white,
          ),
          child: DropdownButtonHideUnderline(
            child: DropdownButton<String>(
              isExpanded: true,
              hint: Text(AppStrings.selectBus.tr()),
              value: selectedBusId,
              items: busItems,
              onChanged: (value) {
                setState(() {
                  selectedBusId = value;
                });
              },
              icon: const Icon(Icons.arrow_drop_down, color: TColor.mainColor),
              underline: Container(),
              dropdownColor: Colors.white,
              style: TextStyle(
                color: TColor.text,
                fontSize: 14.sp,
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildDatePicker() {
    return InkWell(
      onTap: () {
        date_picker.DatePicker.showDatePicker(
          context,
          showTitleActions: true,
          minTime: DateTime(2020, 1, 1),
          maxTime: DateTime.now(),
          onConfirm: (date) {
            setState(() {
              selectedDate = date;
            });
          },
          currentTime: selectedDate ?? DateTime.now(),
          locale: context.locale.toString() == "ar"
              ? date_picker.LocaleType.ar
              : date_picker.LocaleType.en,
          theme: date_picker.DatePickerTheme(
            headerColor: TColor.mainColor,
            backgroundColor: Colors.white,
            itemStyle: TextStyle(
              color: TColor.text,
              fontSize: 16.sp,
              fontWeight: FontWeight.w500,
            ),
            doneStyle: TextStyle(
              color: Colors.white,
              fontSize: 16.sp,
              fontWeight: FontWeight.w600,
            ),
            cancelStyle: TextStyle(
              color: Colors.white.withOpacity(0.8),
              fontSize: 16.sp,
              fontWeight: FontWeight.w500,
            ),
          ),
        );
      },
      child: Container(
        width: double.infinity,
        padding: EdgeInsets.symmetric(horizontal: 12.w, vertical: 15.h),
        decoration: BoxDecoration(
          border: Border.all(color: TColor.grey5),
          borderRadius: BorderRadius.circular(8.r),
          color: Colors.white,
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              selectedDate != null
                  ? DateFormat('yyyy-MM-dd').format(selectedDate!)
                  : AppStrings.selectAbsenceDate.tr(),
              style: TextStyle(
                color: selectedDate != null ? TColor.text : TColor.grey5,
                fontSize: 14.sp,
                fontWeight:
                    selectedDate != null ? FontWeight.w500 : FontWeight.w400,
              ),
            ),
            Icon(
              Icons.calendar_today,
              color: selectedDate != null ? TColor.mainColor : TColor.grey5,
              size: 20.sp,
            ),
          ],
        ),
      ),
    );
  }

  void _applyFilters() {
    final previousTripsCubit = context.read<PreviousTripsCubit>();

    // Apply filters with the selected values
    previousTripsCubit.setFilters(
      busId: selectedBusId,
      date: selectedDate != null
          ? DateFormat('yyyy-MM-dd').format(selectedDate!)
          : null,
    );

    // Fetch the filtered trips
    previousTripsCubit.getPreviousTrips(isFirst: true);

    // Close the filter panel after applying filters
    setState(() {
      isExpanded = false;
    });
  }

  void _resetFilters() {
    setState(() {
      selectedBusId = null;
      selectedDate = null;
      isExpanded = false; // Close the filter panel after resetting filters
    });

    // Reset filters and reload data
    final previousTripsCubit = context.read<PreviousTripsCubit>();
    previousTripsCubit.resetFilters();
    previousTripsCubit.getPreviousTrips(isFirst: true);
  }
}
