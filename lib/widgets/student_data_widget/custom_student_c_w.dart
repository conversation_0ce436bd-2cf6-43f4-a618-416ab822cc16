import 'package:bus/config/theme_colors.dart';
import 'package:bus/utils/sized_box.dart';
import 'package:bus/views/custom_widgets/custom_text.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class CustomStudentCW extends StatelessWidget {
  final String? name;
  final String? label;
  final bool? isLabel;
  const CustomStudentCW({
    super.key,
    this.name,
    this.label = "",
    this.isLabel = false,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        isLabel == true
            ? CustomText(
                text: label,
                fontW: FontWeight.w500,
                fontSize: 15,
                color: TColor.namePersonal,
              )
            : const SizedBox(),
        isLabel == true ? const SBox(h: 3) : const SizedBox(),
        Container(
          width: 428.w,
          height: 43.w,
          decoration: BoxDecoration(
            border: Border.all(color: TColor.dialogName, width: 1.w),
            borderRadius: BorderRadius.circular(10.r),
          ),
          child: Padding(
            padding: context.locale.toString() == "ar"
                ? EdgeInsets.only(right: 15.w, top: 12.w)
                : EdgeInsets.only(left: 15.w, top: 12.w),
            child: CustomText(
              text: name,
              fontSize: 16,
              fontW: FontWeight.w500,
              color: TColor.namePersonal,
            ),
          ),
        ),
      ],
    );
  }
}
