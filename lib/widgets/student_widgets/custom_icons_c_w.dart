import 'package:bus/config/theme_colors.dart';
import 'package:bus/translations/local_keys.g.dart';
import 'package:bus/views/screens/add_bus_screen/add_bus_screen.dart';
import 'package:bus/views/screens/add_driver_screen/add_driver_screen.dart';
import 'package:bus/views/screens/add_student_file_screen/add_student_file_screen.dart';
import 'package:bus/views/screens/add_student_screen/add_student_screen.dart';
import 'package:bus/views/screens/add_supervisor_screen/add_supervisor_screen.dart';
import 'package:bus/views/screens/parent_data_screen/parent_data_screen.dart';
import 'package:bus/widgets/student_widgets/custom_container_dialog_w.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';


class CustomIconsCW extends StatelessWidget {
  final String? type;
  const CustomIconsCW({
    Key? key,
    this.type,
  }) : super(key: key);

  _showDialogStudent(BuildContext context) {
    showDialog(
        context: context,
        builder: (context) {
          return AlertDialog(
            backgroundColor: Colors.transparent,
            insetPadding: context.locale.toString() == "ar"
                ? EdgeInsets.only(bottom: 350.w, left: 110.w)
                : EdgeInsets.only(bottom: 350.w, right: 110.w),
            shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.all(Radius.circular(8.0.r))),
            content: Builder(
              builder: (context) {
                return Container(
                  padding:
                      EdgeInsets.symmetric(horizontal: 20.w, vertical: 20.h),
                  decoration: BoxDecoration(
                    color: TColor.white,
                    border: Border.all(color: TColor.tabColors, width: 1),
                    borderRadius: BorderRadius.circular(8.0.r),
                  ),
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      CustomContainerDialogW(
                        icons: Icons.groups_outlined,
                        name: AppStrings.addByHand.tr(),
                        onTap: () {
                          Navigator.pop(context);
                          Navigator.pushNamed(
                              context, AddStudentScreen.routeName);
                        },
                      ),
                      CustomContainerDialogW(
                        icons: Icons.file_upload_outlined,
                        name: AppStrings.addByFile.tr(),
                        onTap: () {
                          Navigator.pushNamed(
                              context, AddStudentFileScreen.routeName);
                        },
                      ),
                    ],
                  ),
                );
              },
            ),
          );
        });
  }

  _showDialogDriver(BuildContext context) {
    showDialog(
        context: context,
        builder: (context) {
          return AlertDialog(
            backgroundColor: Colors.transparent,
            insetPadding: context.locale.toString() == "ar"
                ? EdgeInsets.only(bottom: 350.w, left: 110.w)
                : EdgeInsets.only(bottom: 350.w, right: 110.w),
            shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.all(Radius.circular(8.0.r))),
            content: Builder(
              builder: (context) {
                return Container(
                  width: 100.w,
                  height: 132.w,
                  decoration: BoxDecoration(
                    color: TColor.white,
                    border: Border.all(color: TColor.tabColors, width: 1),
                    borderRadius: BorderRadius.circular(8.0.r),
                  ),
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      CustomContainerDialogW(
                        icons: Icons.groups_outlined,
                        name: AppStrings.addDriver.tr(),
                        onTap: () {
                          Navigator.pop(context);
                          Navigator.pushNamed(context, AddDriverScreen.routeName);
                        },
                      ),
                      // const SBox(),
                      // CustomContainerDialogW(
                      //   icons: Icons.file_upload_outlined,
                      //   name: LocaleKeys.addByFile.tr(),
                      //   onTap: () {
                      //     Navigator.pushNamed(context, AddStudentFileScreen.routeName);
                      //   },
                      // ),
                    ],
                  ),
                );
              },
            ),
          );
        });
  }

  _showDialogSupervisor(BuildContext context) {
    showDialog(
        context: context,
        builder: (context) {
          return AlertDialog(
            backgroundColor: Colors.transparent,
            insetPadding: context.locale.toString() == "ar"
                ? EdgeInsets.only(bottom: 350.w, left: 110.w)
                : EdgeInsets.only(bottom: 350.w, right: 110.w),
            shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.all(Radius.circular(8.0.r))),
            content: Builder(
              builder: (context) {
                return Container(
                  width: 100.w,
                  height: 132.w,
                  decoration: BoxDecoration(
                    color: TColor.white,
                    border: Border.all(color: TColor.tabColors, width: 1),
                    borderRadius: BorderRadius.circular(8.0.r),
                  ),
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      CustomContainerDialogW(
                        icons: Icons.groups_outlined,
                        name: AppStrings.addSupervisor.tr(),
                        onTap: () {
                          Navigator.of(context)
                          ..pop()
                          ..pushNamed(AddSupervisorScreen.routeName);
                        },
                      ),
                      // const SBox(),
                      // CustomContainerDialogW(
                      //   icons: Icons.file_upload_outlined,
                      //   name: LocaleKeys.addByFile.tr(),
                      //   onTap: () {
                      //     Navigator.pushNamed(context, AddStudentFileScreen.routeName);
                      //   },
                      // ),
                    ],
                  ),
                );
              },
            ),
          );
        });
  }

  _showDialogBus(BuildContext context) {
    showDialog(
        context: context,
        builder: (context) {
          return AlertDialog(
            backgroundColor: Colors.transparent,
            insetPadding: context.locale.toString() == "ar"
                ? EdgeInsets.only(bottom: 350.w, left: 110.w)
                : EdgeInsets.only(bottom: 350.w, right: 110.w),
            shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.all(Radius.circular(8.0.r))),
            content: Builder(
              builder: (context) {
                return Container(
                  width: 100.w,
                  height: 132.w,
                  decoration: BoxDecoration(
                    color: TColor.white,
                    border: Border.all(color: TColor.tabColors, width: 1),
                    borderRadius: BorderRadius.circular(8.0.r),
                  ),
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      CustomContainerDialogW(
                        icons: Icons.groups_outlined,
                        name: AppStrings.addBus.tr(),
                        onTap: () {
                          Navigator.pop(context);
                          Navigator.pushNamed(context, AddBusScreen.routeName);
                        },
                      ),
                    ],
                  ),
                );
              },
            ),
          );
        });
  }

  _showDialogParint(BuildContext context) {
    showDialog(
        context: context,
        builder: (context) {
          return AlertDialog(
            backgroundColor: Colors.transparent,
            insetPadding: context.locale.toString() == "ar"
                ? EdgeInsets.only(bottom: 350.w, left: 110.w)
                : EdgeInsets.only(bottom: 350.w, right: 110.w),
            shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.all(Radius.circular(8.0.r))),
            content: Builder(
              builder: (context) {
                return Container(
                  width: 100.w,
                  height: 132.w,
                  decoration: BoxDecoration(
                    color: TColor.white,
                    border: Border.all(color: TColor.tabColors, width: 1),
                    borderRadius: BorderRadius.circular(8.0.r),
                  ),
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      CustomContainerDialogW(
                        icons: Icons.groups_outlined,
                        name: AppStrings.showParent.tr(),
                        onTap: () {
                          Navigator.pop(context);
                          Navigator.pushNamed(
                              context, ParentDataScreen.routeName);
                        },
                      ),
                    ],
                  ),
                );
              },
            ),
          );
        });
  }

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: () {
        if (type == "student") {
          _showDialogStudent(context);
        } else if (type == "drive") {
          _showDialogDriver(context);
        } else if (type == "supervisor") {
          _showDialogSupervisor(context);
        } else if (type == "Bus") {
          _showDialogBus(context);
        } else if (type == "Parint") {
          _showDialogParint(context);
        }
      },
      child: SizedBox(
        height: 55.w,
        child: Stack(
          children: [
            Container(
              width: 47.w,
              height: 46.21.w,
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(8.r),
                color: TColor.borderContainer,
              ),
              child: Center(
                  child: Icon(
                Icons.add,
                color: Colors.white,
                size: 40.w,
              )),
            ),
          ],
        ),
      ),
    );
  }
}
