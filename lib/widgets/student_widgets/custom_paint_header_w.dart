import 'package:bus/widgets/student_widgets/my_custom_clipper_W.dart';
import 'package:flutter/material.dart';

class CustomPaintHeaderW extends StatelessWidget {
  const CustomPaintHeaderW({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final WIDTH = MediaQuery.of(context).size.width;
    return CustomPaint(
      // child: Container(
      //   width: 1.sw,
      //   height: 150.w,
      //   color: TColor.borderContainer,
      // ),
      size: Size(WIDTH, (WIDTH*0.35046728971962615).toDouble()),
      painter : <PERSON><PERSON>ustomClipperW(),
    );
  }
}
