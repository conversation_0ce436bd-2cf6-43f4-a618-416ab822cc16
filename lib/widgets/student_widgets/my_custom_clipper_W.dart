

import 'package:flutter/cupertino.dart';


class MyCustomClipperW extends CustomPainter {
  @override
  void paint(Canvas canvas, Size size) {
    Path path_0 = Path();
    path_0.moveTo(size.width*0.00008927103,0);
    path_0.lineTo(size.width*1.000089,0);
    path_0.lineTo(size.width*1.000089,size.height*0.6418653);
    path_0.cubicTo(size.width*1.000089,size.height*0.6418653,size.width*0.9946939,size.height*0.7077867,size.width*0.9785537,size.height*0.7523267);
    path_0.cubicTo(size.width*0.9624136,size.height*0.7968733,size.width*0.9355280,size.height*0.8200333,size.width*0.9355280,size.height*0.8200333);
    path_0.lineTo(size.width*0.06452407,size.height*0.8205200);
    path_0.cubicTo(size.width*0.06452407,size.height*0.8205200,size.width*0.04234860,size.height*0.8229600,size.width*0.02422196,size.height*0.8698733);
    path_0.cubicTo(size.width*0.006095397,size.height*0.9167867,size.width*-0.01426056,size.height*-0.03333333,size.width*0.3503388,size.height*-0.04069013);
    // path_0.cubicTo(size.width*-0.01168224,size.height*0.9996333,size.width*NaN,size.height*NaN,size.width*NaN,size.height*NaN);
    path_0.lineTo(size.width*0.00008927103,0);
    path_0.close();

    Paint paint_0_fill = Paint()..style=PaintingStyle.fill;
    paint_0_fill.color = const  Color(0xff7771D2).withOpacity(1.0);
    canvas.drawPath(path_0,paint_0_fill);
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) {
     return true;
  }

}