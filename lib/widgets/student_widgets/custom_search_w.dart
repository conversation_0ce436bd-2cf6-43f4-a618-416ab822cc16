import 'package:bus/config/theme_colors.dart';
import 'package:bus/utils/sized_box.dart';
import 'package:bus/views/custom_widgets/custom_form_field_border.dart';
import 'package:bus/widgets/student_widgets/custom_icons_c_w.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class CustomSearchW extends StatelessWidget {
  final String hintText;
  final String? type;
  final bool isSearch;
  final bool isIcon;
  final TextEditingController? controller;
  final onpressedSearch;
  const CustomSearchW({
    this.onpressedSearch,
    Key? key,
    required this.hintText,
    this.type,
    this.isSearch = true,
    this.isIcon = true,
    this.controller,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: 20.w),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          isIcon
              ? CustomIconsCW(
                  type: type,
                )
              : Container(),
          const SBox(w: 20),
          isSearch


              ? CustomFormFieldWithBorder(
                  controller: controller,
                  paddingLeft: 0,
                  paddingRight: 0,
                  formFieldWidth: 296.w,
                  height: true,
                  hintText: hintText,
                  fillColor: TColor.fillFormFieldB,
                  borderColor: TColor.fillFormFieldB,
                  suffix: InkWell(
                    onTap: onpressedSearch,
                    child: const Icon(Icons.search),
                  ),
                )





              : const SizedBox(),
        ],
      ),
    );
  }
}
