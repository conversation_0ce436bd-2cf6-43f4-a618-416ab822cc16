import 'package:bus/config/theme_colors.dart';
import 'package:bus/utils/sized_box.dart';
import 'package:bus/views/custom_widgets/custom_text.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class CustomContainerDialogW extends StatelessWidget {
  final IconData? icons;
  final String? name;
  final Function()? onTap;
  const CustomContainerDialogW({
    super.key,
    this.name,
    this.icons,
    this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: onTap,
      child: Container(
        width: MediaQuery.of(context).size.width,
        height: 42.w,
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(10.w),
          color: TColor.fillFormFieldB,
        ),
        child: Padding(
          padding: EdgeInsets.symmetric(horizontal: 10.w),
          child: Row(
            children: [
              Icon(
                icons,
                color: TColor.mainColor,
              ),
              const SBox(w: 10),
              Expanded(
                child: CustomText(
                  text: name,
                  fontW: FontWeight.w500,
                  fontSize: 16,
                  color: TColor.mainColor,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
