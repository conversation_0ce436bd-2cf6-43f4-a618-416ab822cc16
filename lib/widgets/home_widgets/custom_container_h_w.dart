import 'package:bus/config/theme_colors.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

// Constants for standardized sizes
class _Sizes {
  static final double containerRadius = 24.r;
  static final double badgeHeight = 24.h;
  static final double badgeRadius = 12.r;
  static final double iconContainerSize = 48.r;
  static final double decorativeCircleSize = 100.r;

  // Paddings
  static final double containerPadding = 16.w;
  static final double badgePaddingH = 8.w;
  static final double badgePaddingV = 4.h;
  static final double contentSpacing = 8.h;

  // Font sizes
  static final double titleSize = 14.sp;
  static final double badgeTextSize = 12.sp;

  // Elevations and opacities

  // Adjusted sizes for better fit
  static final double containerMinHeight = 140.h;
  // Increased from 135.h to accommodate content
  static final double containerMaxWidth = 120.w;

  // New sizes for enhanced icon styling
  static final double iconInnerPadding = 8.r;
  static final double iconContainerGradientStop1 = 0.2;
  static final double iconContainerGradientStop2 = 0.8;
  static final double iconContainerBorderWidth = 1.5;
  static final double iconGlowRadius = 15.0;
  static final double iconGlowSpread = 1.0;
}

class CustomContainerHW extends StatefulWidget {
  final Widget? icon;
  final String? title;
  final Function()? onTap;
  final bool isActive;
  final String? count;
  final bool isAnimated;

  const CustomContainerHW({
    super.key,
    this.icon,
    this.title,
    this.onTap,
    this.isActive = true,
    this.count,
    this.isAnimated = true,
  });

  @override
  State<CustomContainerHW> createState() => _CustomContainerHWState();
}

class _CustomContainerHWState extends State<CustomContainerHW>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _scaleAnimation;
  bool _isPressed = false;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      duration: const Duration(milliseconds: 150),
      vsync: this,
    );
    _scaleAnimation = Tween<double>(
      begin: 1.0,
      end: 0.95,
    ).animate(CurvedAnimation(
      parent: _controller,
      curve: Curves.easeInOut,
    ));
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  void _handleTapDown(TapDownDetails details) {
    if (!widget.isAnimated) return;
    setState(() => _isPressed = true);
    _controller.forward();
  }

  void _handleTapUp(TapUpDetails details) {
    if (!widget.isAnimated) return;
    setState(() => _isPressed = false);
    _controller.reverse();
  }

  void _handleTapCancel() {
    if (!widget.isAnimated) return;
    setState(() => _isPressed = false);
    _controller.reverse();
  }

  @override
  Widget build(BuildContext context) {
    final isRTL = context.locale.languageCode == 'ar';

    return AnimatedBuilder(
      animation: _scaleAnimation,
      builder: (context, child) => Transform.scale(
        scale: _scaleAnimation.value,
        child: _buildContainer(isRTL),
      ),
    );
  }

  Widget _buildContainer(bool isRTL) {
    return Material(
      color: Colors.transparent,
      child: GestureDetector(
        onTapDown: _handleTapDown,
        onTapUp: _handleTapUp,
        onTapCancel: _handleTapCancel,
        onTap: widget.onTap,
        child: Container(
          constraints: BoxConstraints(
            minHeight: _Sizes.containerMinHeight,
            maxWidth: _Sizes.containerMaxWidth,
          ),
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(_Sizes.containerRadius),
            gradient: LinearGradient(
              begin: isRTL ? Alignment.topRight : Alignment.topLeft,
              end: isRTL ? Alignment.bottomLeft : Alignment.bottomRight,
              colors: widget.isActive
                  ? [
                      TColor.mainColor,
                      TColor.mainColor.withOpacity(0.85),
                    ]
                  : [
                      Colors.grey[300]!,
                      Colors.grey[400]!.withOpacity(0.7),
                    ],
            ),
            boxShadow: [
              BoxShadow(
                color: widget.isActive
                    ? TColor.mainColor.withOpacity(0.25)
                    : Colors.grey.withOpacity(0.25),
                blurRadius: 16,
                offset: const Offset(0, 8),
                spreadRadius: -2,
              ),
            ],
          ),
          child: ClipRRect(
            borderRadius: BorderRadius.circular(_Sizes.containerRadius),
            child: Stack(
              children: [
                _buildDecorativeElements(isRTL),
                _buildMainContent(isRTL),
                if (widget.count != null) _buildCountBadge(isRTL),
                if (_isPressed) _buildPressedOverlay(),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildDecorativeElements(bool isRTL) {
    return Positioned(
      right: isRTL ? null : -_Sizes.decorativeCircleSize / 2,
      left: isRTL ? -_Sizes.decorativeCircleSize / 2 : null,
      top: -_Sizes.decorativeCircleSize / 2,
      child: Container(
        width: _Sizes.decorativeCircleSize,
        height: _Sizes.decorativeCircleSize,
        decoration: BoxDecoration(
          shape: BoxShape.circle,
          color: Colors.white.withOpacity(0.12),
        ),
      ),
    );
  }

  Widget _buildMainContent(bool isRTL) {
    return Padding(
      padding: EdgeInsets.all(_Sizes.containerPadding),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisSize: MainAxisSize.min,
        mainAxisAlignment: MainAxisAlignment.start, // Changed from center to start
        children: [
          _buildEnhancedIconContainer(),
          SizedBox(height: _Sizes.contentSpacing),
          Flexible(  // Wrapped in Flexible to prevent overflow
            child: _buildTitle(isRTL),
          ),
        ],
      ),
    );
  }

  Widget _buildEnhancedIconContainer() {
    return Container(
      width: _Sizes.iconContainerSize,
      height: _Sizes.iconContainerSize,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(_Sizes.containerRadius / 2),
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          stops: [
            _Sizes.iconContainerGradientStop1,
            _Sizes.iconContainerGradientStop2,
          ],
          colors: widget.isActive
              ? [
                  Colors.white.withOpacity(0.25),
                  Colors.white.withOpacity(0.15),
                ]
              : [
                  Colors.grey[300]!.withOpacity(0.25),
                  Colors.grey[300]!.withOpacity(0.15),
                ],
        ),
        border: Border.all(
          color: widget.isActive
              ? Colors.white.withOpacity(0.3)
              : Colors.grey[400]!.withOpacity(0.3),
          width: _Sizes.iconContainerBorderWidth,
        ),
        boxShadow: [
          BoxShadow(
            color: widget.isActive
                ? TColor.mainColor.withOpacity(0.3)
                : Colors.black.withOpacity(0.1),
            blurRadius: _Sizes.iconGlowRadius,
            spreadRadius: _Sizes.iconGlowSpread,
          ),
          BoxShadow(
            color: Colors.white.withOpacity(0.1),
            blurRadius: 0,
            spreadRadius: 1,
            offset: const Offset(0, 1),
          ),
        ],
      ),
      child: Container(
        padding: EdgeInsets.all(_Sizes.iconInnerPadding),
        child: _buildIconWithEffect(),
      ),
    );
  }

  Widget _buildIconWithEffect() {
    if (widget.icon == null) return const SizedBox();

    return ShaderMask(
      shaderCallback: (Rect bounds) {
        return LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: widget.isActive
              ? [
                  Colors.white,
                  Colors.white.withOpacity(0.8),
                ]
              : [
                  Colors.grey[700]!,
                  Colors.grey[500]!,
                ],
        ).createShader(bounds);
      },
      child: widget.icon,
    );
  }

  Widget _buildTitle(bool isRTL) {
    return ConstrainedBox(
      constraints: BoxConstraints(
        maxWidth: 140.w,
      ),
      child: Text(
        widget.title ?? '',
        style: TextStyle(
          color: widget.isActive ? TColor.white : Colors.grey[700],
          fontSize: _Sizes.titleSize,
          fontWeight: FontWeight.w600,
          letterSpacing: 0.5,
          height: 1.2,
          shadows: widget.isActive
              ? [
                  Shadow(
                    color: Colors.black.withOpacity(0.3),
                    offset: const Offset(0, 1),
                    blurRadius: 2,
                  ),
                ]
              : null,
        ),
        textAlign: isRTL ? TextAlign.right : TextAlign.left,
        maxLines: 1,
        overflow: TextOverflow.ellipsis,
      ),
    );
  }

  Widget _buildCountBadge(bool isRTL) {
    return Positioned(
      top: _Sizes.containerPadding,
      right: isRTL ? null : _Sizes.containerPadding,
      left: isRTL ? _Sizes.containerPadding : null,
      child: Container(
        height: _Sizes.badgeHeight,
        padding: EdgeInsets.symmetric(
          horizontal: _Sizes.badgePaddingH,
          vertical: _Sizes.badgePaddingV,
        ),
        decoration: BoxDecoration(
          color: TColor.white,
          borderRadius: BorderRadius.circular(_Sizes.badgeRadius),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.1),
              blurRadius: 4,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Text(
          widget.count!,
          style: TextStyle(
            color: widget.isActive ? TColor.mainColor : Colors.grey[700],
            fontSize: _Sizes.badgeTextSize,
            fontWeight: FontWeight.w600,
          ),
        ),
      ),
    );
  }

  Widget _buildPressedOverlay() {
    return Positioned.fill(
      child: Container(
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(_Sizes.containerRadius),
          color: Colors.black.withOpacity(0.1),
        ),
      ),
    );
  }
}
