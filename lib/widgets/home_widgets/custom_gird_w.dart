import 'package:bus/bloc/buses_cubit/buses_cubit.dart';
import 'package:bus/bloc/driver_cubit/driver_cubit.dart';
import 'package:bus/bloc/parent_cubit/parent_cubit.dart';
import 'package:bus/bloc/student_cubit/student_cubit.dart';
import 'package:bus/bloc/supervisor_cubit/supervisor_cubit.dart';
import 'package:bus/config/global_variable.dart';
import 'package:bus/constant/path_route_name.dart';
import 'package:bus/translations/local_keys.g.dart';
import 'package:bus/utils/assets_utils.dart';
import 'package:bus/widgets/home_widgets/custom_container_h_w.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import '../../views/screens/all_request_screen/all_request_screen.dart';

class CustomGirdW extends StatefulWidget {
  const CustomGirdW({super.key});

  @override
  State<CustomGirdW> createState() => _CustomGirdWState();
}

class _CustomGirdWState extends State<CustomGirdW> {
  // Move constants to a separate class for better organization
  static final _Sizes = _GridSizes();

  final List<_GridItem> _gridItems = [
    _GridItem(icon: AppAssets.supervisorHomeIcon, title: AppStrings.students),
    _GridItem(icon: AppAssets.groupIcon, title: AppStrings.supervisors),
    _GridItem(icon: AppAssets.busHomeIcon, title: AppStrings.bus),
    _GridItem(icon: AppAssets.driverHomeIcon, title: AppStrings.drivers),
    _GridItem(icon: AppAssets.parentsIcon, title: AppStrings.parents),
    _GridItem(
        icon: AppAssets.locationIcon, title: AppStrings.requestsChangeAddress),
    _GridItem(
        icon: AppAssets.previousTripsIcon, title: AppStrings.previousTrips),
  ];

  void _handleGridItemTap(BuildContext context, int index) {
    switch (index) {
      case 0:
        context.read<StudentCubit>().getStudent(page: 1, isFirst: true);
        Navigator.pushNamed(context, PathRouteName.student);
        break;
      case 1:
        context
            .read<SupervisorCubit>()
            .getSupervisor(pageNumber: 1, isFirst: true);
        Navigator.pushNamed(context, PathRouteName.allSupervisor);
        break;
      case 2:
        context.read<BusesCubit>().getBuses(pageNumber: 1, isFirst: true);
        Navigator.pushNamed(context, PathRouteName.allBus);
        break;
      case 3:
        context.read<DriverCubit>().getDriver(pageNumber: 1, isFirst: true);
        Navigator.pushNamed(context, PathRouteName.allDriver);
        break;
      case 4:
        context.read<ParentCubit>().getParent(pageNumber: 1, isFirst: true);
        Navigator.pushNamed(context, PathRouteName.parent);
        break;
      case 5:
        Navigator.pushNamed(context, AllRequestScreen.routeName);
        break;
      case 6:
        Navigator.pushNamed(context, PathRouteName.previousTripsScreen);
        break;
    }
  }

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: _Sizes.horizontalPadding),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(height: _Sizes.topSpacing),
          LayoutBuilder(
            builder: (context, constraints) {
              return GridView.count(
                shrinkWrap: true,
                physics: const NeverScrollableScrollPhysics(),
                crossAxisCount: 2,
                mainAxisSpacing: _Sizes.gridSpacing,
                crossAxisSpacing: _Sizes.gridSpacing,
                childAspectRatio: _Sizes.gridAspectRatio,
                children: _gridItems.asMap().entries.map((entry) {
                  return _buildGridItem(entry.key, entry.value);
                }).toList(),
              );
            },
          ),
        ],
      ),
    );
  }

  Widget _buildGridItem(int index, _GridItem item) {
    return CustomContainerHW(
      onTap: () {
        if (subscriptionStatus != null && subscriptionStatus == false) {
          debugPrint('subscriptionStatus: $subscriptionStatus');
          showSubscriptionAlert(context);
        } else {
          _handleGridItemTap(context, index);
        }
      },
      icon: SvgPicture.asset(
        item.icon,
        height: _Sizes.iconSize,
        colorFilter: ColorFilter.mode(
          Color.fromRGBO(255, 255, 255, _Sizes.iconOpacity),
          BlendMode.srcIn,
        ),
      ),
      title: item.title.tr(),
      isAnimated: true, // Enable animation for better feedback
    );
  }
}

// Separate class for grid items
class _GridItem {
  final String icon;
  final String title;

  _GridItem({required this.icon, required this.title});
}

// Standardized sizes class
class _GridSizes {
  // Responsive sizes based on screen size
  final double horizontalPadding = 16.w;
  final double topSpacing = 12.h;
  final double gridSpacing = 12.w;
  final double gridAspectRatio =
      1.8; // Increased from 1.4 to make cards smaller
  final double iconSize = 18.h; // Reduced icon size to match smaller card

  // Constants
  final double iconOpacity = 0.9;
}
