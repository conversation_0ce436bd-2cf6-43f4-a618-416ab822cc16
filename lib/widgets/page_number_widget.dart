import 'package:bus/bloc/absences_cubit/absences_cubit.dart';
import 'package:bus/bloc/buses_cubit/buses_cubit.dart';
import 'package:bus/bloc/change_address_cubit/change_address_cubit.dart';
import 'package:bus/bloc/driver_cubit/driver_cubit.dart';
import 'package:bus/bloc/supervisor_cubit/supervisor_cubit.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';

import '../bloc/student_bus_cubit/student_bus_cubit.dart';
import '../config/theme_colors.dart';
import '../utils/assets_utils.dart';

class PageNumberWidget extends StatefulWidget {
  final int? lastPage;
  final int? currentPage;
  final String? type;
  final String? status;
  const PageNumberWidget({
    Key? key,
     this.lastPage,
     this.currentPage,
     this.type,
    this.status,
  }) : super(key: key);

  @override
  State<PageNumberWidget> createState() => _PageNumberWidgetState();
}

class _PageNumberWidgetState extends State<PageNumberWidget> {
  late int selectedPage;

  @override
  void initState() {
    super.initState();
    selectedPage = widget.currentPage!;
  }

  // Update selectedPage when the parent widget (currentPage) changes.
  @override
  void didUpdateWidget(covariant PageNumberWidget oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (widget.currentPage != oldWidget.currentPage) {
      setState(() {
        selectedPage = widget.currentPage!;
      });
    }
  }

  /// Helper function to change page and trigger the appropriate cubit action.
  void _changePage(int newPage) {
    if (newPage < 1 || newPage > widget.lastPage!) return;
    setState(() {
      selectedPage = newPage;
    });

    // Call the appropriate cubit based on the type.
    switch (widget.type) {
      case "student":
        context.read<StudentBusCubit>().getStudent(pageNumber: newPage);
        break;
      case "ChangeAddressrequest":
        context.read<CAdreessCubit>().getCAdreess(
          pageNumber: newPage,
          status: widget.status,
        );
        break;
      case "driver":
        context.read<DriverCubit>().getDriver(pageNumber: newPage);
        break;
      case "buses":
        context.read<BusesCubit>().getBuses(pageNumber: newPage);
        break;
      case "absences":
        context.read<AbsencesCubit>().getAbsences(pageNumber: newPage);
        break;
      case "supervisor":
        context.read<SupervisorCubit>().getSupervisor(pageNumber: newPage);
        break;
      case "Absencesrequest":
        context.read<AbsencesCubit>().getAbsences(pageNumber: newPage);
        break;
      case "notification":
      // Add your notification cubit call if needed.
        break;
      default:
        break;
    }
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(8.0),
      margin: const EdgeInsets.symmetric(horizontal: 18.0),
      height: 50.h,
      alignment: Alignment.center,
      decoration: BoxDecoration(
        color: TColor.white,
        borderRadius: BorderRadius.circular(5.0),
        boxShadow: [
          BoxShadow(
            color: TColor.black.withOpacity(0.1),
            spreadRadius: 2,
            blurRadius: 1,
            offset: const Offset(0, 1.5),
          ),
        ],
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          // Left arrow: decreases page number.
          IconButton(
            onPressed: () {
              if (selectedPage > 1) {
                _changePage(selectedPage - 1);
              }
            },
            icon: SvgPicture.asset(
              AppAssets.forwardArrow,
              colorFilter: const ColorFilter.mode(TColor.tabColors, BlendMode.srcIn),
              width: 25,
              height: 25,
            ),
          ),
          SizedBox(width: 5.w),
          // Horizontal list of page numbers.
          Expanded(
            child: ListView.separated(
              scrollDirection: Axis.horizontal,
              physics: const BouncingScrollPhysics(),
              shrinkWrap: true,
              itemCount: widget.lastPage!,
              separatorBuilder: (_, index) => SizedBox(width: 5.w),
              itemBuilder: (context, index) {
                final page = index + 1;
                final bool isCurrent = page == selectedPage;
                return InkWell(
                  onTap: () {
                    if (!isCurrent) {
                      _changePage(page);
                    }
                  },
                  child: Container(
                    padding: const EdgeInsets.symmetric(horizontal: 8.0, vertical: 4.0),
                    decoration: BoxDecoration(
                      color: isCurrent ? TColor.mainColor : TColor.white,
                      borderRadius: BorderRadius.circular(5.0),
                      border: Border.all(color: TColor.tabColors),
                    ),
                    child: Text(
                      '$page',
                      style: TextStyle(
                        color: isCurrent ? TColor.white : TColor.tabColors,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                );
              },
            ),
          ),
          SizedBox(width: 5.w),
          // Right arrow: increases page number.
          IconButton(
            onPressed: () {
              if (selectedPage < widget.lastPage!) {
                _changePage(selectedPage + 1);
              }
            },
            icon: SvgPicture.asset(
              AppAssets.arrowBack,
              colorFilter: const ColorFilter.mode(TColor.tabColors, BlendMode.srcIn),
              width: 25,
              height: 25,
            ),
          ),
        ],
      ),
    );
  }
}
