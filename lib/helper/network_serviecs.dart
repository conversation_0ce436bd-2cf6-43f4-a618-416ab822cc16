import 'package:bus/helper/cache_helper.dart';
import 'package:dio/dio.dart';
import 'package:flutter/material.dart';
import '../../config/global_variable.dart';
import '../config/config_base.dart';

/// A service class responsible for handling network requests.
/// Follows the Single Responsibility Principle from SOLID.
class NetworkService {
  final Dio _dio;

  NetworkService({Dio? dio}) : _dio = dio ?? Dio() {
    _initializeInterceptors();
  }

  /// Configures Dio with a logging interceptor.
  void _initializeInterceptors() {
    _dio.interceptors.add(LogInterceptor(
      request: true,
      requestHeader: true,
      requestBody: true,
      responseHeader: true,
      responseBody: true,
      logPrint: (log) => debugPrint(log.toString()),
    ));
  }

  /// Performs a GET request.
  Future<Response> get({
    required String url,
    Map<String, String>? headers,
    Map<String, dynamic>? queryParameters,
    bool isAuth = false,
  }) async {
    try {
      _dio.options.baseUrl = ConfigBase.baseUrl;
      final options = _buildOptions(headers, isAuth);
      final response = await _dio.get(url,
          options: options, queryParameters: queryParameters);
      return _validateResponse(response);
    } on DioException catch (e) {
      return _handleError(e);
    }
  }

  /// Performs a POST request.
  Future<Response> post({
    required String url,
    Map<String, String>? headers,
    Object? body,
    Map<String, dynamic>? queryParameters,
    bool isAuth = false,
    RequestEncoder? encoding,
  }) async {
    try {
      _dio.options.baseUrl = ConfigBase.baseUrl;
      final options = _buildOptions(headers, isAuth, encoding);
      final response = await _dio.post(url,
          data: body, options: options, queryParameters: queryParameters);
      return _validateResponse(response);
    } on DioException catch (e) {
      return _handleError(e);
    }
  }

  /// Performs a DELETE request.
  Future<Response> delete({
    required String url,
    Map<String, String>? headers,
    Map<String, dynamic>? body,
    Map<String, dynamic>? queryParameters,
    bool isAuth = false,
    RequestEncoder? encoding,
  }) async {
    try {
      _dio.options.baseUrl = ConfigBase.baseUrl;
      final options = _buildOptions(headers, isAuth, encoding);
      final response = await _dio.delete(url,
          data: body, options: options, queryParameters: queryParameters);
      return _validateResponse(response);
    } on DioException catch (e) {
      return _handleError(e);
    }
  }

  /// Performs a PATCH request.
  Future<Response> patch({
    required String url,
    Map<String, String>? headers,
    Object? body,
    Map<String, dynamic>? queryParameters,
    bool isAuth = false,
    RequestEncoder? encoding,
  }) async {
    try {
      _dio.options.baseUrl = ConfigBase.baseUrl;
      final options = _buildOptions(headers, isAuth, encoding);
      final response = await _dio.patch(url,
          data: body, options: options, queryParameters: queryParameters);
      return _validateResponse(response);
    } on DioException catch (e) {
      return _handleError(e);
    }
  }

  /// Downloads a file to the specified path.
  Future<Response> download({
    required String url,
    required String savePath,
    Map<String, String>? headers,
    Map<String, dynamic>? queryParameters,
    bool isAuth = false,
  }) async {
    try {
      _dio.options.baseUrl = ConfigBase.baseUrl;
      final options = _buildOptions(headers, isAuth);
      final response = await _dio.download(url, savePath,
          options: options, queryParameters: queryParameters);
      return _validateResponse(response);
    } on DioException catch (e) {
      return _handleError(e);
    }
  }

  /// Builds Dio options, including headers and optional encoding.
  Options _buildOptions(Map<String, String>? headers, bool isAuth,
      [RequestEncoder? encoding]) {
    final defaultHeaders = {
      'ln': CacheHelper.getString('lang') == 'ar' ? 'ar' : 'en'
    };
    final authHeaders = {
      'Authorization': "Bearer $token",
      'ln': CacheHelper.getString('lang') == 'ar' ? 'ar' : 'en'
    };

    return Options(
      headers: isAuth ? authHeaders : {...defaultHeaders, ...?headers},
      requestEncoder: encoding,
    );
  }

  /// Validates the response and ensures it is not null.
  Response _validateResponse(Response? response) {
    if (response != null) {
      return response;
    } else {
      throw DioException(
        requestOptions: RequestOptions(path: ''),
        response: Response(
          requestOptions: RequestOptions(path: ''),
          statusCode: 500,
          statusMessage: 'Unknown error',
        ),
      );
    }
  }

  /// Handles Dio exceptions.
  Response _handleError(DioException e) {
    if (e.response != null) {
      return e.response!;
    } else {
      throw DioException(
        requestOptions: e.requestOptions,
        error: e.error,
        response: e.response,
        type: e.type,
      );
    }
  }

  Dio get dio => _dio;
}
