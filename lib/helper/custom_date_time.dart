import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';

class CustomDateTime {
  // Convert server time string to local time and format as HH:MM
  static String convertToLocalTime(String? timeString) {
    if (timeString == null || timeString.isEmpty) {
      return "00:00";
    }

    try {
      // If it's already in HH:MM format, try to parse it as a time
      if (timeString.contains(":") && timeString.length <= 5) {
        // For simple HH:MM format, we need to add a date to parse it
        // Use today's date as a base
        final now = DateTime.now();
        final dateStr =
            "${now.year}-${now.month.toString().padLeft(2, '0')}-${now.day.toString().padLeft(2, '0')}";

        // Try to parse as a datetime
        try {
          final fullTimeString = "$dateStr $timeString:00"; // Add seconds
          // Just parse to validate the format, but we don't need the result
          DateTime.parse(fullTimeString);

          // No need to convert to local time since we're using today's date
          // Just return the original time
          return timeString;
        } catch (e) {
          debugPrint("Error parsing HH:MM format: $e");
          return timeString;
        }
      }

      DateTime serverTime;

      // Handle different datetime formats
      if (timeString.contains("T") && timeString.contains("Z")) {
        // ISO format with T and Z: "2025-05-01T16:56:47.000000Z"
        serverTime = DateTime.parse(timeString);
      } else if (timeString.contains("-") && timeString.contains(":")) {
        // Format without T: "2025-05-01 17:56:47"
        // Replace space with T to make it ISO compatible
        String isoFormat = timeString.replaceFirst(" ", "T");
        if (!isoFormat.endsWith("Z")) {
          isoFormat += "Z"; // Add Z if missing to indicate UTC
        }
        serverTime = DateTime.parse(isoFormat);
      } else {
        // Try standard parsing as a last resort
        serverTime = DateTime.parse(timeString);
      }

      // Convert to local time
      DateTime localTime = serverTime.toLocal();

      // Format as HH:MM
      return "${localTime.hour.toString().padLeft(2, '0')}:${localTime.minute.toString().padLeft(2, '0')}";
    } catch (e) {
      debugPrint(
          "Error converting time to local: $e for timeString: $timeString");

      // Try to extract time if it's in a format like "2025-05-01 17:56:47"
      if (timeString.contains(" ") && timeString.contains(":")) {
        try {
          // Split by space and get the time part
          String timePart = timeString.split(" ")[1];
          // Extract hours and minutes
          List<String> timeComponents = timePart.split(":");
          if (timeComponents.length >= 2) {
            int hours = int.parse(timeComponents[0]);
            int minutes = int.parse(timeComponents[1]);

            // Create a DateTime object for today with this time
            final now = DateTime.now();
            final dateTime =
                DateTime(now.year, now.month, now.day, hours, minutes);

            // Convert to local time
            final localTime = dateTime.toLocal();

            // Format as HH:MM
            return "${localTime.hour.toString().padLeft(2, '0')}:${localTime.minute.toString().padLeft(2, '0')}";
          }
        } catch (e2) {
          debugPrint("Error extracting time: $e2");
        }
      }

      return timeString;
    }
  }

  // Format time with AM/PM
  static String formatTimeWithAmPm(String? timeString) {
    if (timeString == null || timeString.isEmpty) {
      return "00:00 AM";
    }

    try {
      // Check if it's already in HH:MM format
      if (timeString.contains(":") && timeString.length <= 5) {
        // Parse hours and minutes
        List<String> parts = timeString.split(":");
        int hours = int.parse(parts[0]);
        int minutes = int.parse(parts[1]);

        // Determine AM/PM
        String period = hours >= 12 ? "PM" : "AM";

        // Convert to 12-hour format
        hours = hours % 12;
        hours = hours == 0 ? 12 : hours;

        return "${hours.toString().padLeft(2, '0')}:${minutes.toString().padLeft(2, '0')} $period";
      }

      // Try to parse as full datetime
      try {
        DateTime dateTime;

        // Handle different datetime formats
        if (timeString.contains("T") && timeString.contains("Z")) {
          // ISO format with T and Z: "2025-05-01T16:56:47.000000Z"
          dateTime = DateTime.parse(timeString);
        } else if (timeString.contains(" ") && timeString.contains(":")) {
          // Format without T: "2025-05-01 17:56:47"
          // Replace space with T to make it ISO compatible
          String isoFormat = timeString.replaceFirst(" ", "T");
          if (!isoFormat.endsWith("Z")) {
            isoFormat += "Z"; // Add Z if missing to indicate UTC
          }
          dateTime = DateTime.parse(isoFormat);
        } else {
          // Try standard parsing as a last resort
          dateTime = DateTime.parse(timeString);
        }

        final DateFormat formatter = DateFormat('hh:mm a');
        return formatter.format(dateTime.toLocal());
      } catch (e) {
        debugPrint(
            "Error parsing datetime for AM/PM formatting: $e for timeString: $timeString");

        // Try different extraction methods based on format
        if (timeString.contains("T") && timeString.contains(":")) {
          // Extract time part from ISO format like "2025-05-01T17:23:53.000000Z"
          String timePart = timeString.split("T")[1];
          // Extract hours and minutes
          List<String> timeComponents = timePart.split(":");
          if (timeComponents.length >= 2) {
            int hours = int.parse(timeComponents[0]);
            int minutes = int.parse(timeComponents[1]);

            // Determine AM/PM
            String period = hours >= 12 ? "PM" : "AM";

            // Convert to 12-hour format
            hours = hours % 12;
            hours = hours == 0 ? 12 : hours;

            return "${hours.toString().padLeft(2, '0')}:${minutes.toString().padLeft(2, '0')} $period";
          }
        } else if (timeString.contains(" ") && timeString.contains(":")) {
          // Extract time part from format like "2025-05-01 17:56:47"
          String timePart = timeString.split(" ")[1];
          // Extract hours and minutes
          List<String> timeComponents = timePart.split(":");
          if (timeComponents.length >= 2) {
            int hours = int.parse(timeComponents[0]);
            int minutes = int.parse(timeComponents[1]);

            // Determine AM/PM
            String period = hours >= 12 ? "PM" : "AM";

            // Convert to 12-hour format
            hours = hours % 12;
            hours = hours == 0 ? 12 : hours;

            return "${hours.toString().padLeft(2, '0')}:${minutes.toString().padLeft(2, '0')} $period";
          }
        }

        return timeString;
      }
    } catch (e) {
      debugPrint(
          "Error formatting time with AM/PM: $e for timeString: $timeString");
      return timeString;
    }
  }

  static String formatTimeDifference(
      BuildContext context, DateTime backendUtcTime) {
    // Convert backend UTC time to local time
    DateTime localTime = backendUtcTime.toLocal();

    // Get the current local time
    DateTime now = DateTime.now();

    // If the backend time is in the future, clamp it to now
    if (localTime.isAfter(now)) {
      localTime = now;
    }

    // Calculate the time difference
    Duration difference = now.difference(localTime);

    debugPrint("Backend Local Time: ${localTime.toString()}");
    debugPrint("Current Time: ${now.toString()}");
    debugPrint("Time Difference: ${difference.toString()}");

    // Format the time difference
    if (difference.inMinutes < 1) {
      return context.locale.toString() == "ar" ? "الآن" : "just now";
    } else if (difference.inMinutes < 60) {
      return context.locale.toString() == "ar"
          ? "منذ ${difference.inMinutes} دقيقة"
          : "since ${difference.inMinutes} minutes ago";
    } else if (difference.inHours < 24) {
      return context.locale.toString() == "ar"
          ? "منذ ${difference.inHours} ساعة"
          : "since ${difference.inHours} hours ago";
    } else if (difference.inDays < 30) {
      return context.locale.toString() == "ar"
          ? "منذ ${difference.inDays} يوم"
          : "since ${difference.inDays} days ago";
    } else if (difference.inDays < 365) {
      int months = (difference.inDays / 30).floor();
      return context.locale.toString() == "ar"
          ? "منذ $months شهر"
          : "since $months months ago";
    } else {
      int years = (difference.inDays / 365).floor();
      return context.locale.toString() == "ar"
          ? "منذ $years سنة"
          : "since $years years ago";
    }
  }

  static String formatDate(DateTime? date) {
    if (date == null) {
      return 'غير محدد';
    } else {
      final DateFormat formatter = DateFormat('yyyy-MM-dd');
      final String formattedDate = formatter.format(date);
      return formattedDate;
    }
  }

  static String formatTime(DateTime? time) {
    if (time == null) {
      return 'غير محدد';
    } else {
      final DateFormat formatter = DateFormat('hh:mm a');
      final String formattedDate = formatter.format(time);
      return formattedDate;
    }
  }

  static String formatDateTime(DateTime? dateTime) {
    if (dateTime == null) {
      return 'غير محدد';
    } else {
      final DateFormat formatter = DateFormat('yyyy-MM-dd "at" hh:mm a');
      final String formattedDate = formatter.format(dateTime);
      return formattedDate;
    }
  }
}
