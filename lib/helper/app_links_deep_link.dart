import 'dart:async';
import 'package:app_links/app_links.dart';
import 'package:flutter/cupertino.dart';

class AppLinksDeepLink {

  /// first three code create singleTone class take one object in code in my app

  /// make private constructor using to make class single tone .
  AppLinksDeepLink._();

  static final AppLinksDeepLink _appLinksC = AppLinksDeepLink._();

  /// make default constructor using and make default constructor type factory
  factory AppLinksDeepLink (){
    // return  AppLinksDeepLink._();
    return _appLinksC;
  }



  final AppLinks _appLinks = AppLinks();
  StreamSubscription<Uri>? _linkSubscription;

  Future<void> initDeepLinks() async {
    // Check initial link if app was in cold state (terminated)
    final appLink = await _appLinks.getInitialLink();
    if (appLink != null) {
      var uri = Uri.parse(appLink.toString());
      print(uri);
      print(' here you can redirect from url as per your need ');
    }

    // Handle link when app is in warm state (front or background)
    _linkSubscription = _appLinks.uriLinkStream.listen(
      (uriValue) {
        print(' you will listen any url updates ');
        print(' here you can redirect from url as per your need ');
      },
      onError: (err) {
        debugPrint('====>>> error : $err');
      },
      onDone: () {
        _linkSubscription?.cancel();
      },
    );
  }
}


