import 'package:bus/bloc/profile_cubit/profile_states.dart';
import 'package:bus/data/models/user_models/user_models.dart';
import 'package:bus/data/repo/user_repo.dart';
import 'package:bus/helper/data_state.dart';
import 'package:bus/helper/response_state.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

class ProfileCubit extends Cubit<ProfileStates> {
  final _profileRepo = UserRepo();
  ProfileCubit() : super(const ProfileStates());
  //  UserModel? userModel;

  Future<void> getProfile() async { 
    // userModel = null;
    emit(
      state.copyWith(rStates: ResponseState.loading),
    );
    DataState<UserModel> response = await _profileRepo.repo();

    if (response is DataSuccess) {
      // userModel = response.data;
      emit(
        state.copyWith(
          userModel:response.data,
          rStates: ResponseState.success,
        ),
      );
    } else {

      emit(
        state.copyWith(
          rStates: ResponseState.failure,
        ),
      );
    }
  }
}
