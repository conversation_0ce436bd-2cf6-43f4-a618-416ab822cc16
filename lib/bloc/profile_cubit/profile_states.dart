import 'package:bus/data/models/user_models/user_models.dart';
import 'package:bus/helper/response_state.dart';
import 'package:equatable/equatable.dart';

class ProfileStates extends Equatable {
  final UserModel? userModel;
  final ResponseState rStates;

  const ProfileStates({
    this.userModel,
    this.rStates = ResponseState.init,
  });

  ProfileStates copyWith({
    UserModel? userModel,
    ResponseState? rStates,
  }) {
    return ProfileStates(
      userModel: userModel ?? this.userModel,
      rStates: rStates ?? this.rStates,
    );
  }

  @override
  List<Object?> get props => [
        userModel,
        rStates,
      ];
}
