import 'package:bus/bloc/add_bus_cubit/add_bus_states.dart';
import 'package:bus/data/repo/add_bus_repo.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

class AddBusCubit extends Cubit<AddBusStates> {
  final _addBusRepo = AddBusRepo();
  AddBusCubit() : super(AddBusInitialStates());

  Future<void> addBus({
    String? name,
    String? notes,
    String? car_number,
  }) async {
    emit(AddBusLoadingStates());
    try {
      final response = await _addBusRepo.repo(
        name: name,
        notes: notes,
        car_number: car_number,
      );
      if (response.errors == false) {
        emit(
          AddBusSuccessStates(addBusModels: response),
        );
      } else {
        emit(AddBusErrorStates(error: response.errorMessages!.name![0]));
      }
    } catch (e, s) {
      debugPrint("catch error $s");
      debugPrint("catch error $e");
      emit(AddBusErrorStates(error: e.toString()));
    }
  }
}
