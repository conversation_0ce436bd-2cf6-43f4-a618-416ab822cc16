import 'package:bus/data/models/add_bus_models/add_bus_models.dart';


abstract class AddBusStates {}

class AddBusInitialStates extends AddBusStates {}

class AddBusLoadingStates extends AddBusStates {}

class AddBusSuccessStates extends AddBusStates {
  final AddBusModels? addBusModels;
  AddBusSuccessStates({
    this.addBusModels,
  });
}

class AddBusErrorStates extends AddBusStates {
  final String? error;
  AddBusErrorStates({
    this.error,
  });
}
