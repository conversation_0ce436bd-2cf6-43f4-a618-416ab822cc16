import 'package:bus/data/models/add_student_file_models/add_student_file_models.dart';

abstract class AddStudentFileStates {}

class AddStudentFileInitialStates extends AddStudentFileStates {}

class AddStudentFileLoadingStates extends AddStudentFileStates {}

class AddStudentFileSuccessStates extends AddStudentFileStates {
  final AddStudentFileModels? addStudentFileModels;
  AddStudentFileSuccessStates({this.addStudentFileModels});
}

class AddStudentFileErrorStates extends AddStudentFileStates {
  final String? error;
  AddStudentFileErrorStates({this.error});
}
