import 'dart:io';

import 'package:bus/bloc/add_student_file_cubit/add_student_file_states.dart';
import 'package:bus/data/models/ads_model.dart';
import 'package:bus/data/repo/add_student_file_repo.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

class AddStudentFileCubit extends Cubit<AddStudentFileStates> {
  AddStudentFileCubit() : super(AddStudentFileInitialStates());
  static AddStudentFileCubit get(context) => BlocProvider.of(context);
  final _addStudentFileRepo = AddStudentFileRepo();

  AdsModel? ads;

  Future<void> addFileStudent({
    int? gradeId,
    File? uploadFile,
    String? file,
    int? classId,
  }) async {
    emit(AddStudentFileLoadingStates());
    try {
      final response = await _addStudentFileRepo.repo(
        gradeId: gradeId,
        uploadFile: uploadFile,
        file: file,
        classId: classId,
      );
      if ( response.status == true) {
        emit(AddStudentFileSuccessStates(addStudentFileModels: response));
      } else {
        emit(AddStudentFileErrorStates(error: response.errors?.messages.toString()));
      }
    } catch (e, s) {
      debugPrint("catch error $s");
      debugPrint("catch error $e");
      emit(AddStudentFileErrorStates(error: e.toString()));
    }
  }
}
