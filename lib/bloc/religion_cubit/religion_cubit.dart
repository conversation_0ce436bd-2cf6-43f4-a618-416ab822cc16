import 'package:bus/bloc/religion_cubit/religion_states.dart';
import 'package:bus/data/repo/religion_repo.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

class ReligionCubit extends Cubit<ReligionStates> {
  final _religionRepo = ReligionRepo();
  ReligionCubit() : super(ReligionInitialStates());

  Future<void> getReligion() async {
    emit(ReligionLoadingStates());
    try {
      final response = await _religionRepo.repo();
      if (response.status == true) {
        emit(ReligionSuccessStates(religionModels: response));
      } else {
        emit(ReligionErrorStates(error: response.message));
      }
    } catch (e, s) {
      debugPrint("catch error $s");
      debugPrint("catch error $e");
      emit(ReligionErrorStates(error: e.toString()));
    }
  }
}
