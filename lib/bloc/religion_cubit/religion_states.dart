import 'package:bus/data/models/religion_models/religion_models.dart';

abstract class ReligionStates {}

class ReligionInitialStates extends ReligionStates {}

class ReligionLoadingStates extends ReligionStates {}

class ReligionSuccessStates extends ReligionStates {
  final ReligionModels? religionModels;
  ReligionSuccessStates({
    this.religionModels,
  });
}

class ReligionErrorStates extends ReligionStates {
  final String? error;
  ReligionErrorStates({
    this.error,
  });
}
