import 'package:bus/bloc/send_code_verification_cubit/send_code_verification_states.dart';
import 'package:bus/data/repo/send_code_verification_repo.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:logger/logger.dart';

class SendCodeVerificationCubit extends Cubit<SendCodeVerificationStates> {
  final _sendCodeVerificationRepo = SendCodeVerificationRepo();
  SendCodeVerificationCubit() : super(SendCodeVerificationInitialStates());

  Future<void> sendCodeVerification({
    String? smsCode,
  }) async {
    emit(SendCodeVerificationLoadingStates());
    try {
      final response = await _sendCodeVerificationRepo.repo(
        smsCode: smsCode,
        
      );
      Logger().e(response);
      if (response.status == true) {
        emit(SendCodeVerificationSuccessStates(
            sendCodeVerificationModels: response));
      } else {
        emit(SendCodeVerificationErrorStates(error: response.message));
      }
    } catch (e, s) {
      debugPrint("catch error $s");
      debugPrint("catch error $e");
      emit(SendCodeVerificationErrorStates(error: e.toString()));
    }
  }
}
