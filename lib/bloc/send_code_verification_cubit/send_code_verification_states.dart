import 'package:bus/data/models/send_verificiation_code_models/send_verification_code_models.dart';

abstract class SendCodeVerificationStates {}

class SendCodeVerificationInitialStates extends SendCodeVerificationStates {}

class SendCodeVerificationLoadingStates extends SendCodeVerificationStates {}

class SendCodeVerificationSuccessStates extends SendCodeVerificationStates {
  final SendCodeVerificationModels? sendCodeVerificationModels;
  SendCodeVerificationSuccessStates({
    this.sendCodeVerificationModels,
  });
}

class SendCodeVerificationErrorStates extends SendCodeVerificationStates {
  final String? error;
  SendCodeVerificationErrorStates({
    this.error,
  });
}
