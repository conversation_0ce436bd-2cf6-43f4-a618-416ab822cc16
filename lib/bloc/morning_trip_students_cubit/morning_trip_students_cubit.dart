import 'package:bus/bloc/morning_trip_students_cubit/morning_trip_students_states.dart';
import 'package:bus/data/models/trip_models/morning_trip_students_model.dart';
import 'package:bus/data/repo/morning_trip_students_repo.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

class MorningTripStudentsCubit extends Cubit<MorningTripStudentsState> {
  MorningTripStudentsCubit() : super(MorningTripStudentsInitialState());
  final _morningTripStudentsRepo = MorningTripStudentsRepo();

  static MorningTripStudentsCubit get(context) => BlocProvider.of(context);

  MorningTripStudentsResponse? studentsResponse;
  List<MorningTripStudent> presentStudents = [];

  /// Fetches students present on the bus for a morning trip
  Future<void> getPresentStudents({required String busId}) async {
    emit(MorningTripStudentsLoadingState());
    try {
      final response = await _morningTripStudentsRepo.getPresentStudents(busId: busId);
      
      if (response.errors == false) {
        studentsResponse = response;
        presentStudents = response.presentOnBus ?? [];
        emit(MorningTripStudentsSuccessState(response: response));
      } else {
        emit(MorningTripStudentsErrorState(error: response.message ?? "Unknown error"));
      }
    } catch (e, s) {
      debugPrint("Error in cubit: $e");
      debugPrint("Stack trace: $s");
      emit(MorningTripStudentsErrorState(error: e.toString()));
    }
  }
}
