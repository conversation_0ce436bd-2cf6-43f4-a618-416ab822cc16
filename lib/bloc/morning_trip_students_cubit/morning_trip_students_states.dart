import 'package:bus/data/models/trip_models/morning_trip_students_model.dart';
import 'package:equatable/equatable.dart';

abstract class MorningTripStudentsState extends Equatable {
  const MorningTripStudentsState();

  @override
  List<Object?> get props => [];
}

class MorningTripStudentsInitialState extends MorningTripStudentsState {}

class MorningTripStudentsLoadingState extends MorningTripStudentsState {}

class MorningTripStudentsSuccessState extends MorningTripStudentsState {
  final MorningTripStudentsResponse response;

  const MorningTripStudentsSuccessState({required this.response});

  @override
  List<Object?> get props => [response];
}

class MorningTripStudentsErrorState extends MorningTripStudentsState {
  final String error;

  const MorningTripStudentsErrorState({required this.error});

  @override
  List<Object?> get props => [error];
}
