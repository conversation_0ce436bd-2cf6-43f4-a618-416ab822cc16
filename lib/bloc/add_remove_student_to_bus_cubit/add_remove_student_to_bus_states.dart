abstract class AddRemoveStudentToBusStates {}

class AddRemoveStudentToBusInitialStates extends AddRemoveStudentToBusStates {}

class AddRemoveStudentToBusLoadingStates extends AddRemoveStudentToBusStates {}

class AddRemoveStudentToBusSuccessStates extends AddRemoveStudentToBusStates {
  final bool? checkSuccess;
  AddRemoveStudentToBusSuccessStates({
    this.checkSuccess,
  });
}

class AddRemoveStudentToBusErrorStates extends AddRemoveStudentToBusStates {
  final bool? error;
  AddRemoveStudentToBusErrorStates({
    this.error,
  });
}
