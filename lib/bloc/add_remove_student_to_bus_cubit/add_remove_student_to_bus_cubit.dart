import 'package:bus/bloc/add_remove_student_to_bus_cubit/add_remove_student_to_bus_states.dart';
import 'package:bus/data/repo/student_repo.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

class AddRemoveStudentToBusCubit extends Cubit<AddRemoveStudentToBusStates> {
  final _addOrRemoveStudentsToBus = StudentRepo();
  AddRemoveStudentToBusCubit() : super(AddRemoveStudentToBusInitialStates());

  Future<void> addStudents({
    required List<String> studentIds,
    String? busId,
  }) async {
    emit(AddRemoveStudentToBusLoadingStates());
    try {
      final response = await _addOrRemoveStudentsToBus.addOrRemoveStudentsToBus(
        studentIds: studentIds,
        busId: busId,
      );
      // Logger().e(response);
      if (response == true) {
        emit(
          AddRemoveStudentToBusSuccessStates(checkSuccess: response),
        );
      } else {
        emit(AddRemoveStudentToBusErrorStates(error: response));
      }
    } catch (e, s) {
      debugPrint("catch error $s");
      debugPrint("catch error $e");
      emit(AddRemoveStudentToBusErrorStates(error: false));
    }
  }
}
