import 'package:bus/data/models/temporary_address_models/temporary_address_models.dart';
import 'package:equatable/equatable.dart';

abstract class TemporaryAddressStates extends Equatable {
  const TemporaryAddressStates();

  @override
  List<Object?> get props => [];
}

class TemporaryAddressInitialState extends TemporaryAddressStates {}

class TemporaryAddressLoadingState extends TemporaryAddressStates {}

class TemporaryAddressSuccessState extends TemporaryAddressStates {
  final List<TemporaryAddressItem>? temporaryAddressItems;
  final int currentPage;
  final int lastPage;

  const TemporaryAddressSuccessState({
    this.temporaryAddressItems,
    required this.currentPage,
    required this.lastPage,
  });

  @override
  List<Object?> get props => [temporaryAddressItems, currentPage, lastPage];
}

class TemporaryAddressErrorState extends TemporaryAddressStates {
  final String error;

  const TemporaryAddressErrorState({required this.error});

  @override
  List<Object> get props => [error];
}
