import 'package:bus/bloc/temporary_address_cubit/temporary_address_states.dart';
import 'package:bus/data/models/temporary_address_models/temporary_address_models.dart';
import 'package:bus/data/repository/temporary_address_repository.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

class TemporaryAddressCubit extends Cubit<TemporaryAddressStates> {
  TemporaryAddressCubit() : super(TemporaryAddressInitialState());

  static TemporaryAddressCubit get(context) => BlocProvider.of(context);

  List<TemporaryAddressItem>? temporaryAddressItems = [];
  int currentPage = 1;
  int lastPage = 1;

  Future<void> getTemporaryAddresses({
    String? status,
    int page = 1,
  }) async {
    emit(TemporaryAddressLoadingState());

    try {
      final result = await TemporaryAddressRepository.getTemporaryAddresses(
        status: status,
        page: page,
      );

      if (result.status == true) {
        temporaryAddressItems = result.data?.data;
        currentPage = result.data?.currentPage ?? 1;
        lastPage = result.data?.lastPage ?? 1;

        emit(TemporaryAddressSuccessState(
          temporaryAddressItems: temporaryAddressItems,
          currentPage: currentPage,
          lastPage: lastPage,
        ));
      } else {
        emit(const TemporaryAddressErrorState(error: "Failed to load data"));
      }
    } catch (e) {
      emit(TemporaryAddressErrorState(error: e.toString()));
    }
  }
}
