import 'package:bus/data/models/update_profile_models/update_profile_models.dart';
import 'package:bus/helper/response_state.dart';
import 'package:equatable/equatable.dart';

class UpdateProfileStates extends Equatable {
  final UpdateProfileModels? updateSchoolsModels;
  final ResponseState rStates;

  const UpdateProfileStates({
    this.updateSchoolsModels,
    this.rStates = ResponseState.init,
  });

  UpdateProfileStates copyWith(
      {UpdateProfileModels? updateSchoolsModels, ResponseState? rStates}) {
    return UpdateProfileStates(
      updateSchoolsModels: updateSchoolsModels ?? this.updateSchoolsModels,
      rStates: rStates ?? this.rStates,
    );
  }

  @override
  List<Object?> get props => [
        updateSchoolsModels,
        rStates,
      ];
}
