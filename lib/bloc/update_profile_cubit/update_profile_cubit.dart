import 'dart:io';

import 'package:bus/bloc/update_profile_cubit/update_profile_states.dart';
import 'package:bus/data/models/update_profile_models/update_profile_models.dart';
import 'package:bus/data/repo/update_profile_repo.dart';
import 'package:bus/helper/data_state.dart';
import 'package:bus/helper/response_state.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:open_street_map_search_and_pick/open_street_map_search_and_pick.dart';

class UpdateProfileCubit extends Cubit<UpdateProfileStates> {
  final _updateProfileRepo = UpdateProfileRepo();
  UpdateProfileCubit() : super(const UpdateProfileStates());

  Future<void> updateProfile({
    String? email,
    String? phone,
    String? address,
    String? currentPassword,
    String? name,
    String? cityName,
    LatLong? position,
    File? image,
  }) async {
    emit(state.copyWith(rStates: ResponseState.loading));
    DataState<UpdateProfileModels> response = await _updateProfileRepo.repo(
        email: email,
        address: address,
        phone: phone,
        currentPassword: currentPassword,
        name: name,
        cityName: cityName,
        position: position,
        image: image);
    if (response is DataSuccess) {
      emit(
        state.copyWith(
            rStates: ResponseState.success, updateSchoolsModels: response.data),
      );
    } else {
      emit(
        state.copyWith(rStates: ResponseState.failure),
      );
    }
  }
}
