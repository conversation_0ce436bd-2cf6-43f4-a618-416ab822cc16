import 'package:bus/data/models/trip_models/morning_trip_absences_model.dart';
import 'package:equatable/equatable.dart';

abstract class MorningTripAbsencesState extends Equatable {
  const MorningTripAbsencesState();

  @override
  List<Object?> get props => [];
}

class MorningTripAbsencesInitialState extends MorningTripAbsencesState {}

class MorningTripAbsencesLoadingState extends MorningTripAbsencesState {}

class MorningTripAbsencesSuccessState extends MorningTripAbsencesState {
  final MorningTripAbsencesResponse response;

  const MorningTripAbsencesSuccessState({required this.response});

  @override
  List<Object?> get props => [response];
}

class MorningTripAbsencesErrorState extends MorningTripAbsencesState {
  final String error;

  const MorningTripAbsencesErrorState({required this.error});

  @override
  List<Object?> get props => [error];
}
