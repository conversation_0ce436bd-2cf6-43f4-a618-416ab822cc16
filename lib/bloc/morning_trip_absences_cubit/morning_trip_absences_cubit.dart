import 'package:bus/bloc/morning_trip_absences_cubit/morning_trip_absences_states.dart';
import 'package:bus/data/models/trip_models/morning_trip_absences_model.dart';
import 'package:bus/data/repo/morning_trip_absences_repo.dart';
import 'package:bus/translations/local_keys.g.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

class MorningTripAbsencesCubit extends Cubit<MorningTripAbsencesState> {
  MorningTripAbsencesCubit() : super(MorningTripAbsencesInitialState());
  final _morningTripAbsencesRepo = MorningTripAbsencesRepo();

  static MorningTripAbsencesCubit get(context) => BlocProvider.of(context);

  MorningTripAbsencesResponse? absencesResponse;
  List<MorningTripAbsentStudent> absentStudents = [];

  /// Fetches students absent from the morning trip
  Future<void> getAbsentStudents({required String busId}) async {
    emit(MorningTripAbsencesLoadingState());
    try {
      final response = await _morningTripAbsencesRepo.getAbsentStudents(busId: busId);

      if (response.errors == false) {
        absencesResponse = response;
        absentStudents = response.absences ?? [];
        emit(MorningTripAbsencesSuccessState(response: response));
      } else {
        emit(MorningTripAbsencesErrorState(error: response.message ?? AppStrings.unknownError.tr()));
      }
    } catch (e, s) {
      debugPrint("Error in cubit: $e");
      debugPrint("Stack trace: $s");
      emit(MorningTripAbsencesErrorState(error: e.toString()));
    }
  }
}
