import 'dart:io';

import 'package:bus/bloc/add_driver_cubit/add_driver_states.dart';
import 'package:bus/data/repo/add_driver_repo.dart';
import 'package:dio/dio.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:logger/logger.dart';

class AddDriverCubit extends Cubit<AddDriverStates> {
  final _addDriverRepo = AddDriverRepo();
  AddDriverCubit() : super(AddDriverInitialStates());

  Future<void> addDriver({
    String? name,
    MultipartFile? file__lol,
    String? fileName,
    String? username,
    int? genderId,
    int? religionId,
    int? typeBloodId,
    int? busId,
    String? birthDate,
    String? joiningDate,
    String? password,
    String? password_confirmation,
    String? address,
    String? cityName,
    String? phone,
    File? image,
    String? joinDateBirthS,
    String? dateBirthS,
  }) async {
    emit(AddDriverLoadingStates());
    try {
      final response = await _addDriverRepo.repo(
        name: name,
        image: image,
        password: password,
        password_confirmation: password_confirmation,
        phone: phone,
        cityName: cityName,
        address: address,
        genderId: genderId,
        username: username,
        birthDate: birthDate,
        typeBloodId: typeBloodId,
        busId: busId,
        religionId: religionId,
        joiningDate: joiningDate
      );
      if (response.errors == false) {
        Logger().w(response.errors);
        emit(
          AddDriverSuccessStates(addDriverModels: response),
        );
      } else {
        emit(AddDriverErrorStates(error: response.messages));
      }
    } catch (e, s) {
      debugPrint("catch error $s");
      debugPrint("catch error $e");
      emit(AddDriverErrorStates(error: e.toString()));
    }
  }
}
