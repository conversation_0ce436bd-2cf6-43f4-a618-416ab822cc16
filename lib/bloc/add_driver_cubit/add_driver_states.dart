import 'package:bus/data/models/add_driver_models/add_driver_models.dart';


abstract class AddDriverStates {}

class AddDriverInitialStates extends AddDriverStates {}

class AddDriverLoadingStates extends AddDriverStates {}

class AddDriverSuccessStates extends AddDriverStates {
  final AddDriverModels? addDriverModels;
  AddDriverSuccessStates({
    this.addDriverModels,
  });
}

class AddDriverErrorStates extends AddDriverStates {
  final String? error;
  AddDriverErrorStates({
    this.error,
  });
}
