// subscription_cubit.dart

import 'dart:io' show Platform;
import 'package:bus/config/config_base.dart';
import 'package:bus/config/global_variable.dart';
import 'package:bus/config/theme_colors.dart';
import 'package:bus/data/models/subscription/coupon_model.dart';
import 'package:bus/data/repo/subscription_repo.dart';
import 'package:bus/helper/network_serviecs.dart';
import 'package:bus/services/payment_service.dart';
import 'package:bus/translations/local_keys.g.dart';
import 'package:bus/views/custom_widgets/custom_text.dart';
import 'package:bus/views/screens/layout_screen/layout_screen.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:equatable/equatable.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter/material.dart' as material;
import 'package:in_app_purchase/in_app_purchase.dart';
import 'package:logger/logger.dart';

part 'subscription_state.dart';

bool isPro = PaymentService.instance.isProUser;

class SubscriptionCubit extends Cubit<SubscriptionState> {
  SubscriptionCubit() : super(SubscriptionInitial());

  static SubscriptionCubit get(context) => BlocProvider.of(context);
  final _subscriptionsRepo = SubscriptionsRepo();

  CouponModel? coupon;

  Future<void> couponSubscription(
      {required String code, required context}) async {
    emit(couponLoading());
    try {
      final response = await _subscriptionsRepo.couponSubscribe(code: code);
      material.debugPrint("is pro: $isPro");
      if (response.status == true) {
        material.debugPrint(response.toString());
        coupon = response;
        SnackBar snackBar = SnackBar(
          backgroundColor: TColor.greenSuccess,
          content: CustomText(
            text: AppStrings.subscribedSuccessfully.tr(),
            color: TColor.white,
            maxLine: 3,
          ),
        );

        // Set subscription status
        subscriptionStatus = true;
        material.debugPrint('couponSubscription: $subscriptionStatus');

        // Get user data first - this will load all the necessary profile data
        await getUserData();

        // Now navigate to home screen
        Navigator.pushAndRemoveUntil(
          context,
          MaterialPageRoute(
            builder: (context) => const LayoutScreen(),
          ),
          (_) => false
        );

        snackBarKey.currentState?.showSnackBar(snackBar);
        material.debugPrint("is pro: $isPro");
      } else {
        material.debugPrint("ErrorState: ${response.message}");
        SnackBar snackBar = SnackBar(
          backgroundColor: TColor.redAccent,
          content: CustomText(
            text: response.messages,
            color: TColor.white,
            maxLine: 3,
          ),
        );
        snackBarKey.currentState?.showSnackBar(snackBar);
      }
    } catch (e, stackTrace) {
      material.debugPrint("catch error at Absence repo: $e");
      material.debugPrint(stackTrace.toString());
      material.debugPrint("error $e");
    }
    emit(SubscriptionInitial());
  }

  Future<void> buySubscription(context) async {
    emit(SubscriptionLoading());
    try {
      final String productId = Platform.isIOS
          ? 'com.busaty.parent.subscription.yearly'
          : 'subscribtion_yearly';

      final ProductDetailsResponse response =
          await InAppPurchase.instance.queryProductDetails({productId});

      if (response.notFoundIDs.isNotEmpty) {
        material.debugPrint('Product not found: ${response.notFoundIDs}');
        emit(SubscriptionError('Product not found: ${response.notFoundIDs}'));
        SnackBar snackBar = const SnackBar(
          backgroundColor: TColor.redAccent,
          content: CustomText(
            text: "",
            color: TColor.white,
            maxLine: 3,
          ),
        );
        snackBarKey.currentState?.showSnackBar(snackBar);
        return;
      }

      if (response.productDetails.isEmpty) {
        material.debugPrint('No products found');
        emit(const SubscriptionError('No products found'));
        return;
      }

      final PurchaseParam purchaseParam = PurchaseParam(
        productDetails: response.productDetails.first,
        applicationUserName: null,
      );

      // Start the purchase
      final bool success = await InAppPurchase.instance.buyNonConsumable(
        purchaseParam: purchaseParam,
      );

      if (!success) {
        emit(const SubscriptionError('Failed to start purchase'));
        return;
      }

      // The actual purchase result will be delivered via the InAppPurchase.instance.purchaseStream
      // Make sure you're listening to this stream and handling the purchase updates

    } catch (e) {
      material.debugPrint('Error during subscription purchase: $e');
      emit(SubscriptionError(e.toString()));
      SnackBar snackBar = SnackBar(
        backgroundColor: TColor.redAccent,
        content: CustomText(
          text: e.toString(),
          color: TColor.white,
          maxLine: 3,
        ),
      );
      snackBarKey.currentState?.showSnackBar(snackBar);
    }
  }

  void listenToPurchaseUpdates(context) {
    InAppPurchase.instance.purchaseStream.listen(
      (purchaseDetailsList) {
        handlePurchaseUpdates(purchaseDetailsList);
      },
      onError: (error) {
        material.debugPrint('Error in purchase stream: $error');
        SnackBar snackBar = const SnackBar(
          backgroundColor: TColor.redAccent,
          content: CustomText(
            text: "",
            color: TColor.white,
            maxLine: 3,
          ),
        );
        snackBarKey.currentState?.showSnackBar(snackBar);
      },
    );
  }

  void handlePurchaseUpdates(List<PurchaseDetails> purchaseDetailsList) {
    for (final purchaseDetails in purchaseDetailsList) {
      if (purchaseDetails.status == PurchaseStatus.pending) {
        emit(SubscriptionLoading());
      } else if (purchaseDetails.status == PurchaseStatus.error) {
        emit(SubscriptionError(purchaseDetails.error?.message ?? 'Purchase failed'));
      } else if (purchaseDetails.status == PurchaseStatus.purchased ||
                 purchaseDetails.status == PurchaseStatus.restored) {
        // Verify the purchase with your backend
        _verifyPurchase(purchaseDetails);
      }

      if (purchaseDetails.pendingCompletePurchase) {
        InAppPurchase.instance.completePurchase(purchaseDetails);
      }
    }
  }

  Future<void> _verifyPurchase(PurchaseDetails purchaseDetails) async {
    try {
      final response = await _subscriptionsRepo.verifyPurchase(purchaseDetails.purchaseID!);

      if (response.status) {
        emit(SubscriptionSuccess());
        subscriptionStatus = true;
        getUserData();
         material.debugPrint('_verifyPurchase: $subscriptionStatus');
        SnackBar snackBar = SnackBar(
          backgroundColor: TColor.greenSuccess,
          content: CustomText(
            text: AppStrings.subscribedSuccessfully.tr(),
            color: TColor.white,
            maxLine: 3,
          ),
        );
        snackBarKey.currentState?.showSnackBar(snackBar);
      } else {
        emit(SubscriptionError(response.message));
      }
    } catch (e) {
      emit(SubscriptionError('Failed to verify purchase: $e'));
    }
  }

  getUserData() async {
    try {
      var response =
          await NetworkService().get(url: ConfigBase.profile, isAuth: true);
      material.debugPrint('Response data: ${response.data}');

      userEmail = response.data["email"];
      material.debugPrint('userEmail: $userEmail');

      userName = response.data["name"];
      material.debugPrint('userName: $userName');

      userImageUrl = response.data["logo_path"];
      material.debugPrint('userImageUrl: $userImageUrl');

      userPhone = response.data["phone"];
      material.debugPrint('userPhone: $userPhone');

      userAddress = response.data["address"];
      material.debugPrint('userAddress: $userAddress');
      Logger().e('subscription - Status: $subscriptionStatus');

      subscriptionStatus = response.data["subscription_status"] ?? false;
      material.debugPrint('subscriptionStatus: $subscriptionStatus');

    } catch (e, stackTrace) {
      material.debugPrint("catch error at profile fetch: $e");
      material.debugPrint(stackTrace.toString());
    }
  }
}

// Add these states to your subscription_state.dart file if not already present
class SubscriptionLoading extends SubscriptionState {
  @override
  List<Object?> get props => [];
}
class SubscriptionSuccess extends SubscriptionState {
  @override
  List<Object?> get props => [];
}
class SubscriptionError extends SubscriptionState {
  final String message;
  const SubscriptionError(this.message);

  @override
  List<Object?> get props => [message];
}