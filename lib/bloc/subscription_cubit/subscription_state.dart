part of 'subscription_cubit.dart';

abstract class SubscriptionState extends Equatable {
  const SubscriptionState();
}
//BuySubscriptionSuccess
class SubscriptionInitial extends SubscriptionState {
  @override
  List<Object> get props => [];
}



class BuySubscriptionSuccess extends SubscriptionState {
  @override
  List<Object> get props => [];
}
class GetSubscriptionSuccess extends SubscriptionState {
  @override
  List<Object> get props => [];
}



class couponLoading extends SubscriptionState {
  @override
  List<Object> get props => [];
}
