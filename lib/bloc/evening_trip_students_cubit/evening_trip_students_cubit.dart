import 'package:bus/bloc/evening_trip_students_cubit/evening_trip_students_states.dart';
import 'package:bus/data/models/trip_models/evening_trip_students_model.dart';
import 'package:bus/data/repo/evening_trip_students_repo.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

class EveningTripStudentsCubit extends Cubit<EveningTripStudentsState> {
  EveningTripStudentsCubit() : super(EveningTripStudentsInitialState());
  final _eveningTripStudentsRepo = EveningTripStudentsRepo();

  static EveningTripStudentsCubit get(context) => BlocProvider.of(context);

  EveningTripStudentsResponse? studentsResponse;
  List<EveningTripPresentStudent> presentStudents = [];

  /// Fetches students present on the bus for an evening trip
  Future<void> getPresentStudents({required String tripId}) async {
    emit(EveningTripStudentsLoadingState());
    try {
      final response = await _eveningTripStudentsRepo.getPresentStudents(tripId: tripId);
      
      if (response.errors == false) {
        studentsResponse = response;
        presentStudents = response.presentOnBus ?? [];
        emit(EveningTripStudentsSuccessState(response: response));
      } else {
        emit(EveningTripStudentsErrorState(error: response.message ?? "Unknown error"));
      }
    } catch (e, s) {
      debugPrint("Error in cubit: $e");
      debugPrint("Stack trace: $s");
      emit(EveningTripStudentsErrorState(error: e.toString()));
    }
  }
}
