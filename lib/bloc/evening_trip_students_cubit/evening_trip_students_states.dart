import 'package:bus/data/models/trip_models/evening_trip_students_model.dart';
import 'package:equatable/equatable.dart';

abstract class EveningTripStudentsState extends Equatable {
  const EveningTripStudentsState();

  @override
  List<Object?> get props => [];
}

class EveningTripStudentsInitialState extends EveningTripStudentsState {}

class EveningTripStudentsLoadingState extends EveningTripStudentsState {}

class EveningTripStudentsSuccessState extends EveningTripStudentsState {
  final EveningTripStudentsResponse response;

  const EveningTripStudentsSuccessState({required this.response});

  @override
  List<Object?> get props => [response];
}

class EveningTripStudentsErrorState extends EveningTripStudentsState {
  final String error;

  const EveningTripStudentsErrorState({required this.error});

  @override
  List<Object?> get props => [error];
}
