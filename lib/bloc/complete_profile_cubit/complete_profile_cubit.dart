import 'package:bus/config/theme_colors.dart';
import 'package:bus/translations/local_keys.g.dart';
import 'package:bus/views/custom_widgets/custom_snack_bar.dart';
import 'package:bus/views/screens/coupon_screen/coupon_screen.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:dio/dio.dart';
import '../../../helper/network_serviecs.dart';
import '../../../config/config_base.dart';
import '../../../helper/response_state.dart';
import 'complete_profile_states.dart';
import 'package:flutter/material.dart';

class CompleteProfileCubit extends Cubit<CompleteProfileStates> {
  CompleteProfileCubit() : super(const CompleteProfileStates());

  static CompleteProfileCubit get(context) => BlocProvider.of(context);

  Future<void> completeProfile({
    required String name,
    required String phone,
    required String address,
    String? cityName,
    double? latitude,
    double? longitude,
    String? imageFile,
    required BuildContext context,
  }) async {
    emit(state.copyWith(rStates: ResponseState.loading));

    try {
      FormData formData = FormData.fromMap({
        "name": name,
        "phone": phone,
        "address": address,
        if (cityName != null) "city_name": cityName,
        if (latitude != null) "latitude": latitude.toString(),
        if (longitude != null) "longitude": longitude.toString(),
        if (imageFile != null) "logo": await MultipartFile.fromFile(imageFile),
      });

      Response? response = await NetworkService().post(
        url: ConfigBase.completeProfile,
        body: formData,
        isAuth: true,
      );

      if (response.statusCode == 200 && response.data["status"] == true) {
         Navigator.pushNamed(context, CouponScreen.routeName);
        // Navigator.pushAndRemoveUntil(
        //                           context,
        //                           MaterialPageRoute(
        //                             builder: (context) => const LayoutScreen(),
        //                           ),
        //                           (_) => false);
      } else {
        String errorMessage = '';
        if (response.data is Map) {
          if (response.data['messages'] is Map) {
            // Handle nested messages
            Map<String, dynamic> messages = response.data['messages'];
            errorMessage = messages.entries
                .map((e) => '${e.key}: ${(e.value as List).join(', ')}')
                .join('\n');
          } else {
            // Handle direct message
            errorMessage =
                response.data["messages"] ?? AppStrings.failedToCompleteProfile.tr();
          }
        }

        customSnackBar(
          message: "Error ${response.statusCode} - $errorMessage",
          bgColor: TColor.redAccent,
        );

        emit(state.copyWith(
          rStates: ResponseState.failure,
          error: errorMessage,
        ));
      }
    } on DioException catch (e) {
      String errorMessage = '';
      if (e.response?.data is Map) {
        if (e.response?.data['messages'] is Map) {
          Map<String, dynamic> messages = e.response?.data['messages'];
          errorMessage = messages.entries
              .map((e) => '${e.key}: ${(e.value as List).join(', ')}')
              .join('\n');
        } else {
          errorMessage =
              e.response?.data['message'] ?? e.message ?? AppStrings.anErrorOccurred.tr();
        }
      } else {
        errorMessage = e.message ?? AppStrings.anErrorOccurred.tr();
      }

      customSnackBar(
        message: "Error ${e.response?.statusCode ?? 'unknown'} - $errorMessage",
        bgColor: TColor.redAccent,
      );

      emit(state.copyWith(
        rStates: ResponseState.failure,
        error: errorMessage,
      ));
    }
  }
}
