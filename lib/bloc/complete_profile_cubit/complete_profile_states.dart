import 'package:equatable/equatable.dart';
import '../../../helper/response_state.dart';

class CompleteProfileStates extends Equatable {
  final ResponseState rStates;
  final String? error;

  const CompleteProfileStates({
    this.rStates = ResponseState.init,
    this.error,
  });

  CompleteProfileStates copyWith({
    ResponseState? rStates,
    String? error,
  }) {
    return CompleteProfileStates(
      rStates: rStates ?? this.rStates,
      error: error ?? this.error,
    );
  }

  @override
  List<Object?> get props => [rStates, error];
}