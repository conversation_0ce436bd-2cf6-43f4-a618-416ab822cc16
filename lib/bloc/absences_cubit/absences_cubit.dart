import 'package:bus/bloc/absences_cubit/absences_states.dart';
import 'package:bus/data/repo/absences_repo.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

class AbsencesCubit extends Cubit<AbsencesStates> {
  final _absencesRepo = AbsencesRepo();
  AbsencesCubit() : super(AbsencesInitialStates());

  Future<void> getAbsences({int? pageNumber}) async {
    emit(AbsencesLoadingStates());
    try {
      final response = await _absencesRepo.repo(pageNumber: pageNumber);
      if (response[4] == true) {
        emit(AbsencesSuccessStates(
            absencesModels: response[0],
            currentpage: response[2] ?? 1,
            lastpage: response[3] ?? 1));
      } else {
        emit(AbsencesErrorStates(error: response[1]));
      }
    } catch (e) {
      if (kDebugMode) {
        debugPrint("catch error at absebces cubit $e");
      }
      emit(AbsencesErrorStates(error: e.toString()));
    }
  }

  Future<void> getAbsencesWithFilters(
      {int? pageNumber, attendType, studentName, date, busid}) async {
    emit(AbsencesLoadingStates());
    try {
      final response = await _absencesRepo.getAbsencesWithFiltter(
          date: date,
          studentName: studentName,
          attendType: attendType,
          pageNumber: pageNumber,
          busid: busid);
      if (response[4] == true) {
        emit(AbsencesSuccessStates(
            absencesModels: response[0],
            currentpage: response[2] ?? 1,
            lastpage: response[3] ?? 1));
      } else {
        emit(AbsencesSuccessStates(
            lastpage: response[3],
            currentpage: response[2],
            absencesModels: response[0]));
      }
    } catch (e) {
      if (kDebugMode) {
        debugPrint("catch error at absebces cubit $e");
      }
      emit(AbsencesErrorStates(error: e.toString()));
    }
  }
}
