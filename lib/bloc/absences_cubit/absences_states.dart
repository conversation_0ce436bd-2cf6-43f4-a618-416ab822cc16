abstract class AbsencesStates {}

class AbsencesInitialStates extends AbsencesStates {}

class AbsencesLoadingStates extends AbsencesStates {}

class AbsencesSuccessStates extends AbsencesStates {
  final List<AbsencesModel> absencesModels;
  final int lastpage;
  final int currentpage;

  AbsencesSuccessStates({
    required this.lastpage,
    required this.currentpage,
    required this.absencesModels,
  });
}

class AbsencesErrorStates extends AbsencesStates {
  final String? error;
  AbsencesErrorStates({this.error});
}

class AbsencesModel {
  String? school_name;
  // String? school_phone;
  int? id;
  int? school_id;
  int? bus_id;
  String? bus_name;
  String? bus_car_number;

  int? my__parent_id;
  String? my__parent_name;
  String? my__parent_phone;

  int? student_id;
  String? student_name;
  String? student_phone;

  String? attendence_date;
  String? attendence_type;

  AbsencesModel({
    this.id,
    this.school_name,
    this.school_id,
    this.bus_id,
    this.bus_name,
    this.bus_car_number,
    this.my__parent_id,
    this.my__parent_name,
    this.my__parent_phone,
    this.student_id,
    this.student_name,
    this.student_phone,
    this.attendence_date,
    this.attendence_type,
  });

  factory AbsencesModel.fromMap(Map<String, dynamic> map) {
    return AbsencesModel(
      id: map['id'],
      school_id: int.parse(map['school_id'].toString()),
      bus_id: int.parse(map['bus']['id'].toString()),
      bus_name: map['bus']?['name'] ?? "",
      bus_car_number: map['bus']?['car_number'] ?? "",
      my__parent_id: int.parse(map['my__parent_id'].toString()),
      my__parent_name: map['parent']?['name'] ?? "",
      my__parent_phone: map['parent']?['phone'] ?? "",
      student_id: int.parse(map['students']["id"].toString()),
      student_name: map['students']?["name"] ?? "",
      student_phone: map['students']?['phone'] ?? "",
      school_name: map['schools']?["name"] ?? "",
      attendence_date: map['attendence_date'] ?? "",
      attendence_type: map['attendence_type'] ?? "",
    );
  }
  @override
  String toString() {
    return '#AbsencesModel(id: $id, school_id: $school_id, bus_id: $bus_id, bus_name: $bus_name, bus_car_number: $bus_car_number, my__parent_id: $my__parent_id, my__parent_name: $my__parent_name, my__parent_phone: $my__parent_phone, student_id: $student_id, student_name: $student_name, student_phone: $student_phone, attendence_date: $attendence_date, attendence_type: $attendence_type)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;

    return other is AbsencesModel &&
        other.id == id &&
        other.school_id == school_id &&
        other.bus_id == bus_id &&
        other.bus_name == bus_name &&
        other.bus_car_number == bus_car_number &&
        other.my__parent_id == my__parent_id &&
        other.my__parent_name == my__parent_name &&
        other.my__parent_phone == my__parent_phone &&
        other.student_id == student_id &&
        other.student_name == student_name &&
        other.student_phone == student_phone &&
        other.attendence_date == attendence_date &&
        other.attendence_type == attendence_type;
  }

  @override
  int get hashCode {
    return id.hashCode ^
        school_id.hashCode ^
        bus_id.hashCode ^
        bus_name.hashCode ^
        bus_car_number.hashCode ^
        my__parent_id.hashCode ^
        my__parent_name.hashCode ^
        my__parent_phone.hashCode ^
        student_id.hashCode ^
        student_name.hashCode ^
        student_phone.hashCode ^
        attendence_date.hashCode ^
        attendence_type.hashCode;
  }
}
