import 'package:bus/data/repo/auth_repo/login_repo.dart';
import 'package:bus/helper/response_state.dart';
import 'package:equatable/equatable.dart';

class LoginState extends Equatable {
  final LoginDataModelh? loginModels;
  final ResponseState rStates;
  final String? massage;

  const LoginState({
    this.loginModels,
    this.rStates = ResponseState.init,
    this.massage,
  });

  LoginState copyWith({
    LoginDataModelh? loginModels,
    ResponseState? rStates,
    String? massage,
  }) {
    return LoginState(
      loginModels: loginModels ?? this.loginModels,
      rStates: rStates ?? this.rStates,
      massage: massage ?? this.massage,
    );
  }

  @override
  List<Object?> get props => [
        loginModels,
        rStates,
        massage,
      ];
}
