import 'package:bus/bloc/login_cubit/login_states.dart';
import 'package:bus/config/global_variable.dart';
import 'package:bus/data/repo/auth_repo/login_repo.dart';
import 'package:bus/helper/cache_helper.dart';
import 'package:bus/helper/data_state.dart';
import 'package:bus/helper/response_state.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

class LoginCubit extends Cubit<LoginState> {
  final _loginRepo = LoginRepo();
  LoginCubit() : super(const LoginState());

  Future<void> login({
    String? email,
    String? password,
  }) async {
    emit(state.copyWith(rStates: ResponseState.loading));
    DataState<LoginDataModelh> response = await _loginRepo.repo(
      email: email,
      password: password,
    );

    if (response is DataSuccess) {
      debugPrint(response.data?.email_verified_at);
      if (response.data?.email_verified_at == null) {
        emit(
          state.copyWith(
            rStates: ResponseState.needVerivecation,
          ),
        );
      } else {
        // Set Google sign-in flag to false for regular login
        isGoogleSignIn = false;
        CacheHelper.putBool("isGoogleSignIn", false);
        debugPrint("LoginCubit.login - Set isGoogleSignIn to false");

        emit(
          state.copyWith(
            loginModels: response.data,
            rStates: ResponseState.success,
          ),
        );
      }
    } else {
      emit(
        state.copyWith(rStates: ResponseState.failure),
      );
    }
  }


   Future<void> firebaseLogin({ String? accessToken,
    String? idToken, }) async {
    emit(state.copyWith(rStates: ResponseState.loading));
    DataState<LoginDataModelh> response =
        await _loginRepo.firebaseRepo(idToken: idToken,
        accessToken: accessToken);
        debugPrint("firebaseLogin ${response.data}");
        debugPrint("firebaseLogin ${response.data?.email_verified_at}");
    if (response is DataSuccess) {
      if (response.data?.email_verified_at == null) {
        emit(state.copyWith(rStates: ResponseState.needVerivecation));
      } else {
        // Set Google sign-in flag in the cubit
        isGoogleSignIn = true;
        CacheHelper.putBool("isGoogleSignIn", true);
        debugPrint("LoginCubit.firebaseLogin - Set isGoogleSignIn to true");

        emit(state.copyWith(loginModels: response.data, rStates: ResponseState.success));
      }
    } else {
      emit(
        state.copyWith(rStates: ResponseState.failure),
      );
    }
  }
}
