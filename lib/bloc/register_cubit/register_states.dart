import 'package:bus/helper/response_state.dart';
import 'package:equatable/equatable.dart';

import '../../data/models/auth_models/register_models/register_models.dart';

class RegisterStates extends Equatable {
  final RegisterModels? registerModels;
  final ResponseState? rState;
  final String? rMessage;

  const RegisterStates({
    this.rMessage = " ",
    this.registerModels,
    this.rState = ResponseState.init,
  });

  RegisterStates copyWith(
      {RegisterModels? registerModels,
      ResponseState? rState,
      String? message}) {
    return RegisterStates(
      registerModels: registerModels ?? this.registerModels,
      rState: rState ?? this.rState,
      rMessage: message,
    );
  }

  @override
  List<Object?> get props => [registerModels, rState, rMessage];
}
