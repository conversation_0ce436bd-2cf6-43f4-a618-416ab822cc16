import 'package:bus/bloc/register_cubit/register_states.dart';
import 'package:bus/data/repo/auth_repo/register_repo.dart';
import 'package:bus/helper/data_state.dart';
import 'package:bus/helper/response_state.dart';
import 'package:bus/translations/local_keys.g.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import '../../data/models/auth_models/register_models/register_models.dart';

class RegisterCubit extends Cubit<RegisterStates> {
  final _registerRepo = RegisterRepo();
  RegisterCubit() : super(const RegisterStates());
  bool? getStatus;
  String? version;

  Future<void> register({
    String? name,
    String? email,
    String? password,
    String? confirmedPassword,
    String? phone,
    String? address,
    String? latitude,
    String? longitude,
    BuildContext? context,
  }) async {
    emit(state.copyWith(rState: ResponseState.loading));
    DataState<RegisterModels> response = await _registerRepo.repo(
      name: name,
      email: email,
      password: password,
      confirmedPassword: confirmedPassword,
      phone: phone,
      address: address,
      latitude: latitude,
      longitude: longitude,
      context: context,
    );
    if (password != confirmedPassword) {
      emit(state.copyWith(
        rState: ResponseState.failure,
        message: AppStrings.checkPassword.tr(),
      ));
    } else {
      if (response is DataSuccess) {
        emit(
          state.copyWith(
              registerModels: response.data, rState: ResponseState.success),
        );
      } else if (response is DataFailed) {
        emit(
          state.copyWith(
              rState: ResponseState.failure,
              registerModels: (response as DataFailed).data,
              message: response.message.toString()),
        );
      }
    }
  }


    Future<void> getUserStatus(String name) async {
    emit(state.copyWith(rState: ResponseState.loading));
    DataState<Map> response = await _registerRepo.getUserStatus(name);

    if (response is DataSuccess) {
     getStatus = response.data!["status"]==1?true:false;
     version = response.data!["version"];
     debugPrint("getStatus $getStatus");
     debugPrint("getStatus ${response.data!["version"]}");
     
      emit(
        state.copyWith( rState: ResponseState.getStatus ),
      );
    } else if (response is DataFailed) {
      emit(
        state.copyWith(
            rState: ResponseState.failure,
            message: response.message.toString()),
      );
    }
  }

}
