import 'package:bloc/bloc.dart';
import 'package:bus/bloc/change_password_cubit/change_password_states.dart';
import 'package:bus/data/repo/change_password_repo.dart';
import 'package:bus/helper/data_state.dart';
import 'package:bus/helper/response_state.dart';

import '../../data/models/change_password_model.dart';

class ChangePasswordCubit extends Cubit<ChangePasswordStates> {
  final _changePasswordRepo = ChangePasswordRepo();
  ChangePasswordCubit() : super(const ChangePasswordStates());

  Future<void> changePassword({
    String? currentPassword,
    String? newPassword,
    String? confirmedPassword,
  }) async {
    emit(state.copyWith(rStates: ResponseState.loading));
    DataState<ChangePasswordModel> response = await _changePasswordRepo.repo(
      currentPassword: currentPassword,
      newPassword: newPassword,
      confirmedPassword: confirmedPassword,
    );

    if (response is DataSuccess) {
      emit(
        state.copyWith(
            rStates: ResponseState.success,
            changePasswordModels: response.data),
      );
    } else {
      emit(state.copyWith(rStates: ResponseState.failure, message: response.message));
    }
  }
}
