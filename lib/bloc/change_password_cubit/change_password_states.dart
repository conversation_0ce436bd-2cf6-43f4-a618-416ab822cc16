import 'package:bus/helper/response_state.dart';
import 'package:equatable/equatable.dart';

import '../../data/models/change_password_model.dart';

class ChangePasswordStates extends Equatable {
  final ChangePasswordModel? changePasswordModels;
  final ResponseState rStates;
  final String? message;

  const ChangePasswordStates({
    this.changePasswordModels,
    this.rStates = ResponseState.init,
    this.message
  });

  ChangePasswordStates copyWith({
    ChangePasswordModel? changePasswordModels,
    ResponseState? rStates,
    String? message
  }) {
    return ChangePasswordStates(
      changePasswordModels: changePasswordModels ?? this.changePasswordModels,
      rStates: rStates ?? this.rStates,
      message: message ?? this.message
    );
  }

  @override
  List<Object?> get props => [
        changePasswordModels,
        rStates,
      ];
}
