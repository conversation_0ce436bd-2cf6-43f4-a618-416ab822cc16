import 'package:bus/bloc/question_helper_cubit/question_helper_states.dart';
import 'package:bus/data/repo/question_help_repo.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

class QuestionHelperCubit extends Cubit<QuestionHelperStates> {
  QuestionHelperCubit() : super(QuestionHelperInitialStates());
  final _questionHelperRepo = QuestionHelperRepo();

  static QuestionHelperCubit get(context) => BlocProvider.of(context);

  Future<void> getQuestion() async {
    emit(QuestionHelperLoadingStates());
    try {
      final response = await _questionHelperRepo.repo();
      if (response.status == true) {
        emit(QuestionHelperSuccessStates(questionHelp: response));
      } else {
        emit(QuestionHelperErrorStates(error: response.message));
      }
    } catch (e, s) {
      debugPrint("catch error $s");
      debugPrint("catch error at cubits$e");
      emit(QuestionHelperErrorStates(error: e.toString()));
    }
  }
}
