import 'package:bus/data/models/question_help_models/question_help_models.dart';

abstract class QuestionHelperStates {}

class Question<PERSON><PERSON>perInitialStates extends QuestionHelperStates {}

class QuestionHelperLoadingStates extends QuestionHelperStates {}

class QuestionHelperSuccessStates extends Question<PERSON><PERSON>perStates {
  final QuestionHelp? questionHelp;
  QuestionHelperSuccessStates({
    this.questionHelp,
  });
}

class QuestionHelperErrorStates extends QuestionHelperStates {
  final String? error;
  QuestionHelperErrorStates({
    this.error,
  });
}
