import 'package:bus/data/models/trip_models/evening_trip_arrived_model.dart';
import 'package:equatable/equatable.dart';

abstract class EveningTripArrivedState extends Equatable {
  const EveningTripArrivedState();

  @override
  List<Object?> get props => [];
}

class EveningTripArrivedInitialState extends EveningTripArrivedState {}

class EveningTripArrivedLoadingState extends EveningTripArrivedState {}

class EveningTripArrivedSuccessState extends EveningTripArrivedState {
  final EveningTripArrivedResponse response;

  const EveningTripArrivedSuccessState({required this.response});

  @override
  List<Object?> get props => [response];
}

class EveningTripArrivedErrorState extends EveningTripArrivedState {
  final String error;

  const EveningTripArrivedErrorState({required this.error});

  @override
  List<Object?> get props => [error];
}
