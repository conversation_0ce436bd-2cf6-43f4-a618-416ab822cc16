import 'package:bus/bloc/evening_trip_arrived_cubit/evening_trip_arrived_states.dart';
import 'package:bus/data/models/trip_models/evening_trip_arrived_model.dart';
import 'package:bus/data/repo/evening_trip_arrived_repo.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

class EveningTripArrivedCubit extends Cubit<EveningTripArrivedState> {
  EveningTripArrivedCubit() : super(EveningTripArrivedInitialState());
  final _eveningTripArrivedRepo = EveningTripArrivedRepo();

  static EveningTripArrivedCubit get(context) => BlocProvider.of(context);

  EveningTripArrivedResponse? arrivedResponse;
  List<EveningTripArrivedStudent> arrivedStudents = [];

  /// Fetches students who arrived home during an evening trip
  Future<void> getArrivedStudents({required String busId}) async {
    emit(EveningTripArrivedLoadingState());
    try {
      final response = await _eveningTripArrivedRepo.getArrivedStudents(busId: busId);
      
      if (response.errors == false) {
        arrivedResponse = response;
        arrivedStudents = response.arrived ?? [];
        emit(EveningTripArrivedSuccessState(response: response));
      } else {
        emit(EveningTripArrivedErrorState(error: response.message ?? "Unknown error"));
      }
    } catch (e, s) {
      debugPrint("Error in cubit: $e");
      debugPrint("Stack trace: $s");
      emit(EveningTripArrivedErrorState(error: e.toString()));
    }
  }
}
