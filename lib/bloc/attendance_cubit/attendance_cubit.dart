import 'package:bus/bloc/attendance_cubit/attendance_states.dart';
import 'package:bus/data/models/attendance_models/absent_student_model.dart';
import 'package:bus/data/models/attendance_models/attendant_student_model.dart';
import 'package:bus/data/models/attendance_models/student_attendance_model.dart';
import 'package:bus/data/repo/attendance_repo.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

class AttendanceCubit extends Cubit<AttendanceStates> {
  final _attendanceRepo = AttendanceRepo();

  AttendanceCubit() : super(AttendanceInitialState());

  static AttendanceCubit get(context) => BlocProvider.of(context);

  List<StudentAttendanceModel> allStudents = [];
  List<StudentAttendanceModel> presentStudents = [];
  List<StudentAttendanceModel> absentStudents = [];
  List<AbsentStudentModel> apiAbsentStudents = [];
  List<AttendantStudentModel> apiAttendantStudents = [];

  Future<void> getTripAttendance({required int tripId}) async {
    emit(AttendanceLoadingState());

    try {
      // Get both present and absent students from API
      await Future.wait([
        _getAttendantStudents(tripId),
        _getAbsentStudents(tripId),
      ]);

      // Combine all students for the header count
      allStudents = [...presentStudents, ...absentStudents];

      emit(AttendanceSuccessState(students: allStudents));
    } catch (e) {
      debugPrint("Error in getTripAttendance: $e");
      emit(AttendanceErrorState(error: e.toString()));
    }
  }

  Future<void> _getAttendantStudents(int tripId) async {
    try {
      final response =
          await _attendanceRepo.getTripAttendantStudents(tripId: tripId);

      if (response.errors != true && response.attendantStudents != null) {
        apiAttendantStudents = response.attendantStudents ?? [];

        // Convert API attendant students to StudentAttendanceModel for UI
        presentStudents = apiAttendantStudents
            .map((student) => StudentAttendanceModel(
                  id: int.tryParse(student.id ?? "0"),
                  name: student.name,
                  is_present: true,
                  attendance_time:
                      student.pivot?.arrived_at ?? student.pivot?.onboard_at,
                  grade: "الصف ${student.grade_id}",
                  classroom: student.classroom_id?.toString() ?? "",
                  profile_image: student.logo_path,
                ))
            .toList();
      } else {
        // If API fails, use empty list
        presentStudents = [];
      }
    } catch (e) {
      debugPrint("Error in _getAttendantStudents: $e");
      // If API fails, use empty list
      presentStudents = [];
    }
  }

  Future<void> _getAbsentStudents(int tripId) async {
    try {
      final response =
          await _attendanceRepo.getTripAbsentStudents(tripId: tripId);

      if (response.errors != true && response.absentStudents != null) {
        apiAbsentStudents = response.absentStudents ?? [];

        // Convert API absent students to StudentAttendanceModel for UI
        absentStudents = apiAbsentStudents
            .map((student) => StudentAttendanceModel(
                  id: int.tryParse(student.id ?? "0"),
                  name: student.name,
                  is_present: false,
                  attendance_time: null,
                  grade: "الصف ${student.grade_id}",
                  classroom: student.classroom_id?.toString() ?? "",
                  profile_image: student.logo_path,
                ))
            .toList();
      } else {
        // If API fails, use empty list
        absentStudents = [];
      }
    } catch (e) {
      debugPrint("Error in _getAbsentStudents: $e");
      // If API fails, use empty list
      absentStudents = [];
    }
  }
}
