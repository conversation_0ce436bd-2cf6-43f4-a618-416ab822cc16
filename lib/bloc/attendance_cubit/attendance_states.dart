import 'package:bus/data/models/attendance_models/student_attendance_model.dart';
import 'package:equatable/equatable.dart';

abstract class AttendanceStates extends Equatable {
  const AttendanceStates();

  @override
  List<Object?> get props => [];
}

class AttendanceInitialState extends AttendanceStates {}

class AttendanceLoadingState extends AttendanceStates {}

class AttendanceSuccessState extends AttendanceStates {
  final List<StudentAttendanceModel> students;

  const AttendanceSuccessState({required this.students});

  @override
  List<Object?> get props => [students];
}

class AttendanceErrorState extends AttendanceStates {
  final String error;

  const AttendanceErrorState({required this.error});

  @override
  List<Object?> get props => [error];
}
