import 'package:bus/data/models/updata_bus_models/update_bus_models.dart';


abstract class UpdateBusStates {}

class UpdateBusInitialStates extends UpdateBusStates {}

class UpdateBusLoadingStates extends UpdateBusStates {}

class UpdateBusSuccessStates extends UpdateBusStates {
  final UpdateBusModels? updateBusModels;
  UpdateBusSuccessStates({
    this.updateBusModels,
  });
}

class UpdateBusErrorStates extends UpdateBusStates {
  final String? error;
  UpdateBusErrorStates({
    this.error,
  });
}
