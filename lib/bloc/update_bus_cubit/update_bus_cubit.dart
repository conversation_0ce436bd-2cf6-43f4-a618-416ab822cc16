import 'package:bus/bloc/update_bus_cubit/update_bus_states.dart';
import 'package:bus/data/repo/update_bus_repo.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

class UpdateBusCubit extends Cubit<UpdateBusStates> {
  final _updateBusRepo = UpdateBusRepo();
  UpdateBusCubit() : super(UpdateBusInitialStates());

  Future<void> updateBus({
    String? busId,
    String? name,
    String? notes,
    String? car_number,
  }) async {
    emit(UpdateBusLoadingStates());
    try {
      final response = await _updateBusRepo.repo(
        busId: busId,
        name: name,
        notes: notes,
        car_number: car_number,
      );
      if (response.status == true) {
        emit(
          UpdateBusSuccessStates(updateBusModels: response),
        );
      } else {
        emit(UpdateBusErrorStates(error: response.message));
      }
    } catch (e, s) {
      debugPrint("catch error $s");
      debugPrint("catch error $e");
      emit(UpdateBusErrorStates(error: e.toString()));
    }
  }
}
