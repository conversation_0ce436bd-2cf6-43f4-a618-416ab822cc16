part of 'class_room_cubit.dart';

abstract class ClassRoomState extends Equatable {
  const ClassRoomState();

  @override
  List<Object> get props => [];
}

class AllClassRoomInitialStates extends ClassRoomState {}

class AllClassRoomLoadingStates extends ClassRoomState {}

class AllClassRoomSuccessStates extends ClassRoomState {
  final List<ClassRoomModel> classrooms;
  AllClassRoomSuccessStates({
    required this.classrooms,
  });
}

class ClassRoomInitial extends ClassRoomState {}

class ClassRoomInitialStates extends ClassRoomState {}

class ClassRoomLoadingStates extends ClassRoomState {}

class ClassRoomSuccessStates extends ClassRoomState {
  final ClassRoomModels? classRoomModels;
  ClassRoomSuccessStates({this.classRoomModels});
}

class ClassRoomErrorStates extends ClassRoomState {
  final String? error;
  ClassRoomErrorStates({
    this.error,
  });
}

class ClassRoomModels {
  int? gradeid;
  List<ClassRoomModel?>? classRooms;
  ClassRoomModels({
    this.gradeid,
    this.classRooms,
  });

  ClassRoomModels copyWith({
    int? gradeid,
    List<ClassRoomModel?>? classRooms,
  }) {
    return ClassRoomModels(
      gradeid: gradeid ?? this.gradeid,
      classRooms: classRooms ?? this.classRooms,
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'gradeid': gradeid,
      'classRooms': classRooms?.map((x) => x?.toMap()).toList(),
    };
  }

  factory ClassRoomModels.fromMap(Map<String, dynamic> map) {
    return ClassRoomModels(
      gradeid: map['gradeid']?.toInt(),
      classRooms: map['classRooms'] != null
          ? List<ClassRoomModel?>.from(
              map['classRooms']?.map((x) => ClassRoomModel?.fromMap(x)))
          : null,
    );
  }

  String toJson() => json.encode(toMap());

  factory ClassRoomModels.fromJson(String source) =>
      ClassRoomModels.fromMap(json.decode(source));

  @override
  String toString() =>
      'ClassRoomModels(gradeid: $gradeid, classRooms: $classRooms)';

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;

    return other is ClassRoomModels &&
        other.gradeid == gradeid &&
        listEquals(other.classRooms, classRooms);
  }

  @override
  int get hashCode => gradeid.hashCode ^ classRooms.hashCode;
}

class ClassRoomModel {
  int? id;
  String? name;
  int? grade_id;
  ClassRoomModel({
    this.grade_id,
    this.id,
    this.name,
  });

  ClassRoomModel copyWith({
    int? id,
    String? name,
  }) {
    return ClassRoomModel(
      id: id ?? this.id,
      name: name ?? this.name,
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'name': name,
    };
  }

  factory ClassRoomModel.fromMap(Map<String, dynamic> map) {
    return ClassRoomModel(
      id: map['id'],
      name: map['name'],
      grade_id: map['grade_id'],
    );
  }

  String toJson() => json.encode(toMap());

  factory ClassRoomModel.fromJson(String source) =>
      ClassRoomModel.fromMap(json.decode(source));

  @override
  String toString() =>
      'ClassRoomModel(grade_id : $grade_id ,  : $id, name: $name)';

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;

    return other is ClassRoomModel && other.id == id && other.name == name;
  }

  @override
  int get hashCode => id.hashCode ^ name.hashCode;
}
