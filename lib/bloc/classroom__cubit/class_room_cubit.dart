import 'dart:convert';
import 'dart:developer';

import 'package:bloc/bloc.dart';
import 'package:bus/config/config_base.dart';
import 'package:bus/config/global_variable.dart';
import 'package:dio/dio.dart';
import 'package:equatable/equatable.dart';
import 'package:flutter/foundation.dart';

part 'class_room_state.dart';

class ClassRoomCubit extends Cubit<ClassRoomState> {
  ClassRoomCubit() : super(ClassRoomInitial());

  Future<void> getClassRoom(int gradid) async {
    emit(ClassRoomLoadingStates());
    try {
      final authHeaders = {'Authorization': "Bearer $token"};
      final response = await Dio().get(
        "${ConfigBase.baseUrl}classrooms/grade/classroom/$gradid",
        options: Options(
          headers: authHeaders,
        ),
      );
      if (response.statusCode == 200) {
        log(response.data["data"]["classrooms"].toString(), name: "e");
        log(gradid.toString(), name: "e");
        List<ClassRoomModel> classrooms = [];
        for (var e in (response.data["data"]["classrooms"] as List<dynamic>)) {
          classrooms.add(ClassRoomModel.fromMap(e));
        }

        ClassRoomModels religionModels =
            ClassRoomModels(classRooms: classrooms);

        emit(ClassRoomSuccessStates(classRoomModels: religionModels));
      } else {
        emit(ClassRoomErrorStates());
      }
    } on DioException catch (e) {
      if (e.response != null) {}
      log(e.error.toString(), name: 'dioError');
    } catch (e) {
      debugPrint("catch error at class room cubit $e");
      emit(ClassRoomErrorStates());
    }
  }

  Future<void> deleteClassRoom(id) async {
    try {
      final authHeaders = {'Authorization': "Bearer $token"};
      //debugPrint(token);
      debugPrint(id);
      final response = await Dio().delete(
        "${ConfigBase.baseUrl}classrooms/destroy/$id",
        options: Options(
          headers: authHeaders,
        ),
      );
      debugPrint(response.data.toString());
    } catch (e) {
      debugPrint(e.toString());
    }
  }

  Future<void> getAllClassRooms({
    int pagenumber = 1,
  }) async {
    // Logger().wtf("====================================");
    emit(AllClassRoomLoadingStates());
    try {
      final authHeaders = {'Authorization': "Bearer $token"};
      final response = await Dio().get(
        "${ConfigBase.baseUrl}classrooms/index",
        options: Options(
          headers: authHeaders,
        ),
      );
      // Logger().wtf(response.data);
      if (response.statusCode == 200) {
        // Logger().wtf(response.data);
        // log(response.data["data"]["classrooms"].toString(), name: "e");

        List<ClassRoomModel> classrooms = [];

        for (var e in (response.data["data"]["classrooms"] as List<dynamic>)) {
          classrooms.add(ClassRoomModel.fromMap(e));
        }

        emit(AllClassRoomSuccessStates(
          classrooms: classrooms,
        ));
      } else {
        emit(ClassRoomErrorStates());
      }
    } on DioException catch (e) {
      if (e.response != null) {}
      log(e.error.toString(), name: 'dioError');
    } catch (e) {
      debugPrint("catch error at class room cubit $e");
      emit(ClassRoomErrorStates());
    }
  }
}
