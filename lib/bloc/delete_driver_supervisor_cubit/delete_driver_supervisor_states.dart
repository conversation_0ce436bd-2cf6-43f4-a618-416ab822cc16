import 'package:bus/data/models/delete_driver_supervisor_models/delete_driver_supervisor_models.dart';

abstract class DeleteDriverSupervisorStates {}

class DeleteDriverSupervisorInitialStates extends DeleteDriverSupervisorStates {}

class DeleteDriverSupervisorLoadingStates extends DeleteDriverSupervisorStates {}

class DeleteDriverSupervisorSuccessStates extends DeleteDriverSupervisorStates {
  final DeleteDriverSupervisorModels? deleteDriverSupervisorModels;
  DeleteDriverSupervisorSuccessStates({
    this.deleteDriverSupervisorModels,
  });
}

class DeleteDriverSupervisorErrorStates extends DeleteDriverSupervisorStates {
  final String? error;
  DeleteDriverSupervisorErrorStates({
    this.error,
  });
}
