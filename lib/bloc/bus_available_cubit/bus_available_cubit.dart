import 'package:bus/bloc/bus_available_cubit/bus_available_states.dart';
import 'package:bus/data/repo/available_bus_in_driver_repo.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

class BusAvailableCubit extends Cubit<BusAvailableStates> {
  final _busAvailableRepo = BusAvailableRepo();
  BusAvailableCubit() : super(BusAvailableInitialStates());

  Future<void> busAvailable() async {
    emit(BusAvailableLoadingStates());
    try {
      final response = await _busAvailableRepo.repo();
      if (response.status == true) {
        emit(
          BusAvailableSuccessStates(busAvailableModels: response),
        );
      } else {
        emit(BusAvailableErrorStates(error: response.message));
      }
    } catch (e, s) {
      debugPrint("catch error $s");
      debugPrint("catch error $e");
      emit(BusAvailableErrorStates(error: e.toString()));
    }
  }
}
