import 'package:bus/data/models/bus_avaiable_models/bus_available_models.dart';

abstract class BusAvailableStates {}

class BusAvailableInitialStates extends BusAvailableStates {}

class BusAvailableLoadingStates extends BusAvailableStates {}

class BusAvailableSuccessStates extends BusAvailableStates {
  final BusAvailableModels? busAvailableModels;
  BusAvailableSuccessStates({
    this.busAvailableModels,
  });
}

class BusAvailableErrorStates extends BusAvailableStates {
  final String? error;
  BusAvailableErrorStates({
    this.error,
  });
}
