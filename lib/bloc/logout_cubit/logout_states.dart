import 'package:bus/helper/response_state.dart';
import 'package:equatable/equatable.dart';

import '../../data/models/change_password_model.dart';

class LogoutStates extends Equatable {
  final ChangePasswordModel? changePasswordModels;
  final ResponseState rStates;

  const LogoutStates({
    this.changePasswordModels,
    this.rStates = ResponseState.init,
  });

  LogoutStates copyWith({
    ChangePasswordModel? changePasswordModels,
    ResponseState? rStates,
  }) {
    return LogoutStates(
      changePasswordModels: changePasswordModels ?? this.changePasswordModels,
      rStates: rStates ?? this.rStates,
    );
  }

  @override
  List<Object?> get props => [
        changePasswordModels,
        rStates,
      ];
}
