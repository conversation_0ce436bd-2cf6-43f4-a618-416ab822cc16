import 'package:bus/data/models/trip_models/morning_trip_waiting_model.dart';
import 'package:equatable/equatable.dart';

abstract class MorningTripWaitingState extends Equatable {
  const MorningTripWaitingState();

  @override
  List<Object?> get props => [];
}

class MorningTripWaitingInitialState extends MorningTripWaitingState {}

class MorningTripWaitingLoadingState extends MorningTripWaitingState {}

class MorningTripWaitingSuccessState extends MorningTripWaitingState {
  final MorningTripWaitingResponse response;

  const MorningTripWaitingSuccessState({required this.response});

  @override
  List<Object?> get props => [response];
}

class MorningTripWaitingErrorState extends MorningTripWaitingState {
  final String error;

  const MorningTripWaitingErrorState({required this.error});

  @override
  List<Object?> get props => [error];
}
