import 'package:bus/bloc/morning_trip_waiting_cubit/morning_trip_waiting_states.dart';
import 'package:bus/data/models/trip_models/morning_trip_waiting_model.dart';
import 'package:bus/data/repo/morning_trip_waiting_repo.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

class MorningTripWaitingCubit extends Cubit<MorningTripWaitingState> {
  MorningTripWaitingCubit() : super(MorningTripWaitingInitialState());
  final _morningTripWaitingRepo = MorningTripWaitingRepo();

  static MorningTripWaitingCubit get(context) => BlocProvider.of(context);

  MorningTripWaitingResponse? waitingResponse;
  List<MorningTripWaitingStudent> waitingStudents = [];

  /// Fetches students waiting for the morning trip
  Future<void> getWaitingStudents({required String busId}) async {
    emit(MorningTripWaitingLoadingState());
    try {
      final response = await _morningTripWaitingRepo.getWaitingStudents(busId: busId);
      
      if (response.status == true) {
        waitingResponse = response;
        waitingStudents = response.waiting ?? [];
        emit(MorningTripWaitingSuccessState(response: response));
      } else {
        emit(MorningTripWaitingErrorState(error: response.message ?? "Unknown error"));
      }
    } catch (e, s) {
      debugPrint("Error in cubit: $e");
      debugPrint("Stack trace: $s");
      emit(MorningTripWaitingErrorState(error: e.toString()));
    }
  }
}
