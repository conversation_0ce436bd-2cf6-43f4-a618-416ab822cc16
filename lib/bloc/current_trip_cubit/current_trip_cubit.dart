
import 'package:bus/bloc/current_trip_cubit/current_trip_states.dart';
import 'package:bus/data/repo/current_trip_repo.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

class CurrentTripCubit extends Cubit<CurrentTripStates> {
  CurrentTripCubit() : super(CurrentTripInitialStates());
  final _currentTripRepo = CurrentTripRepo();

  static CurrentTripCubit get(context) => BlocProvider.of(context);


  Future<void> getCurrentTrip() async {
    emit(CurrentTripLoadingStates());
    try {
      final response = await _currentTripRepo.repo();
      if(response.status == true) {
        emit(CurrentTripSuccessStates(currentTripModels: response));
      } else {
        emit(CurrentTripErrorStates(error: response.message));
      }
    }catch (e, s) {
      debugPrint("catch error $s");
      debugPrint("catch error at cubits$e");
      emit(CurrentTripErrorStates(error: e.toString()));
    }
  }
}