

import 'package:bus/data/models/current_trip_models/current_trip_models.dart';

abstract class CurrentTripStates {}

class CurrentTripInitialStates extends CurrentTripStates {}

class CurrentTripLoadingStates extends CurrentTripStates {}

class CurrentTripSuccessStates extends CurrentTripStates {
  final CurrentTripModels? currentTripModels;
  CurrentTripSuccessStates({
    this.currentTripModels,
  });
}

class CurrentTripErrorStates extends CurrentTripStates {
  final String? error;
  CurrentTripErrorStates({
    this.error,
  });
}
