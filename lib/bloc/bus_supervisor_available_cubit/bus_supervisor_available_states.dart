import 'package:bus/data/models/bus_avaiable_models/bus_available_models.dart';

abstract class BusSupervisorAvailableStates {}

class BusSupervisorAvailableInitialStates
    extends BusSupervisorAvailableStates {}

class BusSupervisorAvailableLoadingStates
    extends BusSupervisorAvailableStates {}

class BusSupervisorAvailableSuccessStates extends BusSupervisorAvailableStates {
  final BusAvailableModels? busAvailableModels;
  BusSupervisorAvailableSuccessStates({
    this.busAvailableModels,
  });
}

class BusSupervisorAvailableErrorStates extends BusSupervisorAvailableStates {
  final String? error;
  BusSupervisorAvailableErrorStates({
    this.error,
  });
}
