import 'package:bus/bloc/parent_cubit/parent_repo.dart';
import 'package:bus/bloc/parent_cubit/parent_states.dart';
import 'package:dio/dio.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import '../../data/models/parent_models/parent_data_model.dart';
import '../../data/models/parent_models/parent_model.dart';

class ParentCubit extends Cubit<ParentState> {
  static ParentCubit get(context) => BlocProvider.of(context);
  ParentCubit() : super(ParentInitialStates());

  final ParentRepo _parentRepo = ParentRepo();

  /// Data properties
  List<DataInfo> data = [];
  int _page = 1;
  int? last_pages, total, currentPage;
  bool hasMoreData = false;
  bool isLoading = false; // Prevent duplicate API calls
  ParentDataModel? parentDataModel;
  List<DataInfo> searchData = [];

  /// Initializes the scroll controller and adds a listener for pagination.
  void initScrollController({
    bool isFirst = false,
    required ScrollController scrollController,
    Function()? setStates,
  }) {
    scrollController.addListener(() {
      if (scrollController.position.pixels >= scrollController.position.maxScrollExtent) {
        if (currentPage != null &&
            last_pages != null &&
            currentPage! < last_pages!) {
          hasMoreData = true;
          _page++;
          getParent(pageNumber: _page, isFirst: false);
          if (setStates != null) setStates();
        } else {
          hasMoreData = false;
          if (setStates != null) setStates();
        }
      }
    });
  }

  /// Fetches parent data for the given page.
  Future<void> getParent({
    int? pageNumber,
    bool isFirst = false,
  }) async {
    // Prevent multiple simultaneous calls.
    if (isLoading) return;
    isLoading = true;

    emit(ParentLoadingStates());

    // Reset data if this is the first request.
    if (isFirst) {
      data = [];
      _page = 1;
    }

    try {
      final response = await _parentRepo.repo(pageNumber: pageNumber);

      if (response.status == true) {
        // Update pagination information.
        last_pages = response.data!.lastPage;
        currentPage = response.data!.currentPage;
        data.addAll(response.data!.data!);

        emit(ParentSuccessStates());
      } else {
        _handleError(response.message ?? "Unknown error occurred");
      }
    } catch (e, stackTrace) {
      _handleError("An unexpected error occurred: $e", stackTrace);
    }

    isLoading = false;
  }

  /// Helper method to handle errors and emit error states.
  void _handleError(String errorMessage, [StackTrace? stackTrace]) {
    debugPrint("Error: $errorMessage");
    if (stackTrace != null) {
      debugPrint("Stack trace: $stackTrace");
    }
    emit(ParentErrorStates(error: errorMessage));
    isLoading = false;
  }

  /// Fetches detailed parent data for a given parent ID.
  Future<void> getParentData({ int? parentId }) async {
    emit(ParentDataLoadingStates());

    try {
      final response = await _parentRepo.parentDataRepo(parentId: parentId);

      if (response.status == true) {
        debugPrint(response.toString());
        parentDataModel = response;
        emit(ParentSuccessStates());
      } else {
        debugPrint("ErrorState: ${response.message}");
        emit(ParentErrorStates(error: response.message ?? "Unknown error occurred"));
      }
    } on DioException catch (e, stackTrace) {
      debugPrint("DioError in getParentData: ${e.message}");
      debugPrint("DioError in getParentData: $stackTrace");
      emit(ParentErrorStates(error: "Network error: ${e.message}"));
    } catch (e, stackTrace) {
      debugPrint("Unexpected error in getParentData: $e");
      debugPrint("Stack trace: $stackTrace");
      emit(ParentErrorStates(error: "An unexpected error occurred: $e"));
    }
  }
}
