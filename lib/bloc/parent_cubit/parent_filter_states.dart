
abstract class ParentFilterState {}

class ParentFilterInitialStates extends ParentFilterState {}

class ParentDataLoadingStates extends ParentFilterState {}

class ParentFilterLoadingStates extends ParentFilterState {}

class ParentFilterSuccessStates extends ParentFilterState {}

class ParentFilterErrorStates extends ParentFilterState {
  final String? error;
  ParentFilterErrorStates({
    this.error,
  });
}

