import 'package:bus/data/models/parent_models/parent_model.dart';

abstract class ParentState {}

class ParentInitialStates extends ParentState {}

class ParentLoadingStates extends ParentState {}

class ParentSuccessStartInitializingStates extends ParentState {
  final List<DataInfo>? data;
  int? last_page, current_page;
  ParentSuccessStartInitializingStates({
    this.current_page,
    this.last_page,
    this.data,
  });
}

class ParentSuccessStates extends ParentState {}

class ParentErrorStates extends ParentState {
  final String? error;
  ParentErrorStates({
    this.error,
  });
}


class ParentDataLoadingStates extends ParentState {}



