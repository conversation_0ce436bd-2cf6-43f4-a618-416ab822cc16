import 'package:bus/data/models/parent_models/parent_model.dart';
import 'package:bus/helper/network_serviecs.dart';
import 'package:dio/dio.dart';
import 'package:flutter/foundation.dart';

import '../../config/config_base.dart';
import '../../data/models/parent_models/parent_data_model.dart';

class ParentRepo {
  final _dio = NetworkService();

  Future<ParentModels> repo({
    String? parentName,
    int? pageNumber,
  }) async {
    try {
      // Log the input parameters
      debugPrint(
          "Input parameters - parentName: $parentName, pageNumber: $pageNumber");

      // Construct the API URL with query parameters
      final url =
          "${ConfigBase.baseUrl}parents/index?page=$pageNumber&limit=10&text=$parentName";
      debugPrint("Constructed URL: $url");

      // Make the API request
      final response = await _dio.get(
        url: url,
        isAuth: true,
      );

      // Log the response status code for debugging
      debugPrint("Parent from repo ${response.statusCode}");

      // Parse the response data into a ParentModels object
      final parentModels = ParentModels.fromJson(response.data);
      debugPrint("Parsed ParentModels: $parentModels");

      // Return the parsed data
      return parentModels;
    } on DioException catch (e) {
      // Handle Dio-specific errors (e.g., network issues, timeouts)
      debugPrint("DioError in repo: ${e.message}");
      return ParentModels(); // Return an empty object as a fallback
    } catch (e, stackTrace) {
      // Handle other unexpected errors (e.g., JSON parsing errors)
      debugPrint("Unexpected error in repo: $e");
      debugPrint("Stack trace: $stackTrace");
      return ParentModels(); // Return an empty object as a fallback
    }
  }

  Future<ParentDataModel> parentDataRepo({int? parentId}) async {
    try {
      // Construct the API URL
      final url = '${ConfigBase.parentShow}/$parentId';

      // Make the API request
      final response = await _dio.get(
        url: url,
        isAuth: true,
      );

      // Log the response data for debugging
      // Logger().w(response.data);

      // Parse the response data into a ParentDataModel object
      final parentDataModel = ParentDataModel.fromMap(response.data);

      // Return the parsed data
      return parentDataModel;
    } on DioException catch (e, stackTrace) {
      // Handle Dio-specific errors (e.g., network issues, timeouts)
      debugPrint("DioError in parentDataRepo: ${e.message}");
      debugPrint("Stack trace: $stackTrace");
      return ParentDataModel(message: "Network error: ${e.message}");
    } catch (e, stackTrace) {
      // Handle other unexpected errors (e.g., JSON parsing errors)
      debugPrint("Unexpected error in parentDataRepo: $e");
      debugPrint("Stack trace: $stackTrace");
      return ParentDataModel(message: "An unexpected error occurred: $e");
    }
  }
}
