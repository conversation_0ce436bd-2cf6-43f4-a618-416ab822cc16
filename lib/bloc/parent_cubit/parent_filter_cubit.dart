import 'package:bus/bloc/parent_cubit/parent_filter_states.dart';
import 'package:bus/bloc/parent_cubit/parent_repo.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import '../../data/models/parent_models/parent_model.dart';

class ParentFilterCubit extends Cubit<ParentFilterState> {
  static ParentFilterCubit get(context) => BlocProvider.of(context);
  ParentFilterCubit() : super(ParentFilterInitialStates());
  final _ParentRepo = ParentRepo();

  List<DataInfo> searchData = [];

  Future<void> getParentWithFilters({
    int? pageNumber,
    String? parentName,
  }) async {
    emit(ParentFilterLoadingStates());
    searchData = [];
    try {
      final responses = await _ParentRepo.repo(
          pageNumber: pageNumber, parentName: parentName);

      if (responses.status == true) {
        searchData.addAll(responses.data!.data!);
        // Logger().w(searchData);
        emit(ParentFilterSuccessStates());
      } else {
        emit(ParentFilterErrorStates(error: "response.message"));
      }
    } catch (e) {
      debugPrint("catch error $e");
      emit(ParentFilterErrorStates(error: e.toString()));
    }
  }
}
