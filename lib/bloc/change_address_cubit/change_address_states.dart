import 'dart:convert';

abstract class CAdreessStates {}

class CAdreessInitialStates extends CAdreessStates {}

class CAdreessLoadingStates extends CAdreessStates {}

class CAdreessSuccessStates extends CAdreessStates {
  final List<CAdreessModels?>? cAdreessModels2;
  final curentPage;
  final last_page;
  CAdreessSuccessStates({
    this.last_page,
    this.curentPage = 1,
    this.cAdreessModels2,
  });
}

class CAdreessErrorStates extends CAdreessStates {
  final String? error;
  CAdreessErrorStates({this.error});
}

class CAdreessModels {
  String? old_address;
  String? old_latitude;
  String? old_longitude;
  String? address;
  String? longitude;
  String? latitude;
  String? parentName;
  String schoolName;
  String studentName;
  String gradeName;
  String busName;
  int status;
    StatusText? statusText;


  int id;
  int? my__parent_id;
  int? school_id;
  int? bus_id;

  CAdreessModels({

    required this.status,
    required this.id,
    required this.gradeName,
    required this.busName,
    required this.studentName,
    this.my__parent_id,
    this.school_id,
    this.bus_id,
    this.parentName,
    required this.schoolName,
    this.old_address,
    this.old_latitude,
    this.old_longitude,
    this.address,
    this.longitude,
    this.latitude,
      this.statusText
  });

  Map<String, dynamic> toMap() {
    return {
      'old_address': old_address,
      'old_latitude': old_latitude,
      'old_longitude': old_longitude,
      'address': address,
      'longitude': longitude,
      'latitude': latitude,
      'my__parent_id': my__parent_id,
      'school_id': school_id,
      'bus_id': bus_id,
    };
  }

  factory CAdreessModels.fromMap(Map<String, dynamic> map) {
    return CAdreessModels(
        status: map['status'],
         statusText :map['status_text'] != null
        ?  StatusText.fromJson(map['status_text'])
        : null,
        id: map['id'],
        gradeName: map['grade']?["name"] ?? "",
        old_address: map['old_address'],
        old_latitude: map['old_latitude'],
        old_longitude: map['old_longitude'],
        address: map['address'],
        longitude: map['longitude'],
        latitude: map['latitude'],
        my__parent_id: int.tryParse(map['my__parent_id'].toString()),
        school_id: int.tryParse(map['school_id'].toString()),
        bus_id: int.tryParse(map['bus_id'].toString()),
        schoolName: map['schools']?["name"] ?? "",
        studentName: map['students']?["name"] ?? "",
        busName: map['bus']?["name"] ?? "",
        parentName: map['parent']?["name"] ?? "");
  }

  String toJson() => json.encode(toMap());

  factory CAdreessModels.fromJson(String source) =>
      CAdreessModels.fromMap(json.decode(source));

  @override
  String toString() {
    return 'CAdreessModels(old_address: $old_address, old_latitude: $old_latitude, old_longitude: $old_longitude, address: $address, longitude: $longitude, latitude: $latitude, my__parent_id: $my__parent_id, school_id: $school_id, bus_id: $bus_id,status_text:$statusText)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;

    return other is CAdreessModels &&
        other.old_address == old_address &&
        other.old_latitude == old_latitude &&
        other.old_longitude == old_longitude &&
        other.address == address &&
        other.longitude == longitude &&
        other.latitude == latitude &&
        other.my__parent_id == my__parent_id &&
        other.school_id == school_id &&
        other.bus_id == bus_id&&
         other.statusText == statusText;
  }

  @override
  int get hashCode {
    return old_address.hashCode ^
        old_latitude.hashCode ^
        old_longitude.hashCode ^
        address.hashCode ^
        longitude.hashCode ^
        latitude.hashCode ^
        my__parent_id.hashCode ^
        school_id.hashCode ^
         statusText.hashCode ^
        bus_id.hashCode;
        
  }
}
class StatusText {
  String? text;
  String? color;

  StatusText({this.text, this.color});

  StatusText.fromJson(Map<String, dynamic> json) {
    text = json['text'];
    color = json['color'];
  }}
