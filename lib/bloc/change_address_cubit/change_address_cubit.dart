import 'package:bus/bloc/change_address_cubit/change_address_states.dart';
import 'package:bus/data/repo/change_adress_requests_repo.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

class CAdreessCubit extends Cubit<CAdreessStates> {
  final _absencesRepo = CAdreessRepo();
  CAdreessCubit() : super(CAdreessInitialStates());

  Future<void> getCAdreess({int? pageNumber, String? status}) async {
    emit(CAdreessLoadingStates());
    try {
      final response = await _absencesRepo.repo(pageNumber: pageNumber, status: status);
      if (response.length > 0) {
        emit(CAdreessSuccessStates(
            cAdreessModels2: response[0],
            curentPage: response[1],
            last_page: response[2]));
      } else {
        emit(CAdreessErrorStates(error: "No Requests found."));
      }
    } catch (e) {
      if (kDebugMode) {
        debugPrint("catch error $e");
      }
      emit(CAdreessErrorStates(error: e.toString()));
    }
  }
}
