import 'dart:io';
import 'package:bus/bloc/add_suporvisor_cubit/add_supervisor_states.dart';
import 'package:bus/data/repo/add_supervisor_repo.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

class AddSupervisorCubit extends Cubit<AddSupervisorStates> {
  final _addSupervisorRepo = AddSupervisorRepo();

  AddSupervisorCubit() : super(AddSupervisorInitialStates());

  Future<void> addSupervisor({
    String? name,
    String? fileName,
    String? username,
    int? genderId,
    int? religionId,
    int? typeBloodId,
    int? busId,
    String? birthDate,
    String? password,
    String? password_confirmation,
    String? address,
    String? cityName,
    String? phone,
    File? image,
  }) async {
    emit(AddSupervisorLoadingStates());
    try {
      final response = await _addSupervisorRepo.repo(
        name: name,
        image: image,
        password: password,
        password_confirmation: password_confirmation,
        phone: phone,
        cityName: cityName,
        address: address,
        genderId: genderId,
        username: username,
        birthDate: birthDate,
        typeBloodId: typeBloodId,
        busId: busId,
        religionId: religionId,
      );

      if (response.errors == false) {
        emit(AddSupervisorSuccessStates(addDriverModels: response));
      } else {
        emit(AddSupervisorErrorStates(error: response.messages));
      }
    } catch (e, s) {
      debugPrint("catch error $s");
      debugPrint("catch error $e");
      emit(AddSupervisorErrorStates(error: e.toString()));
    }
  }
}
