import 'package:bus/data/models/student_models/student_model.dart';

abstract class StudentBusStates {}

class StudentBusInitialStates extends StudentBusStates {}

class StudentBusLoadingStates extends StudentBusStates {
  final int current_page;
  StudentBusLoadingStates({this.current_page = 1});
}


class StudentBusSuccessStates extends StudentBusStates {
  List<StudentModel?>? studentModel;
  int? last_page, current_page;
  StudentBusSuccessStates({
    this.last_page,
    this.current_page,
    this.studentModel,
  });
}

class StudentBusErrorStates extends StudentBusStates {
  final String? error;
  StudentBusErrorStates({
    this.error,
  });
}
