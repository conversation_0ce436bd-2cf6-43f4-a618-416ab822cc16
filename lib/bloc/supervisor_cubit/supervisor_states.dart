import 'package:bus/data/models/supervisor_mdodels/supervisor_models.dart';

abstract class SupervisorStates {}

class SupervisorInitialStates extends SupervisorStates {}

class SupervisorLoadingStates extends SupervisorStates {}

class SupervisorSuccessStates extends SupervisorStates {
  final SupervisorModels? supervisorModels;
  SupervisorSuccessStates({
    this.supervisorModels,
  });
}

class SupervisorErrorStates extends SupervisorStates {
  final String? error;
  SupervisorErrorStates({
    this.error,
  });
}
