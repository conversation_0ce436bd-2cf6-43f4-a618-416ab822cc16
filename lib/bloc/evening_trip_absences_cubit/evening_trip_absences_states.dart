import 'package:bus/data/models/trip_models/evening_trip_absences_model.dart';
import 'package:equatable/equatable.dart';

abstract class EveningTripAbsencesState extends Equatable {
  const EveningTripAbsencesState();

  @override
  List<Object?> get props => [];
}

class EveningTripAbsencesInitialState extends EveningTripAbsencesState {}

class EveningTripAbsencesLoadingState extends EveningTripAbsencesState {}

class EveningTripAbsencesSuccessState extends EveningTripAbsencesState {
  final EveningTripAbsencesResponse response;

  const EveningTripAbsencesSuccessState({required this.response});

  @override
  List<Object?> get props => [response];
}

class EveningTripAbsencesErrorState extends EveningTripAbsencesState {
  final String error;

  const EveningTripAbsencesErrorState({required this.error});

  @override
  List<Object?> get props => [error];
}
