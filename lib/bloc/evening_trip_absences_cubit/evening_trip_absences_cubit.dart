import 'package:bus/bloc/evening_trip_absences_cubit/evening_trip_absences_states.dart';
import 'package:bus/data/models/trip_models/evening_trip_absences_model.dart';
import 'package:bus/data/repo/evening_trip_absences_repo.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

class EveningTripAbsencesCubit extends Cubit<EveningTripAbsencesState> {
  EveningTripAbsencesCubit() : super(EveningTripAbsencesInitialState());
  final _eveningTripAbsencesRepo = EveningTripAbsencesRepo();

  static EveningTripAbsencesCubit get(context) => BlocProvider.of(context);

  EveningTripAbsencesResponse? absencesResponse;
  List<EveningTripAbsentStudent> absentStudents = [];

  /// Fetches students absent from the evening trip
  Future<void> getAbsentStudents({required String busId}) async {
    emit(EveningTripAbsencesLoadingState());
    try {
      final response = await _eveningTripAbsencesRepo.getAbsentStudents(busId: busId);
      
      if (response.errors == false) {
        absencesResponse = response;
        absentStudents = response.absences ?? [];
        emit(EveningTripAbsencesSuccessState(response: response));
      } else {
        emit(EveningTripAbsencesErrorState(error: response.message ?? "Unknown error"));
      }
    } catch (e, s) {
      debugPrint("Error in cubit: $e");
      debugPrint("Stack trace: $s");
      emit(EveningTripAbsencesErrorState(error: e.toString()));
    }
  }
}
