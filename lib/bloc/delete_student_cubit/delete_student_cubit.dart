import 'package:bus/bloc/delete_student_cubit/delete_student_states.dart';
import 'package:bus/data/repo/delete_student_repo.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

class DeleteStudentCubit extends Cubit<DeleteStudentStates> {
  final _deleteStudentRepo = DeleteStudentRepo();
  DeleteStudentCubit() : super(DeleteStudentInitialStates());

  Future<void> deleteStudent({
    String? id,
  }) async {
    emit(DeleteStudentLoadingStates());
    try {
      final response = await _deleteStudentRepo.repo(id: id);
      if (response["errors"] == true) {
        emit(DeleteStudentErrorStates(error: response['message']));
      } else {
        emit(DeleteStudentSuccessStates(deleteStudent: response));
      }
    } catch (e, s) {
      debugPrint("catch error $s");
      debugPrint("catch error $e");
      emit(DeleteStudentErrorStates(error: e.toString()));
    }
  }
}
