abstract class DeleteStudentStates {}

class DeleteStudentInitialStates extends DeleteStudentStates {}

class DeleteStudentLoadingStates extends DeleteStudentStates {}

class DeleteStudentSuccessStates extends DeleteStudentStates {
  final Map<String, dynamic>? deleteStudent;
  DeleteStudentSuccessStates({
    this.deleteStudent,
  });
}

class DeleteStudentErrorStates extends DeleteStudentStates {
  final String? error;
  DeleteStudentErrorStates({
    this.error,
  });
}
