part of 'notifications_cubit.dart';

abstract class NotificationsState extends Equatable {
  const NotificationsState();
}

class NotificationsInitial extends NotificationsState {
  @override
  List<Object> get props => [];
}

class NotificationsLoading extends NotificationsState{
  @override
  List<Object> get props => [];
}

class NotificationsSuccess extends NotificationsState{
  @override
  List<Object> get props => [];
}

class NotificationsError extends NotificationsState{
  @override
  List<Object> get props => [];
}
