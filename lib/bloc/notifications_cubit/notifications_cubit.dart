import 'package:bus/data/models/notifications_models/notifications_model.dart';
import 'package:equatable/equatable.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import '../../../data/repo/notifications_repo.dart';
import '../../data/models/notifications_models/fcm_token_model.dart';

part 'notifications_state.dart';

class NotificationsCubit extends Cubit<NotificationsState> {
  NotificationsCubit() : super(NotificationsInitial());
  static NotificationsCubit get(context) => BlocProvider.of(context);
  final _notificationsRepo = NotificationsRepo();

  AllNotificationsModel1? notifications;
  FcmTokenModel? allParentsFcmTokens;
  FcmTokenModel? parentFcmToken;
  FcmTokenModel? supervisorsFcmTokens;
  List<NotificationAllData>? notificationData = [];
  int _page = 1;
  int? last_page;
  int? total;
  int? currentPage;
  bool? hasMoreData = false;

  void initScrollController({
    bool? isFirst = false,
    ScrollController? scrollController,
    Function()? setStates,
  }) {
    scrollController!.addListener(() {
      if (scrollController.position.maxScrollExtent ==
          scrollController.offset) {
        if (currentPage! < last_page!) {
          hasMoreData = true;
          _page++;
          getNotifications(
            page: _page,
            isFirst: false,
          );
          setStates!();
        } else {
          hasMoreData = false;
          setStates!();
        }
      }
    });
  }

  Future<void> getNotifications({
    int? page = 1,
    bool? isFirst = false,
  }) async {
    emit(NotificationsLoading());
    if (isFirst == true) {
      notificationData = [];
      _page = 1;
    }
    try {
      final response = await _notificationsRepo.notifications(page: page);

      notifications = response;
      total = response.total;
      currentPage = response.currentPage;
      last_page = response.lastPage;
      notificationData!.addAll(response.data!);
      emit(NotificationsSuccess());
    } catch (e, s) {
      debugPrint("catch error $s");
      debugPrint("catch error at repo $e");
      debugPrint("catch error $e");
      emit(NotificationsError());
    }
  }

  Future<void> getAllParentsFcmTokens() async {
    emit(NotificationsLoading());
    try {
      final response = await _notificationsRepo.allParentsFcmTokens();
      if (response.status == true) {
        debugPrint(response.data.toString());
        allParentsFcmTokens = response;
        emit(NotificationsSuccess());
      } else {
        debugPrint("ErrorState: ${response.message}");
        emit(NotificationsError());
      }
    } catch (e) {
      debugPrint("getAllParentsFcmTokens catch error $e");
      emit(NotificationsError());
    }
  }

  Future<void> getParentFcmToken({int? studentId}) async {
    emit(NotificationsLoading());
    try {
      final response =
          await _notificationsRepo.parentFcmTokens(studentId: studentId);
      if (response.status == true) {
        debugPrint(response.data.toString());
        parentFcmToken = response;
        emit(NotificationsSuccess());
      } else {
        debugPrint("ErrorState: ${response.message}");
        emit(NotificationsError());
      }
    } catch (e, s) {
      debugPrint("catch error $s");
      debugPrint("catch error at repo $e");
      debugPrint("getParentFcmToken catch error $e");
      emit(NotificationsError());
    }
  }

  // Future<void> getSuperVisorsFcmToken({String? busId}) async {
  //   emit(NotificationsLoading());
  //   try {
  //     final response =
  //         await _notificationsRepo.supervisorFcmToken(busId: busId);
  //     if (response.status == true) {
  //       debugPrint(response.data.toString());
  //       supervisorsFcmTokens = response;
  //     } else {
  //       debugPrint("ErrorState: ${response.message}");
  //     }
  //   } catch (e) {
  //     debugPrint("getSuperVisorsFcmToken catch error $e");
  //   }
  // }
}
