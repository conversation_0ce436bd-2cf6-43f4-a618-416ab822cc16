import 'package:bus/bloc/grade_cubit/grade_states.dart';
import 'package:bus/data/repo/grade_repo.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

class GradeCubit extends Cubit<GradeStates> {
  final _gradeRepo = GradeRepo();
  GradeCubit() : super(GradeInitialStates());

  Future<void> getGrade() async {
    emit(GradeLoadingStates());
    try {
      final response = await _gradeRepo.repo();
      if (response.status == true) {
        emit(GradeSuccessStates(gradeModels: response));
      } else {
        emit(GradeErrorStates(error: response.message));
      }
    } catch (e, s) {
      debugPrint("catch error $s");
      debugPrint("catch error $e");
      emit(GradeErrorStates(error: e.toString()));
    }
  }
}
