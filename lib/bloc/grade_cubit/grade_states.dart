import 'package:bus/data/models/grade_models/grade_models.dart';

abstract class GradeStates {}

class GradeInitialStates extends GradeStates {}

class GradeLoadingStates extends GradeStates {}

class GradeSuccessStates extends GradeStates {
  final GradeModels? gradeModels;
  GradeSuccessStates({
    this.gradeModels,
  });
}

class GradeErrorStates extends GradeStates {
  final String? error;
  GradeErrorStates({
    this.error,
  });
}
