import 'package:bus/bloc/notification_settings_cubit/notification_settings_states.dart';
import 'package:bus/data/repo/notification_settings_repo.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

class NotificationSettingsCubit extends Cubit<NotificationSettingsStates> {
  final _notificationSettingsRepo = NotificationSettingsRepo();
  
  NotificationSettingsCubit() : super(NotificationSettingsInitialStates());

  static NotificationSettingsCubit get(context) => BlocProvider.of(context);

  Future<void> updateNotificationSettings({
    required bool tripStartEndNotificationStatus,
    required bool studentAbsenceNotificationStatus,
    required bool studentAddressNotificationStatus,
  }) async {
    emit(NotificationSettingsLoadingStates());
    
    try {
      final response = await _notificationSettingsRepo.updateNotificationSettings(
        tripStartEndNotificationStatus: tripStartEndNotificationStatus,
        studentAbsenceNotificationStatus: studentAbsenceNotificationStatus,
        studentAddressNotificationStatus: studentAddressNotificationStatus,
      );
      
      if (response.status == true) {
        emit(NotificationSettingsSuccessStates(notificationSettingsModel: response));
      } else {
        emit(NotificationSettingsErrorStates(error: response.message));
      }
    } catch (e, s) {
      debugPrint("catch error $s");
      debugPrint("catch error $e");
      emit(NotificationSettingsErrorStates(error: e.toString()));
    }
  }
}
