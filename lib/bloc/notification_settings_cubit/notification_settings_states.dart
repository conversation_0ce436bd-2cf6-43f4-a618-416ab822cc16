import 'package:bus/data/models/notification_settings_models/notification_settings_model.dart';

abstract class NotificationSettingsStates {}

class NotificationSettingsInitialStates extends NotificationSettingsStates {}

class NotificationSettingsLoadingStates extends NotificationSettingsStates {}

class NotificationSettingsSuccessStates extends NotificationSettingsStates {
  final NotificationSettingsModel notificationSettingsModel;

  NotificationSettingsSuccessStates({required this.notificationSettingsModel});
}

class NotificationSettingsErrorStates extends NotificationSettingsStates {
  final String? error;

  NotificationSettingsErrorStates({this.error});
}
