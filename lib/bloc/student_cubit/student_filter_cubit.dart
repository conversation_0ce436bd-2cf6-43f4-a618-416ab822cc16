import 'package:bus/bloc/student_cubit/student_filter_states.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import '../../data/repo/student_repo.dart';

class StudentFilterCubit extends Cubit<StudentFilterState> {
  static StudentFilterCubit get(context) => BlocProvider.of(context);
  StudentFilterCubit() : super(StudentFilterInitialStates());

  final StudentRepo _studentRepo = StudentRepo();

  Future<void> getStudentWithFilters({
    int pageNumber = 1,
    String studentName = '',
  }) async {
    emit(StudentFilterLoadingStates());
    try {
      final response = await _studentRepo.repo(
        pageNumber: pageNumber,
        studentName: studentName,
      );
      if (response.status == true) {
        // Assuming response.studentModels is a List<StudentModel?>
        emit(StudentFilterSuccessStates(searchData: response.studentModels));
      } else {
        emit(StudentFilterErrorStates(error: response.toString()));
      }
    } catch (e) {
      debugPrint("Error in getStudentWithFilters: $e");
      emit(StudentFilterErrorStates(error: e.toString()));
    }
  }

  
}
