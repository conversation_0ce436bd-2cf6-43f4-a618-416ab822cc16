import '../../data/models/student_models/student_model.dart';

abstract class StudentFilterState {}

class StudentFilterInitialStates extends StudentFilterState {}

class StudentFilterLoadingStates extends StudentFilterState {}

class StudentFilterSuccessStates extends StudentFilterState {
  final List<StudentModel?> searchData;
  StudentFilterSuccessStates({required this.searchData});
}

class StudentFilterErrorStates extends StudentFilterState {
  final String? error;
  StudentFilterErrorStates({this.error});
}
