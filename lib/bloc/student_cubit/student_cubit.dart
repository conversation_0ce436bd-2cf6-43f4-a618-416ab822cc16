import 'package:bus/bloc/student_cubit/student_states.dart';
import 'package:bus/data/repo/student_repo.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import '../../data/models/student_models/student_model.dart';

class StudentCubit extends Cubit<StudentState> {
  final _studentRepo = StudentRepo();
  List<StudentModel?>? studentModel;
  List<StudentModel?>? sons;
  int? last_page, current_page;
  StudentCubit() : super(StudentInitialStates());
  static StudentCubit get(context) => BlocProvider.of(context);

  List<StudentModel> data = [];
  int _page = 1;
  int? last_pages, total, currentPage;
  bool? hasMoreData = false;

  void initScrollController({
    bool? isFirst = false,
    ScrollController? scrollController,
    Function()? setStates,
  }) {
    scrollController!.addListener(() {
      if (scrollController.position.maxScrollExtent ==
          scrollController.offset) {
        if (currentPage! < last_pages!) {
          hasMoreData = true;
          _page++;
          getStudent(
            page: _page,
            isFirst: false,
          );
          setStates!();
        } else {
          hasMoreData = false;
          setStates!();
        }
      }
    });
  }

  Future<void> getStudent({
    int? page = 1,
    //  int? bus_Id,

    bool? isFirst = false,
  }) async {
    emit(StudentLoadingStates());
    if (isFirst == true) {
      data = [];
      _page = 1;
    }
    try {
      final response = await _studentRepo.repo(
        pageNumber: page,
      );
      if (response.studentModels.isNotEmpty) {
        studentModel = response.studentModels;
        current_page = response.currentPage;
        last_page = response.lastPage;
        last_pages = response.lastPage;
        currentPage = response.currentPage;
        data.addAll(response.studentModels);
        emit(StudentSuccessStates());
      } else {
        emit(StudentErrorStates(error: "response.message"));
      }
    } catch (e) {
      debugPrint("catch error $e");
      emit(StudentErrorStates(error: e.toString()));
    }
  }

  Future<void> getStudentWithBusId({int? busId}) async {
    emit(StudentLoadingStates());
    try {
      final response = await _studentRepo.busStudentsRepo(busId: busId);

      if (response.studentModels.isNotEmpty) {
        emit(StudentSuccessStates());
        sons = response.studentModels;
      } else {
        emit(StudentErrorStates(error: "response.message"));
      }
    } catch (e) {
      debugPrint("catch error $e");
      emit(StudentErrorStates(error: e.toString()));
    }
  }

  Future<void> getStudentWithParentId({int? parentId}) async {
    emit(StudentLoadingStates());
    try {
      final response =
          await _studentRepo.parentStudentsRepo(parentId: parentId);

      if (response.studentModels.isNotEmpty) {
        emit(StudentSuccessStates());
        sons = response.studentModels;
      } else {
        emit(StudentErrorStates(error: "response.message"));
      }
    } catch (e, s) {
      debugPrint("catch error $s");
      debugPrint("catch error $e");
      emit(StudentErrorStates(error: e.toString()));
    }
  }
}
