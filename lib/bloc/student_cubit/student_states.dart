import 'package:bus/data/models/student_models/student_model.dart';

abstract class StudentState {}

class StudentInitialStates extends StudentState {}

class StudentLoadingStates extends StudentState {}

class StudentSuccessStartInitializingStates extends StudentState {
  final List<StudentModel?>? studentModels;
  int? last_page, current_page;
  StudentSuccessStartInitializingStates({
    this.current_page,
    this.last_page,
    this.studentModels,
  });
}

class StudentSuccessStates extends StudentState {}

class StudentNoStudentsStates extends StudentState {
  final String? error;
  StudentNoStudentsStates({
    this.error,
  });
}

class StudentErrorStates extends StudentState {
  final String? error;
  StudentErrorStates({
    this.error,
  });
}
