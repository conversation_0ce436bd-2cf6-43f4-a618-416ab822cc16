import 'package:bus/data/models/trip_models/previous_trip_model.dart';
import 'package:equatable/equatable.dart';

abstract class PreviousTripsStates extends Equatable {
  const PreviousTripsStates();

  @override
  List<Object?> get props => [];
}

class PreviousTripsInitialState extends PreviousTripsStates {}

class PreviousTripsLoadingState extends PreviousTripsStates {}

class PreviousTripsSuccessState extends PreviousTripsStates {
  final List<PreviousTripModel> trips;

  const PreviousTripsSuccessState({required this.trips});

  @override
  List<Object?> get props => [trips];
}

class PreviousTripsErrorState extends PreviousTripsStates {
  final String error;

  const PreviousTripsErrorState({required this.error});

  @override
  List<Object?> get props => [error];
}

class PreviousTripsPaginationSuccessState extends PreviousTripsStates {
  final List<PreviousTripModel> trips;

  const PreviousTripsPaginationSuccessState({required this.trips});

  @override
  List<Object?> get props => [trips];
}

class PreviousTripsFilterLoadingState extends PreviousTripsStates {}

class PreviousTripsFilterSuccessState extends PreviousTripsStates {
  final List<PreviousTripModel> trips;

  const PreviousTripsFilterSuccessState({required this.trips});

  @override
  List<Object?> get props => [trips];
}

class PreviousTripsFilterErrorState extends PreviousTripsStates {
  final String error;

  const PreviousTripsFilterErrorState({required this.error});

  @override
  List<Object?> get props => [error];
}
