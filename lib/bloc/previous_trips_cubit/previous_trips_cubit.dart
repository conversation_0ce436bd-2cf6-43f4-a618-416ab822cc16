import 'package:bus/bloc/previous_trips_cubit/previous_trips_states.dart';
import 'package:bus/data/models/trip_models/previous_trip_model.dart';
import 'package:bus/data/repo/previous_trips_repo.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

class PreviousTripsCubit extends Cubit<PreviousTripsStates> {
  final _previousTripsRepo = PreviousTripsRepo();

  PreviousTripsCubit() : super(PreviousTripsInitialState());

  static PreviousTripsCubit get(context) => BlocProvider.of(context);

  List<PreviousTripModel> trips = [];
  int currentPage = 1;
  int? lastPage;
  bool hasMoreData = false;
  bool isLoading = false;

  // Selected filters
  String? selectedBusId;
  String? selectedDate;

  void initScrollController({
    required ScrollController scrollController,
    required Function() setStates,
  }) {
    scrollController.addListener(() {
      if (scrollController.position.maxScrollExtent ==
          scrollController.offset) {
        if (lastPage != null && currentPage < lastPage!) {
          hasMoreData = true;
          currentPage++;
          getPreviousTrips(
            pageNumber: currentPage,
            isFirst: false,
          );
          setStates();
        } else {
          hasMoreData = false;
          setStates();
        }
      }
    });
  }

  Future<void> getPreviousTrips({
    int? pageNumber,
    bool isFirst = true,
    bool isFilter = false,
  }) async {
    if (isFirst) {
      emit(PreviousTripsLoadingState());
      currentPage = 1;
      trips = [];
    } else if (isFilter) {
      emit(PreviousTripsFilterLoadingState());
      currentPage = 1;
      trips = [];
    } else {
      isLoading = true;
    }

    try {
      // Fetch trips with the selected filters

      final response = await _previousTripsRepo.getPreviousTrips(
        pageNumber: pageNumber ?? currentPage,
        busId: selectedBusId,
        date: selectedDate,
      );

      if (response.status == true) {
        if (isFirst || isFilter) {
          trips = response.data ?? [];
        } else {
          trips.addAll(response.data ?? []);
        }

        // In a real implementation, you would get pagination info from the response
        // For now, we'll just assume there's only one page of data
        lastPage = 1;

        if (isFirst) {
          emit(PreviousTripsSuccessState(trips: trips));
        } else if (isFilter) {
          emit(PreviousTripsFilterSuccessState(trips: trips));
        } else {
          emit(PreviousTripsPaginationSuccessState(trips: trips));
        }
      } else {
        emit(PreviousTripsErrorState(
            error: response.message ?? "Unknown error"));
      }
    } catch (e) {
      emit(PreviousTripsErrorState(error: e.toString()));
    } finally {
      isLoading = false;
    }
  }

  void setFilters({String? busId, String? date}) {
    selectedBusId = busId;
    selectedDate = date;
    getPreviousTrips(isFilter: true);
  }

  void resetFilters() {
    // Reset filters to default values

    selectedBusId = null;
    selectedDate = null;

    // Filters have been reset

    getPreviousTrips(isFilter: true);
  }
}
