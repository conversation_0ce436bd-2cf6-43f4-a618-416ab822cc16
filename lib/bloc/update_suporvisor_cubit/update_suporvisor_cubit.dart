import 'package:bus/data/models/add_driver_models/add_driver_models.dart';


abstract class UpdateSupervisorStates {}

class UpdateSupervisorInitialStates extends UpdateSupervisorStates {}

class UpdateSupervisorLoadingStates extends UpdateSupervisorStates {}

class UpdateSupervisorSuccessStates extends UpdateSupervisorStates {
  final AddDriverModels? addDriverModels;
  UpdateSupervisorSuccessStates({
    this.addDriverModels,
  });
}

class UpdateSupervisorErrorStates extends UpdateSupervisorStates {
  final String? error;
  UpdateSupervisorErrorStates({
    this.error,
  });
}
