import 'dart:io';
import 'package:bus/bloc/update_suporvisor_cubit/update_suporvisor_cubit.dart';
import 'package:bus/data/repo/update_supervisor_repo.dart';
import 'package:dio/dio.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

class UpdateSupervisorCubit extends Cubit<UpdateSupervisorStates> {
  final _updateSupervisorRepo = UpdateSupervisorRepo();
  UpdateSupervisorCubit() : super(UpdateSupervisorInitialStates());

  Future<void> updateSupervisor(
      {String? name,
      MultipartFile? file__lol,
      String? fileName,
      String? username,
      int? genderId,
      int? religionId,
      int? typeBloodId,
      int? busId,
      String? birthDate,
      String? password,
      String? password_confirmation,
      String? address,
      String? cityName,
      String? phone,
      File? image,
      String? dateBirthS,
      int? supervisorId}) async {
    emit(UpdateSupervisorLoadingStates());
    try {
      final response = await _updateSupervisorRepo.repo(
        name: name,
        image: image,
        password: password,
        password_confirmation: password_confirmation,
        phone: phone,
        cityName: cityName,
        address: address,
        genderId: genderId,
        username: username,
        birthDate: birthDate,
        typeBloodId: typeBloodId,
        busId: busId,
        religionId: religionId,
        supervisorId: supervisorId,
      );
      if (response.errors == false) {
        // Logger().w(response.errors);
        emit(
          UpdateSupervisorSuccessStates(addDriverModels: response),
        );
      } else {
        emit(UpdateSupervisorErrorStates(error: response.messages));
      }
    } catch (e, s) {
      debugPrint("catch error $s");
      debugPrint("catch error $e");
      emit(UpdateSupervisorErrorStates(error: e.toString()));
    }
  }
}
