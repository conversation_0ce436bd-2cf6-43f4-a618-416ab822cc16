import 'package:bus/bloc/check_internet_cubit/check_internet_states.dart';
import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:internet_connection_checker/internet_connection_checker.dart';

class CheckInternetCubit extends Cubit<CheckInternetStates> {
  CheckInternetCubit() : super(CheckInternetInitialStates());
  bool disConnected = false;
  bool checkFromStream = false;

  /// check connectivity by streaming when app opened
  void checkConnectionStream({Object? data, BuildContext? context}) async {
    bool result = await InternetConnectionChecker.instance.hasConnection;
    if (data == ConnectivityResult.none) {
      disConnected = true;
      checkFromStream = true;
    } else {
      if (result == true) {
        disConnected = false;
      } else {
        disConnected = true;
        checkFromStream = true;
      }
    }
    if (disConnected == false && checkFromStream == true) {
      /// write something here
      checkFromStream = false;
    }
    emit(CheckConnectionStreamStates());
  }

  /// check connectivity when app in background or terminated  check internet
  void checkConnectivity() async {
    var result = await Connectivity().checkConnectivity();
    if (result == ConnectivityResult.none) {
      disConnected = true;
    } else {
      bool result =
          await await InternetConnectionChecker.instance.hasConnection;
      if (result == true) {
        disConnected = false;
      } else {
        disConnected = true;
      }
    }
    emit(CheckConnectivityStates());
  }
}
