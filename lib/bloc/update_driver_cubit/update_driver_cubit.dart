import 'dart:io';

import 'package:bus/bloc/update_driver_cubit/update_driver_states.dart';
import 'package:bus/data/repo/update_driver_repo.dart';
import 'package:dio/dio.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

class UpdateDriverCubit extends Cubit<UpdateDriverStates> {
  final _updateDriverRepo = UpdateDriverRepo();
  UpdateDriverCubit() : super(UpdateDriverInitialStates());

  Future<void> updateDriver({
    String? name,
    MultipartFile? file__lol,
    String? fileName,
    String? username,
    int? genderId,
    int? religionId,
    int? typeBloodId,
    int? busId,
    String? birthDate,
    String? joiningDate,
    String? password,
    String? address,
    String? cityName,
    String? phone,
    File? image,
    String? joinDateBirthS,
    String? dateBirthS,
    int? driverId,
  }) async {
    emit(UpdateDriverLoadingStates());
    try {
      final response = await _updateDriverRepo.repo(
        name: name,
        image: image,
        password: password,
        phone: phone,
        cityName: cityName,
        address: address,
        genderId: genderId,
        username: username,
        birthDate: birthDate,
        typeBloodId: typeBloodId,
        busId: busId,
        religionId: religionId,
        joiningDate: joiningDate,
        driverId: driverId,
      );
      if (response.errors == false) {
        emit(
          UpdateDriverSuccessStates(addDriverModels: response),
        );
      } else {
        emit(UpdateDriverErrorStates(error: response.messages));
      }
    } catch (e, s) {
      debugPrint("catch error $s");
      debugPrint("catch error $e");
      emit(UpdateDriverErrorStates(error: e.toString()));
    }
  }
}
