import 'package:bus/data/models/add_driver_models/add_driver_models.dart';

abstract class UpdateDriverStates {}

class UpdateDriverInitialStates extends UpdateDriverStates {}

class UpdateDriverLoadingStates extends UpdateDriverStates {}

class UpdateDriverSuccessStates extends UpdateDriverStates {
  final AddDriverModels? addDriverModels;
  UpdateDriverSuccessStates({
    this.addDriverModels,
  });
}

class UpdateDriverErrorStates extends UpdateDriverStates {
  final String? error;
  UpdateDriverErrorStates({
    this.error,
  });
}
