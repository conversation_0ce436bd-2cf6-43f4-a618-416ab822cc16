import 'package:bus/data/models/file_name_download_models/file_name-download_models.dart';

abstract class FileNameDownloadStates {}

 class FileNameDownloadInitialStates extends FileNameDownloadStates {}

 class FileNameDownloadLoadingStates extends FileNameDownloadStates {}

 class FileNameDownloadSuccessStates extends FileNameDownloadStates {
  final FileNameDownloadModels? fileNameDownloadModels;
  FileNameDownloadSuccessStates({
    this.fileNameDownloadModels,
  });
}

 class FileNameDownloadErrorStates extends FileNameDownloadStates {
  final String? error;
  FileNameDownloadErrorStates({
    this.error,
  });
}
