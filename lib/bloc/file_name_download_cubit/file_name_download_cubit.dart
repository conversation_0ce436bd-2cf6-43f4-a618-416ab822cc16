import 'dart:developer';
import 'package:bus/data/repo/file_name_download_repo.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:logger/logger.dart';

import 'file_name_download_states.dart';

class FileNameDownloadCubit extends Cubit<FileNameDownloadStates> {
  final _fileNameDownloadRepo = FileNameDownloadRepo();
  FileNameDownloadCubit() : super(FileNameDownloadInitialStates());

  Future<void> getFileDownload() async {
    emit(FileNameDownloadLoadingStates());
    try {
      final response = await _fileNameDownloadRepo.repo();
      if (response.status == true) {
        log(response.data!);
        Logger().w(response.data);
        emit(FileNameDownloadSuccessStates(fileNameDownloadModels: response));
      } else {
        emit(FileNameDownloadErrorStates(error: response.message));
      }
    } catch (e, s) {
      debugPrint("catch error $s");
      debugPrint("catch error $e");
      emit(FileNameDownloadErrorStates(error: e.toString()));
    }
  }
}
