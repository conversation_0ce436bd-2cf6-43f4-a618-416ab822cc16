import 'package:bus/bloc/bus_location_tracker_cubit/bus_location_tracker_model.dart';
import 'package:bus/config/config_base.dart';
import 'package:bus/helper/network_serviecs.dart';
import 'package:flutter/material.dart';
import 'package:logger/logger.dart';

class BusLocationTrackerRepo {
  final _dio = NetworkService();

  Future<BusLocationTrackerModel?>? repo({
    int? busid,
  }) async {
    try {
      final _request = await _dio.get(
        url: "${ConfigBase.buslocationtracker}$busid",
        isAuth: true,
      );
        Logger().e(_request.data);

      // debugPrint("longitude" +
      //     _request.data["data"]["longitude"].toString() +
      //     "latitude" +
      //     _request.data["data"]["latitude"].toString());

      if (_request.statusCode == 200) {
        Logger().e(_request.data);
        
        return BusLocationTrackerModel(
            longitude: double.parse(_request.data["data"]["longitude"]),
            latitude: double.parse(_request.data["data"]["latitude"]));
            
      } else {
        return null;
      }
    } catch (e, s) {
      debugPrint("catch error $s");
      debugPrint("catch error at repo $e");
      return null;
    }
  }
}
