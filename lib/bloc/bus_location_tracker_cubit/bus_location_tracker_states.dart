import 'package:bus/bloc/bus_location_tracker_cubit/bus_location_tracker_model.dart';

abstract class BusLocationTrackerStates {}

class BusLocationTrackerInitialStates extends BusLocationTrackerStates {}

class BusLocationTrackerLoadingStates extends BusLoc<PERSON><PERSON>rackerStates {}

class BusLocationTrackerSuccessStates extends BusLocationTrackerStates {
  final BusLocationTrackerModel? BusLocationTrackerModels;
  BusLocationTrackerSuccessStates({this.BusLocationTrackerModels});
}

class BusLocationTrackerErrorStates extends BusLocationTrackerStates {
  final String? error;
  BusLocationTrackerErrorStates({
    this.error,
  });
}
