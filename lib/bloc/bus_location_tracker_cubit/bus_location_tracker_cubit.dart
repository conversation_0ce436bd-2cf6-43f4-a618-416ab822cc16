import 'package:bus/bloc/bus_location_tracker_cubit/bus_location_tracker_repo.dart';
import 'package:bus/bloc/bus_location_tracker_cubit/bus_location_tracker_states.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

class BusLocationTrackerCubit extends Cubit<BusLocationTrackerStates> {
  final _BusLocationTrackerRepo = BusLocationTrackerRepo();
  BusLocationTrackerCubit() : super(BusLocationTrackerInitialStates());

  Future<void> getBusLocationTracker({int? busid}) async {
    emit(BusLocationTrackerLoadingStates());
    try {
      final response = await _BusLocationTrackerRepo.repo(busid: busid);
      if (response != null) {
        emit(BusLocationTrackerSuccessStates(
            BusLocationTrackerModels: response));
      } else {
        emit(BusLocationTrackerErrorStates(error: "error"));
      }
    } catch (e, s) {
      debugPrint("catch error $s");
      debugPrint("catch error busetracker Cubits $e");
      emit(BusLocationTrackerErrorStates(error: e.toString()));
    }
  }
}
