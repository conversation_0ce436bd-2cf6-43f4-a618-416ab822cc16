import 'package:bus/data/models/user_models/user_models.dart';

abstract class ProfileHomeStates {}

class ProfileHomeInitialStates extends ProfileHomeStates {}

class ProfileHomeLoadingStates extends ProfileHomeStates {}

class ProfileHomeSuccessStates extends ProfileHomeStates {
  final UserModel? userModel;
  ProfileHomeSuccessStates({this.userModel,});
}

class ProfileHomeErrorStates extends ProfileHomeStates {
  final String? error;
  final bool? errors;
  ProfileHomeErrorStates({
    this.error,
    this.errors,
  });
}
