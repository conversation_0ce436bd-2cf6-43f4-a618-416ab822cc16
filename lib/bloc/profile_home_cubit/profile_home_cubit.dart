import 'package:bus/bloc/profile_home_cubit/profile_home_states.dart';
import 'package:bus/data/repo/user_repo.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

class ProfileHomeCubit extends Cubit<ProfileHomeStates> {
  final _profileHomeRepo = UserRepo();
  ProfileHomeCubit() : super(ProfileHomeInitialStates());

  Future<void> getProfileHome() async {
    emit(ProfileHomeLoadingStates());
    try {
      final response = await _profileHomeRepo.repoHome();
      if (response.success == true) {
        emit(ProfileHomeSuccessStates());
      } else {
        emit(
          ProfileHomeErrorStates(
            error: response.message,
            errors: response.errors,
          ),
        );
      }
    } catch (e, s) {
      debugPrint("catch error $s");
      debugPrint(e.toString());
      emit(ProfileHomeErrorStates());
    }
  }
}
