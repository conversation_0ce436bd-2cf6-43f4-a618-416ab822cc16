import 'package:bus/bloc/create_password_cubit/create_password_states.dart';
import 'package:bus/data/models/create_password/create_password_model.dart';
import 'package:bus/data/repo/create_password_repo.dart';
import 'package:bus/helper/data_state.dart';
import 'package:bus/helper/response_state.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

class CreatePasswordCubit extends Cubit<CreatePasswordStates> {
  final CreatePasswordRepo _createPasswordRepo;

  CreatePasswordCubit(this._createPasswordRepo) : super(const CreatePasswordStates());

  static CreatePasswordCubit get(context) => BlocProvider.of(context);

  Future<void> createPassword({
    String? newPassword,
    String? confirmedPassword,
  }) async {
    emit(state.copyWith(rStates: ResponseState.loading));

    DataState<CreatePasswordModel> response = await _createPasswordRepo.createPassword(
      newPassword: newPassword,
      confirmedPassword: confirmedPassword,
    );

    if (response is DataSuccess) {
      // Extract success message from the response
      final successMessage = response.data?.status?.messages ?? "تم تغيير كلمة المرور بنجاح";

      emit(
        state.copyWith(
          rStates: ResponseState.success,
          createPasswordModels: response.data,
          message: successMessage,
        ),
      );
    } else {
      emit(state.copyWith(
        rStates: ResponseState.failure,
        message: response.message
      ));
    }
  }
}
