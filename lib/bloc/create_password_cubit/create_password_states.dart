import 'package:bus/data/models/create_password/create_password_model.dart';
import 'package:bus/helper/response_state.dart';
import 'package:equatable/equatable.dart';

class CreatePasswordStates extends Equatable {
  final CreatePasswordModel? createPasswordModels;
  final ResponseState rStates;
  final String? message;

  const CreatePasswordStates({
    this.createPasswordModels,
    this.rStates = ResponseState.init,
    this.message
  });

  CreatePasswordStates copyWith({
    CreatePasswordModel? createPasswordModels,
    ResponseState? rStates,
    String? message
  }) {
    return CreatePasswordStates(
      createPasswordModels: createPasswordModels ?? this.createPasswordModels,
      rStates: rStates ?? this.rStates,
      message: message ?? this.message
    );
  }

  @override
  List<Object?> get props => [
        createPasswordModels,
        rStates,
        message,
      ];
}
