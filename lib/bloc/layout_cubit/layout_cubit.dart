import 'package:bus/views/screens/help_home_screen/help_home_screen.dart';
import 'package:bus/views/screens/home_screen/home_screen.dart';
import 'package:bus/views/screens/setting_screen/setting_screen.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import 'layout_states.dart';

class LayoutCubit extends Cubit<LayoutStates> {
  LayoutCubit() : super(LayoutInitial());

  static LayoutCubit get(context) => BlocProvider.of(context);

  int currentIndex = 1;
  int tab = 1;

  List<Widget> screens = [
    const SettingScreen(),
    const HomeScreen(),
    const HelpHomeScreen(),
  ];

  void changeBottomNavigationBar(index) {
    currentIndex = index;
    emit(LayoutBottomNavigationBar());
  }

  void changeTabBar(index) {
    tab = index;
    emit(LayoutTabBar());
  }

  void changeLanguages({String? language, BuildContext? context}) {
    context!.setLocale(Locale(language!));
    emit(LayoutChangeLanguages());
  }
}
