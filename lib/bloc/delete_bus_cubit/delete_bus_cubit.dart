import 'package:bus/bloc/delete_bus_cubit/delete_bus_states.dart';
import 'package:bus/data/repo/delete_bus_repo.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

class DeleteBusCubit extends Cubit<DeleteBusStates> {
  final _deleteBusRepo = DeleteBusRepo();
  DeleteBusCubit() : super(DeleteBusInitialStates());

  Future<void> deleteBus({
    int? id,
  }) async {
    emit(DeleteBusLoadingStates());
    try {
      final response = await _deleteBusRepo.repo(id: id);
      if (response["errors"] == true) {
        emit(DeleteBusErrorStates(error: response['message']));
      } else {
        emit(DeleteBusSuccessStates(deleteBus: response));
      }
    } catch (e, s) {
      debugPrint("catch error $s");
      debugPrint("catch error $e");
      emit(DeleteBusErrorStates(error: e.toString()));
    }
  }
}
