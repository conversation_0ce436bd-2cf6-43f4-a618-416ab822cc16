abstract class DeleteBusStates {}

class DeleteBusInitialStates extends DeleteBusStates {}

class DeleteBusLoadingStates extends DeleteBusStates {}

class DeleteBusSuccessStates extends DeleteBusStates {
  final Map<String, dynamic>? deleteBus;
  DeleteBusSuccessStates({
    this.deleteBus,
  });
}

class DeleteBusErrorStates extends DeleteBusStates {
  final String? error;
  DeleteBusErrorStates({
    this.error,
  });
}
