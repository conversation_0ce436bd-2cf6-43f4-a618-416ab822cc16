import 'package:bus/bloc/type_blood_cubit/type_blood_states.dart';
import 'package:bus/data/repo/type_blood_repo.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

class TypeBloodCubit extends Cubit<TypeBloodStates> {
  final _typeBloodRepo = TypeBloodRepo();
  TypeBloodCubit() : super(TypeBloodInitialStates());

  Future<void> getTypeBlood() async {
    emit(TypeBloodLoadingStates());
    try {
      final response = await _typeBloodRepo.repo();
      if (response.status == true) {
        emit(TypeBloodSuccessStates(typeBloodModels: response));
      } else {
        emit(TypeBloodErrorStates(error: response.message));
      }
    } catch (e, s) {
      debugPrint("catch error $s");
      debugPrint("catch error $e");
      emit(TypeBloodErrorStates(error: e.toString()));
    }
  }
}
