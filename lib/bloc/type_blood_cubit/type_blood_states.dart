import 'package:bus/data/models/type_blood_models/type_blood_models.dart';

abstract class TypeBloodStates {}

class TypeBloodInitialStates extends TypeBloodStates {}

class TypeBloodLoadingStates extends TypeBloodStates {}

class TypeBloodSuccessStates extends TypeBloodStates {
  final TypeBloodModels? typeBloodModels;
  TypeBloodSuccessStates({
    this.typeBloodModels,
  });
}

class TypeBloodErrorStates extends TypeBloodStates {
  final String? error;
  TypeBloodErrorStates({
    this.error,
  });
}
