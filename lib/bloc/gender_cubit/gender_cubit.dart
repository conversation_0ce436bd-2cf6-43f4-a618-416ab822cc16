
import 'package:bus/bloc/gender_cubit/gender_states.dart';
import 'package:bus/data/repo/gender_repo.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

class GenderCubit extends Cubit<GenderStates> {
  final _genderRepo = GenderRepo();
  GenderCubit() : super(GenderInitialStates());

  Future<void> getGender() async {
    emit(GenderInitialStates());
    try {
      final response = await _genderRepo.repo();
      if (response.status == true) {
        emit(GenderSuccessStates(genderModels: response));
      } else {
        emit(GenderErrorStates(error: response.message));
      }
    }catch (e, s) {
      debugPrint("catch error $s");
      debugPrint("catch error $e");
      emit(GenderErrorStates(error: e.toString()));
    }
  }
}