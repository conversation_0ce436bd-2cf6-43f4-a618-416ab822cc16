import 'package:bus/data/models/gender_models/gender_models.dart';

abstract class GenderStates {}

class GenderInitialStates extends GenderStates {}

class GenderLoadingStates extends GenderStates {}

class GenderSuccessStates extends GenderStates {
  final GenderModels? genderModels;
  GenderSuccessStates({
    this.genderModels,
  });
}

class GenderErrorStates extends GenderStates {
  final String? error;
  GenderErrorStates({
    this.error,
  });
}
