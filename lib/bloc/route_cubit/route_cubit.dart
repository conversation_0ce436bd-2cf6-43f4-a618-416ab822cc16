import 'package:bus/bloc/route_cubit/route_states.dart';
import 'package:bus/data/models/route_models/route_point_model.dart';
import 'package:bus/data/repo/route_repo.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';

class RouteCubit extends Cubit<RouteStates> {
  final _routeRepo = RouteRepo();

  RouteCubit() : super(RouteInitialState());

  static RouteCubit get(context) => BlocProvider.of(context);

  TripRouteModel? routeData;
  List<LatLng> routePoints = [];
  Set<Polyline> polylines = {};
  Set<Marker> markers = {};

  Future<void> getTripRoute({required int tripId}) async {
    emit(RouteLoadingState());

    try {
      final response = await _routeRepo.getTripRoute(tripId: tripId);

      if (response.status == true && response.data != null) {
        routeData = response.data;

        // Convert route points to LatLng for Google Maps
        routePoints = _routeRepo.getRouteLatLng(routeData?.route_points);

        if (routePoints.isNotEmpty) {
          // Create polyline for the route
          polylines = {
            Polyline(
              polylineId: const PolylineId('trip_route'),
              points: routePoints,
              color: Colors.blue,
              width: 5,
              // Use solid line instead of dashed line
              patterns: const [],
            ),
          };

          // Create markers for start and end points
          markers = {
            Marker(
              markerId: const MarkerId('start_point'),
              position: routePoints.first,
              icon: BitmapDescriptor.defaultMarkerWithHue(
                  BitmapDescriptor.hueGreen),
              infoWindow: const InfoWindow(title: 'Start Point'),
            ),
            Marker(
              markerId: const MarkerId('end_point'),
              position: routePoints.last,
              icon: BitmapDescriptor.defaultMarkerWithHue(
                  BitmapDescriptor.hueRed),
              infoWindow: const InfoWindow(title: 'End Point'),
            ),
          };

          // Add markers for each route point to show the complete path
          for (int i = 1; i < routePoints.length - 1; i++) {
            // Add a marker every few points to avoid cluttering the map
            if (i % 5 == 0) {
              markers.add(
                Marker(
                  markerId: MarkerId('route_point_$i'),
                  position: routePoints[i],
                  icon: BitmapDescriptor.defaultMarkerWithHue(
                      BitmapDescriptor.hueAzure),
                  // Make the intermediate markers smaller
                  visible:
                      false, // Hide the actual marker but keep it for future use
                ),
              );
            }
          }
        }

        emit(RouteSuccessState(
          routeData: routeData!,
          routePoints: routePoints,
          polylines: polylines,
          markers: markers,
        ));
      } else {
        emit(RouteErrorState(error: response.message ?? "Unknown error"));
      }
    } catch (e) {
      emit(RouteErrorState(error: e.toString()));
    }
  }
}
