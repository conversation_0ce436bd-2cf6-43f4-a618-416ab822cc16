import 'package:bus/data/models/route_models/route_point_model.dart';
import 'package:equatable/equatable.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';

abstract class RouteStates extends Equatable {
  const RouteStates();

  @override
  List<Object?> get props => [];
}

class RouteInitialState extends RouteStates {}

class RouteLoadingState extends RouteStates {}

class RouteSuccessState extends RouteStates {
  final TripRouteModel routeData;
  final List<LatLng> routePoints;
  final Set<Polyline> polylines;
  final Set<Marker> markers;

  const RouteSuccessState({
    required this.routeData,
    required this.routePoints,
    required this.polylines,
    required this.markers,
  });

  @override
  List<Object?> get props => [routeData, routePoints, polylines, markers];
}

class RouteErrorState extends RouteStates {
  final String error;

  const RouteErrorState({required this.error});

  @override
  List<Object?> get props => [error];
}
