import 'package:bus/bloc/buses_cubit/buses_states.dart';
import 'package:bus/data/models/buses_models/buses_info_models.dart';
import 'package:bus/data/repo/buses_repo.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import '../../data/models/buses_models/buses_models.dart';

class BusesCubit extends Cubit<BusesState> {
  final _busesRepo = BusesRepo();
  static BusesCubit get(context) => BlocProvider.of(context);
  BusesModel? busesModels;
  BusesCubit() : super(BusesInitialStates());
  List<BusesInfoModel> data = [];
  int _page = 1;
  int? last_pages, total, currentPage;
  bool? hasMoreData = false;

  void initScrollController({
    bool? isFirst = false,
    ScrollController? scrollController,
    Function()? setStates,
  }) {
    scrollController!.addListener(() {
      if (scrollController.position.maxScrollExtent ==
          scrollController.offset) {
        if (currentPage! < last_pages!) {
          hasMoreData = true;
          _page++;
          getBuses(
            pageNumber: _page,
            isFirst: false,
          );
          setStates!();
        } else {
          hasMoreData = false;
          setStates!();
        }
      }
    });
  }

  Future<void> getBuses({
    int? pageNumber,
    bool? isFirst = false,
  }) async {
    emit(BusesLoadingStates());
    if (isFirst == true) {
      data = [];
      _page = 1;
    }
    try {
      final response = await _busesRepo.repo(pageNumber: pageNumber);
      if (response.status == true) {
        last_pages = response.data!.last_page;
        currentPage = response.data!.current_page;
        data.addAll(response.data!.data!);
        busesModels = response;
        emit(BusesPaginationSuccessStates());
      } else {
        emit(BusesErrorStates(error: response.message));
      }
    } catch (e, stackTrace) {
      debugPrint("stackTrace $stackTrace");
      debugPrint("catch error $e");
      emit(BusesErrorStates(error: e.toString()));
    }
  }

  Future<void> getAllBuses() async {
    emit(BusesLoadingStates());
    try {
      final response = await _busesRepo.allBusesRepo();
      if (response.status == true) {
        emit(BusesSuccessStates(busesDataModels: response));
      } else {
        emit(BusesErrorStates(error: response.message));
      }
    } catch (e) {
      debugPrint("catch error at bus cubit$e");
      emit(BusesErrorStates(error: e.toString()));
    }
  }
}
