import 'package:bus/data/models/buses_models/buses_models.dart';

import '../../data/models/buses_models/buses_data_models.dart';
import '../../data/models/buses_models/buses_info_models.dart';

abstract class BusesState {}

class BusesInitialStates extends BusesState {}

class BusesLoadingStates extends BusesState {}

class BusesSuccessStates extends BusesState {
  final BusesModel? busesModels;
  final BusesInfoModel? busesInfoModels;
  final BusesDataModels? busesDataModels;
  BusesSuccessStates({this.busesModels, this.busesInfoModels, this.busesDataModels});
}

class BusesPaginationSuccessStates extends BusesState {}

class BusesErrorStates extends BusesState {
  final String? error;
  BusesErrorStates({
    this.error,
  });
}
