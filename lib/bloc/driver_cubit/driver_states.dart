import 'package:bus/data/models/driver_models/driver_models.dart';

abstract class DriverStates {}

class DriverInitialStates extends DriverStates {}

class DriverLoadingStates extends DriverStates {}

class DriverSuccessStates extends DriverStates {
  final DriverModels? driverModels;
  DriverSuccessStates({
    this.driverModels,
  });
}

class DriverErrorStates extends DriverStates {
  final String? error;
  DriverErrorStates({
    this.error,
  });
}
