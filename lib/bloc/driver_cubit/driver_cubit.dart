import 'dart:developer';

import 'package:bus/bloc/driver_cubit/driver_states.dart';
import 'package:bus/data/models/driver_models/driver_info_models.dart';
import 'package:bus/data/repo/driver_repo.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

class DriverCubit extends Cubit<DriverStates> {
  final _driverRepo = DriverRepo();
  DriverCubit() : super(DriverInitialStates());
  List<DriverInfoModels> data = [];
  int _page = 1;
  int? last_pages, total, currentPage;
  bool? hasMoreData = false;

  void initScrollController({
    bool? isFirst = false,
    ScrollController? scrollController,
    Function()? setStates,
  }) {
    scrollController!.addListener(() {
      if (scrollController.position.maxScrollExtent ==
          scrollController.offset) {
        if (currentPage! < last_pages!) {
          hasMoreData = true;
          _page++;
          getDriver(
            pageNumber: _page,
            isFirst: false,
          );
          setStates!();
        } else {
          hasMoreData = false;
          setStates!();
        }
      }
    });
  }

  Future<void> getDriver({
    int? pageNumber,
    bool? isFirst = false,
  }) async {
    emit(DriverLoadingStates());
    if (isFirst == true) {
      data = [];
      _page = 1;
    }
    try {
      final response = await _driverRepo.repo(pageNumber: pageNumber);
      if (response.status == true) {
        log(response.data!.data!.toString());
        last_pages = response.data!.last_page;
        currentPage = response.data!.current_page;
        data.addAll(response.data!.data!);
        emit(DriverSuccessStates(driverModels: response));
      } else {
        emit(DriverErrorStates(error: response.message));
      }
    } catch (e) {
      debugPrint("catch error $e");
      emit(DriverErrorStates(error: e.toString()));
    }
  }
}
