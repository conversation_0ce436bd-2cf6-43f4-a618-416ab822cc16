import 'package:bus/config/global_variable.dart';
import 'package:bus/services/notification_service/local_notification_service.dart';
import 'package:bus/services/notification_service/utils/logger.dart';
import 'package:bus/utils/get_it_injection.dart';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:timezone/data/latest.dart' as tz;

Future<void> initializeFCM() async {
  try {
    // Initialize timezone first
    tz.initializeTimeZones();
    Logger.i('Timezone initialized');

    // Configure Firebase Messaging first
    await FirebaseMessaging.instance
        .setForegroundNotificationPresentationOptions(
      alert: true,
      badge: true,
      sound: true,
    );

    // Request permissions before initializing service
    final settings = await FirebaseMessaging.instance.requestPermission(
      alert: true,
      badge: true,
      sound: true,
      provisional: false,
    );

    if (settings.authorizationStatus != AuthorizationStatus.authorized) {
      Logger.w(
          'Notification permissions not granted: ${settings.authorizationStatus}');
    }

    // Initialize notification service using GetIt to avoid duplicate initialization
    final localNotificationService = getIt<LocalNotificationService>();

    // Initialize only if not already initialized
    if (!LocalNotificationService.isInitialized) {
      await localNotificationService.initialize();
      Logger.i('Notification service initialized from deep_link');
    } else {
      Logger.i(
          'Notification service already initialized, skipping in deep_link');
    }

    // Add longer initial delay
    await Future.delayed(const Duration(seconds: 5));

    // Try to get token
    final token = await LocalNotificationService.firebaseToken;
    if (token != null) {
      initializeFCMToken();
    } else {
      Logger.w('FCM initialized without token - notifications may not work');
    }
  } catch (e) {
    Logger.e('Error initializing FCM: $e');
  }
}
