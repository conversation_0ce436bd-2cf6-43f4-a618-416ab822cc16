import 'package:bus/data/models/student_models/student_grade_models.dart';
import 'package:equatable/equatable.dart';
import 'package:json_annotation/json_annotation.dart';

part 'gender_models.g.dart';

@JsonSerializable()
class GenderModels extends Equatable {
  final bool? status;
  final String? message;
  final List<StudentGradeModels>? data;

  const GenderModels({
    this.data,
    this.status,
    this.message,
  });

  factory GenderModels.fromJson(Map<String, dynamic> json) {
    return _$GenderModelsFromJson(json);
  }

  Map<String, dynamic> toJson() => _$GenderModelsToJson(this);

  @override
  List<Object?> get props => [
        data,
        status,
        message,
      ];
}
