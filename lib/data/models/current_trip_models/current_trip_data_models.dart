import 'dart:convert';
import 'package:bus/data/models/current_trip_models/notification_bus_models.dart';

class CurrentTripDataModels {
  //userable_id
  final int? id;
  final int? userable_id;

  final String? name;
  final String? phone;
  final int? grade_id;
  final int? gender_id;
  final int? school_id;
  final int? religion_id;
  final int? type__blood_id;
  final int? classroom_id;
  final int? bus_id;
  final String? address;
  final String? city_name;
  final int? status;
  final String? trip_type;
  final String? parent_key;
  final String? parent_secret;
  final String? Date_Birth;
  final String? logo;
  final String? latitude;
  final String? longitude;
  final String? created_at;
  final String? updated_at;
  final String? logo_path;
  final NotificationBusModel? bus;

  const CurrentTripDataModels({
    this.status,
    this.logo_path,
    this.bus_id,
    this.bus,
    this.userable_id,
    this.school_id,
    this.gender_id,
    this.city_name,
    this.address,
    this.religion_id,
    this.phone,
    this.type__blood_id,
    this.updated_at,
    this.created_at,
    this.id,
    this.name,
    this.logo,
    this.latitude,
    this.longitude,
    this.classroom_id,
    this.Date_Birth,
    this.grade_id,
    this.parent_key,
    this.parent_secret,
    this.trip_type,
  });

  CurrentTripDataModels copyWith({
    int? id,
    String? name,
    String? phone,
    int? userable_id,
    int? grade_id,
    int? gender_id,
    int? school_id,
    int? religion_id,
    int? type__blood_id,
    int? classroom_id,
    int? bus_id,
    String? address,
    String? city_name,
    int? status,
    String? trip_type,
    String? parent_key,
    String? parent_secret,
    String? Date_Birth,
    String? logo,
    String? latitude,
    String? longitude,
    String? created_at,
    String? updated_at,
    String? logo_path,
    NotificationBusModel? bus,
  }) =>
      CurrentTripDataModels(
        id: id ?? this.id,
        userable_id: userable_id ?? this.userable_id,
        name: name ?? this.name,
        phone: phone ?? this.phone,
        grade_id: grade_id ?? this.grade_id,
        gender_id: gender_id ?? gender_id,
        school_id: school_id ?? school_id,
        religion_id: religion_id ?? religion_id,
        type__blood_id: type__blood_id ?? this.type__blood_id,
        classroom_id: classroom_id ?? this.classroom_id,
        bus_id: bus_id ?? this.bus_id,
        address: address ?? this.address,
        city_name: city_name ?? city_name,
        status: status ?? this.status,
        trip_type: trip_type ?? this.trip_type,
        parent_key: parent_key ?? this.parent_key,
        parent_secret: parent_secret ?? this.parent_secret,
        Date_Birth: Date_Birth ?? Date_Birth,
        logo: logo ?? this.logo,
        latitude: latitude ?? this.latitude,
        longitude: longitude ?? this.longitude,
        created_at: created_at ?? this.created_at,
        updated_at: updated_at ?? updated_at,
        logo_path: logo_path ?? this.logo_path,
        bus: bus ?? this.bus,
      );

  factory CurrentTripDataModels.fromRawJson(String str) =>
      CurrentTripDataModels.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory CurrentTripDataModels.fromJson(Map<String, dynamic> json) =>
      CurrentTripDataModels(
        id: json["id"],
        userable_id: json["userable_id"],
        name: json["name"],
        phone: json["phone"],
        grade_id: json["grade_id"],
        gender_id: json["gender_id"],
        school_id: json["school_id"],
        religion_id: json["religion_id"],
        type__blood_id: json["type__blood_id"],
        classroom_id: json['classroom_id'],
        bus_id: json['bus_id'],
        address: json['address'],
        city_name: json['city_name'],
        status: json['status'],
        trip_type: json['trip_type'],
        parent_key: json['parent_key'],
        parent_secret: json['parent_secret'],
        Date_Birth: json['Date_Birth'],
        logo: json['logo'],
        latitude: json['latitude'],
        longitude: json['longitude'],
        created_at: json['created_at'],
        updated_at: json['updated_at'],
        logo_path: json['logo_path'],
        bus: json["bus"] == null
            ? null
            : NotificationBusModel.fromJson(json["bus"]),
      );

  Map<String, dynamic> toJson() => {
        "id": id,
        "userable_id": userable_id,
        "name": name,
        "phone": phone,
        "grade_id": grade_id,
        "gender_id": gender_id,
        "school_id": school_id,
        "religion_id": religion_id,
        "type__blood_id": type__blood_id,
        "classroom_id": classroom_id,
        "bus_id": bus_id,
        "address": address,
        "city_name": city_name,
        "status": status,
        "trip_type": trip_type,
        "parent_key": parent_key,
        "parent_secret": parent_secret,
        "Date_Birth": Date_Birth,
        "logo": logo,
        "latitude": latitude,
        "longitude": longitude,
        "created_at": created_at,
        "updated_at": updated_at,
        "logo_path": logo_path,
        "bus": bus?.toJson(),
      };
}
