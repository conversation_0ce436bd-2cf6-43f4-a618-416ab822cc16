import 'dart:convert';

import 'package:bus/data/models/current_trip_models/notification_data_bus_models.dart';

class NotificationBusModel {
  final int? id;
  final String? name;
  final String? notes;
  final int? status;
  final int? school_id;
  final String? car_number;
  final String? created_at;
  final String? updated_at;
  final NotificationDataBusModels? admin;
  final DriverOpenTripModels? driver;

  NotificationBusModel({
    this.status,
    this.name,
    this.updated_at,
    this.created_at,
    this.id,
    this.school_id,
    this.car_number,
    this.admin,
    this.notes,
    this.driver,
  });

  NotificationBusModel copyWith({
    int? id,
    String? name,
    String? notes,
    int? status,
    int? school_id,
    String? car_number,
    String? created_at,
    String? updated_at,
    NotificationDataBusModels? admin,
    DriverOpenTripModels? driver,
  }) =>
      NotificationBusModel(
        id: id ?? this.id,
        name: name ?? this.name,
        notes: notes ?? this.notes,
        status: status ?? this.status,
        school_id: school_id ?? this.school_id,
        car_number: car_number ?? this.car_number,
        created_at: created_at ?? this.created_at,
        updated_at: updated_at ?? this.updated_at,
        admin: admin ?? this.admin,
        driver: driver ?? this.driver,
      );

  factory NotificationBusModel.fromRawJson(String str) =>
      NotificationBusModel.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory NotificationBusModel.fromJson(Map<String, dynamic> json) =>
      NotificationBusModel(
        id: json["id"],
        name: json["name"],
        notes: json["notes"],
        status: json["status"],
        school_id: json["school_id"],
        car_number: json["car_number"],
        created_at: json["created_at"],
        updated_at: json["updated_at"],
        admin: json["admin"] == null
            ? null
            : NotificationDataBusModels.fromJson(json["admin"]),
        driver: json["driver"] == null
            ? null
            : DriverOpenTripModels.fromJson(json["driver"]),
      );

  Map<String, dynamic> toJson() => {
        "id": id,
        "name": name,
        "notes": notes,
        "status": status,
        "school_id": school_id,
        "car_number": car_number,
        "created_at": created_at,
        "updated_at": updated_at,
        "admin": admin?.toJson(),
        "driver": driver?.toJson()
      };
}

class DriverOpenTripModels {
  final int? id;
  final String? name;
  final String? username;
  final String? logo;
  final String? type;
  final String? phone;
  final String? created_at;
  final String? updated_at;
  final String? firebase_token;

  DriverOpenTripModels({
    this.name,
    this.type,
    this.id,
    this.created_at,
    this.updated_at,
    this.logo,
    this.phone,
    this.firebase_token,
    this.username,
  });

  DriverOpenTripModels copyWith({
    int? id,
    String? name,
    String? type,
    String? created_at,
    String? updated_at,
    String? logo,
    String? phone,
    String? firebase_token,
    String? username,
  }) =>
      DriverOpenTripModels(
        id: id ?? this.id,
        name: name ?? this.name,
        created_at: created_at ?? this.created_at,
        updated_at: updated_at ?? this.updated_at,
        type: type ?? this.type,
        logo: logo ?? this.logo,
        username: username ?? this.username,
        phone: phone ?? this.phone,
        firebase_token: firebase_token ?? this.firebase_token,
      );

  factory DriverOpenTripModels.fromRawJson(String str) =>
      DriverOpenTripModels.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory DriverOpenTripModels.fromJson(Map<String, dynamic> json) =>
      DriverOpenTripModels(
        id: json["id"],
        name: json["name"],
        created_at: json["created_at"],
        updated_at: json["updated_at"],
        type: json["type"],
        logo: json["logo"],
        username: json["username"],
        phone: json["phone"],
        firebase_token: json["firebase_token"],
      );

  Map<String, dynamic> toJson() => {
        "id": id,
        "name": name,
        "created_at": created_at,
        "updated_at": updated_at,
        "type": type,
        "username": username,
        "logo": logo,
        "phone": phone,
        "firebase_token": firebase_token,
      };
}
