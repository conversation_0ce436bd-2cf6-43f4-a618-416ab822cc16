import 'dart:convert';
import 'package:bus/data/models/current_trip_models/current_trip_data_models.dart';


class CurrentTripModels {
  final bool? status;
  final String? message;
  final List<CurrentTripDataModels>? data;

  const CurrentTripModels({
    this.status,
    this.message,
    this.data,
  });

  CurrentTripModels copyWith({
    bool? status,
    String? message,
    List<CurrentTripDataModels>? data,
  }) =>
      CurrentTripModels(
        status: status ?? this.status,
        message: message ?? this.message,
        data: data ?? this.data,
      );

  factory CurrentTripModels.fromRawJson(String str) => CurrentTripModels.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory CurrentTripModels.fromJson(Map<String, dynamic> json) =>
      CurrentTripModels(
        data: json["data"] == null
            ? []
            : List<CurrentTripDataModels>.from(
            json["data"]!.map((x) => CurrentTripDataModels.from<PERSON>son(x))),
        status: json["status"],
        message: json['message'],
      );

  Map<String, dynamic> toJson() => {
    "data": data == null
        ? []
        : List<dynamic>.from(data!.map((x) => x.toJson())),
    "message": message,
    "status": status,
  };
}
