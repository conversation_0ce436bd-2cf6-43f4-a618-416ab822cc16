
import 'dart:convert';

class NotificationDataBusModels {
  final int? id;
  final String? name;
  final String? username;
  final int? gender_id;
  final int? school_id;
  final int? religion_id;
  final int? type__blood_id;
  final int? bus_id;
  final String? Joining_Date;
  final String? address;
  final String? city_name;
  final int? status;
  final String? type;
  final String? phone;
  final String? email_verified_at;
  final String? created_at;
  final String? updated_at;
  final String? typeAuth;
  final String? firebase_token;
  final String? logo_path;

  NotificationDataBusModels({
    this.status,
    this.name,
    this.updated_at,
    this.created_at,
    this.id,
    this.school_id,
    this.address,
    this.username,
    this.city_name,
    this.phone,
    this.type,
    this.gender_id,
    this.bus_id,
    this.logo_path,
    this.typeAuth,
    this.firebase_token,
    this.email_verified_at,
    this.Joining_Date,
    this.religion_id,
    this.type__blood_id,
  });

  NotificationDataBusModels copyWith({
    status,
    name,
    updated_at,
    created_at,
    id,
    school_id,
    address,
    username,
    city_name,
    phone,
    type,
    gender_id,
    bus_id,
    logo_path,
    typeAuth,
    firebase_token,
    email_verified_at,
    Joining_Date,
    religion_id,
    type__blood_id,
  }) =>
      NotificationDataBusModels(
        id: id ?? this.id,
        name: name ?? this.name,
        status: status ?? this.status,
        updated_at: updated_at ?? this.updated_at,
        school_id: school_id ?? this.school_id,
        typeAuth: typeAuth ?? this.typeAuth,
        created_at: created_at ?? this.created_at,
        type__blood_id: type__blood_id ?? this.type__blood_id,
        address: address ?? this.address,
        username: username ?? this.username,
        city_name: city_name ?? this.city_name,
        phone: phone ?? this.phone,
        type: type ?? this.type,
        gender_id: gender_id ?? this.gender_id,
        bus_id: bus_id ?? this.bus_id,
        logo_path: logo_path ?? this.logo_path,
        firebase_token: firebase_token ?? this.firebase_token,
        email_verified_at: email_verified_at ?? this.email_verified_at,
        Joining_Date: Joining_Date ?? this.Joining_Date,
        religion_id: religion_id ?? this.religion_id,
      );

  factory NotificationDataBusModels.fromRawJson(String str) =>
      NotificationDataBusModels.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory NotificationDataBusModels.fromJson(Map<String, dynamic> json) =>
      NotificationDataBusModels(
        id: json["id"],
        name: json["name"],
        status: json["status"],
        school_id: json["school_id"],
        created_at: json["created_at"],
        updated_at: json["updated_at"],
        username: json['username'],
        address: json['address'],
        city_name: json['city_name'],
        phone: json['phone'],
        type: json['type'],
        gender_id: json['gender_id'],
        bus_id: json['bus_id'],
        logo_path: json['logo_path'],
        typeAuth: json['typeAuth'],
        firebase_token: json['firebase_token'],
        email_verified_at: json['email_verified_at'],
        Joining_Date: json['Joining_Date'],
        religion_id: json['religion_id'],
        type__blood_id: json['type__blood_id'],
      );

  Map<String, dynamic> toJson() => {
    "id": id,
    "name": name,
    "status": status,
    "school_id": school_id,
    "created_at": created_at,
    "updated_at": updated_at,
    "username": username,
    "address": address,
    "city_name": city_name,
    "phone": phone,
    "type": type,
    "gender_id": gender_id,
    "bus_id": bus_id,
    "logo_path": logo_path,
    "typeAuth": typeAuth,
    "firebase_token": firebase_token,
    "email_verified_at": email_verified_at,
    "Joining_Date": Joining_Date,
    "religion_id": religion_id,
    "type__blood_id": type__blood_id,
  };
}
