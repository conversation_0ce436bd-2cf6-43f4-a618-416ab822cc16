import 'package:equatable/equatable.dart';
import 'package:json_annotation/json_annotation.dart';

part 'add_bus_data_models.g.dart';

@JsonSerializable()
class AddBusDataModels extends Equatable {
  final String? name;
  final String? notes;
  final String? car_number;
  final int? school_id;
  final String? updated_at;
  final String? created_at;
  final int? id;

  const AddBusDataModels({
    this.name,
    this.notes,
    this.created_at,
    this.car_number,
    this.id,
    this.school_id,
    this.updated_at,
  });

  factory AddBusDataModels.fromJson(Map<String, dynamic> json) {
    return _$AddBusDataModelsFromJson(json);
  }

  Map<String, dynamic> toJson() => _$AddBusDataModelsToJson(this);


  @override
  List<Object?> get props => [
        name,
        notes,
        created_at,
        car_number,
        id,
        school_id,
        updated_at,
      ];
}
