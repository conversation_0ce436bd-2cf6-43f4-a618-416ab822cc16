import 'package:equatable/equatable.dart';
import 'package:json_annotation/json_annotation.dart';

part 'add_bus_errors_models.g.dart';

@JsonSerializable()
class ErrorAddBusModels extends Equatable {
  final List<String>? name;

  const ErrorAddBusModels({
    this.name,
  });

  factory ErrorAddBusModels.fromJson(Map<String, dynamic> json) {
    return _$ErrorAddBusModelsFromJson(json);
  }

  Map<String, dynamic> toJson() => _$ErrorAddBusModelsToJson(this);


  @override
  List<Object?> get props => [
        name,
      ];
}
