import 'package:bus/data/models/add_bus_models/add_bus_data_models.dart';
import 'package:bus/data/models/add_bus_models/add_bus_errors_models.dart';
import 'package:equatable/equatable.dart';
import 'package:json_annotation/json_annotation.dart';

part 'add_bus_models.g.dart';

@JsonSerializable()
class AddBusModels extends Equatable {
  final bool? errors;
  final String? message;
  final AddBusDataModels? data;
  final ErrorAddBusModels? errorMessages;

  const AddBusModels({
    this.errors,
    this.message,
    this.data,
    this.errorMessages,
  });

  factory AddBusModels.fromJson(Map<String, dynamic> json) {
    return _$AddBusModelsFromJson(json);
  }

  Map<String, dynamic> toJson() => _$AddBusModelsToJson(this);


  @override
  List<Object?> get props => [
        errors,
        message,
        data,
        errorMessages,
      ];
}
