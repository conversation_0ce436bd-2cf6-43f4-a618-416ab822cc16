import 'package:equatable/equatable.dart';
import 'package:json_annotation/json_annotation.dart';

part 'temporary_address_models.g.dart';

@JsonSerializable()
class TemporaryAddressModels extends Equatable {
  final bool? status;
  final TemporaryAddressData? data;

  const TemporaryAddressModels({
    this.status,
    this.data,
  });

  factory TemporaryAddressModels.fromJson(Map<String, dynamic> json) =>
      _$TemporaryAddressModelsFromJson(json);

  Map<String, dynamic> toJson() => _$TemporaryAddressModelsToJson(this);

  @override
  List<Object?> get props => [status, data];
}

@JsonSerializable()
class TemporaryAddressData extends Equatable {
  @Json<PERSON>ey(name: 'current_page')
  final int? currentPage;
  final List<TemporaryAddressItem>? data;
  @Json<PERSON>ey(name: 'first_page_url')
  final String? firstPageUrl;
  final int? from;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'last_page')
  final int? lastPage;
  @J<PERSON><PERSON><PERSON>(name: 'last_page_url')
  final String? lastPageUrl;
  final List<TemporaryAddressLink>? links;
  @JsonKey(name: 'next_page_url')
  final String? nextPageUrl;
  final String? path;
  @JsonKey(name: 'per_page')
  final int? perPage;
  @JsonKey(name: 'prev_page_url')
  final String? prevPageUrl;
  final int? to;
  final int? total;

  const TemporaryAddressData({
    this.currentPage,
    this.data,
    this.firstPageUrl,
    this.from,
    this.lastPage,
    this.lastPageUrl,
    this.links,
    this.nextPageUrl,
    this.path,
    this.perPage,
    this.prevPageUrl,
    this.to,
    this.total,
  });

  factory TemporaryAddressData.fromJson(Map<String, dynamic> json) =>
      _$TemporaryAddressDataFromJson(json);

  Map<String, dynamic> toJson() => _$TemporaryAddressDataToJson(this);

  @override
  List<Object?> get props => [
        currentPage,
        data,
        firstPageUrl,
        from,
        lastPage,
        lastPageUrl,
        links,
        nextPageUrl,
        path,
        perPage,
        prevPageUrl,
        to,
        total,
      ];
}

@JsonSerializable()
class TemporaryAddressItem extends Equatable {
  final int? id;
  @JsonKey(name: 'student_id')
  final String? studentId;
  final String? address;
  final String? latitude;
  final String? longitude;
  @JsonKey(name: 'from_date')
  final String? fromDate;
  @JsonKey(name: 'to_date')
  final String? toDate;
  @JsonKey(name: 'accept_status')
  final String? acceptStatus;
  @JsonKey(name: 'created_at')
  final String? createdAt;
  @JsonKey(name: 'updated_at')
  final String? updatedAt;
  @JsonKey(name: 'old_bus')
  final List<dynamic>? oldBus;
  final Student? student;

  // إضافة حقول إضافية للعرض
  @JsonKey(includeFromJson: false, includeToJson: false)
  final String? parentName;

  const TemporaryAddressItem({
    this.id,
    this.studentId,
    this.address,
    this.latitude,
    this.longitude,
    this.fromDate,
    this.toDate,
    this.acceptStatus,
    this.createdAt,
    this.updatedAt,
    this.oldBus,
    this.student,
    this.parentName,
  });

  factory TemporaryAddressItem.fromJson(Map<String, dynamic> json) =>
      _$TemporaryAddressItemFromJson(json);

  Map<String, dynamic> toJson() => _$TemporaryAddressItemToJson(this);

  @override
  List<Object?> get props => [
        id,
        studentId,
        address,
        latitude,
        longitude,
        fromDate,
        toDate,
        acceptStatus,
        createdAt,
        updatedAt,
        oldBus,
        student,
        parentName,
      ];
}

@JsonSerializable()
class TemporaryAddressLink extends Equatable {
  final String? url;
  final String? label;
  final bool? active;

  const TemporaryAddressLink({
    this.url,
    this.label,
    this.active,
  });

  factory TemporaryAddressLink.fromJson(Map<String, dynamic> json) =>
      _$TemporaryAddressLinkFromJson(json);

  Map<String, dynamic> toJson() => _$TemporaryAddressLinkToJson(this);

  @override
  List<Object?> get props => [url, label, active];
}

@JsonSerializable()
class Student extends Equatable {
  final String? id;
  @JsonKey(name: 'bus_id')
  final int? busId;
  final String? name;
  final String? address;
  final String? latitude;
  final String? longitude;
  @JsonKey(name: 'logo_path')
  final String? logoPath;
  final Bus? bus;

  const Student({
    this.id,
    this.busId,
    this.name,
    this.address,
    this.latitude,
    this.longitude,
    this.logoPath,
    this.bus,
  });

  factory Student.fromJson(Map<String, dynamic> json) =>
      _$StudentFromJson(json);

  Map<String, dynamic> toJson() => _$StudentToJson(this);

  @override
  List<Object?> get props => [
        id,
        busId,
        name,
        address,
        latitude,
        longitude,
        logoPath,
        bus,
      ];
}

@JsonSerializable()
class Bus extends Equatable {
  final int? id;
  final String? name;
  final dynamic driver;
  final Admin? admin;

  const Bus({
    this.id,
    this.name,
    this.driver,
    this.admin,
  });

  factory Bus.fromJson(Map<String, dynamic> json) =>
      _$BusFromJson(json);

  Map<String, dynamic> toJson() => _$BusToJson(this);

  @override
  List<Object?> get props => [
        id,
        name,
        driver,
        admin,
      ];
}

@JsonSerializable()
class Admin extends Equatable {
  final int? id;
  final String? username;
  final String? name;
  @JsonKey(name: 'gender_id')
  final int? genderId;
  @JsonKey(name: 'school_id')
  final int? schoolId;
  @JsonKey(name: 'religion_id')
  final dynamic religionId;
  @JsonKey(name: 'type__blood_id')
  final dynamic typeBloodId;
  @JsonKey(name: 'bus_id')
  final int? busId;
  @JsonKey(name: 'Joining_Date')
  final dynamic joiningDate;
  final String? address;
  @JsonKey(name: 'city_name')
  final dynamic cityName;
  final int? status;
  final String? logo;
  final String? type;
  final String? phone;
  @JsonKey(name: 'birth_date')
  final dynamic birthDate;
  @JsonKey(name: 'email_verified_at')
  final String? emailVerifiedAt;
  @JsonKey(name: 'firebase_token')
  final dynamic firebaseToken;
  @JsonKey(name: 'typeAuth')
  final String? typeAuth;
  @JsonKey(name: 'created_at')
  final String? createdAt;
  @JsonKey(name: 'updated_at')
  final String? updatedAt;
  @JsonKey(name: 'logo_path')
  final String? logoPath;

  const Admin({
    this.id,
    this.username,
    this.name,
    this.genderId,
    this.schoolId,
    this.religionId,
    this.typeBloodId,
    this.busId,
    this.joiningDate,
    this.address,
    this.cityName,
    this.status,
    this.logo,
    this.type,
    this.phone,
    this.birthDate,
    this.emailVerifiedAt,
    this.firebaseToken,
    this.typeAuth,
    this.createdAt,
    this.updatedAt,
    this.logoPath,
  });

  factory Admin.fromJson(Map<String, dynamic> json) =>
      _$AdminFromJson(json);

  Map<String, dynamic> toJson() => _$AdminToJson(this);

  @override
  List<Object?> get props => [
        id,
        username,
        name,
        genderId,
        schoolId,
        religionId,
        typeBloodId,
        busId,
        joiningDate,
        address,
        cityName,
        status,
        logo,
        type,
        phone,
        birthDate,
        emailVerifiedAt,
        firebaseToken,
        typeAuth,
        createdAt,
        updatedAt,
        logoPath,
      ];
}
