// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'temporary_address_models.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

TemporaryAddressModels _$TemporaryAddressModelsFromJson(
        Map<String, dynamic> json) =>
    TemporaryAddressModels(
      status: json['status'] as bool?,
      data: json['data'] == null
          ? null
          : TemporaryAddressData.fromJson(json['data'] as Map<String, dynamic>),
    );

Map<String, dynamic> _$TemporaryAddressModelsToJson(
        TemporaryAddressModels instance) =>
    <String, dynamic>{
      'status': instance.status,
      'data': instance.data,
    };

TemporaryAddressData _$TemporaryAddressDataFromJson(
        Map<String, dynamic> json) =>
    TemporaryAddressData(
      currentPage: (json['current_page'] as num?)?.toInt(),
      data: (json['data'] as List<dynamic>?)
          ?.map((e) => TemporaryAddressItem.fromJson(e as Map<String, dynamic>))
          .toList(),
      firstPageUrl: json['first_page_url'] as String?,
      from: (json['from'] as num?)?.toInt(),
      lastPage: (json['last_page'] as num?)?.toInt(),
      lastPageUrl: json['last_page_url'] as String?,
      links: (json['links'] as List<dynamic>?)
          ?.map((e) => TemporaryAddressLink.fromJson(e as Map<String, dynamic>))
          .toList(),
      nextPageUrl: json['next_page_url'] as String?,
      path: json['path'] as String?,
      perPage: (json['per_page'] as num?)?.toInt(),
      prevPageUrl: json['prev_page_url'] as String?,
      to: (json['to'] as num?)?.toInt(),
      total: (json['total'] as num?)?.toInt(),
    );

Map<String, dynamic> _$TemporaryAddressDataToJson(
        TemporaryAddressData instance) =>
    <String, dynamic>{
      'current_page': instance.currentPage,
      'data': instance.data,
      'first_page_url': instance.firstPageUrl,
      'from': instance.from,
      'last_page': instance.lastPage,
      'last_page_url': instance.lastPageUrl,
      'links': instance.links,
      'next_page_url': instance.nextPageUrl,
      'path': instance.path,
      'per_page': instance.perPage,
      'prev_page_url': instance.prevPageUrl,
      'to': instance.to,
      'total': instance.total,
    };

TemporaryAddressItem _$TemporaryAddressItemFromJson(
        Map<String, dynamic> json) =>
    TemporaryAddressItem(
      id: (json['id'] as num?)?.toInt(),
      studentId: json['student_id'] as String?,
      address: json['address'] as String?,
      latitude: json['latitude'] as String?,
      longitude: json['longitude'] as String?,
      fromDate: json['from_date'] as String?,
      toDate: json['to_date'] as String?,
      acceptStatus: json['accept_status'] as String?,
      createdAt: json['created_at'] as String?,
      updatedAt: json['updated_at'] as String?,
      oldBus: json['old_bus'] as List<dynamic>?,
      student: json['student'] == null
          ? null
          : Student.fromJson(json['student'] as Map<String, dynamic>),
    );

Map<String, dynamic> _$TemporaryAddressItemToJson(
        TemporaryAddressItem instance) =>
    <String, dynamic>{
      'id': instance.id,
      'student_id': instance.studentId,
      'address': instance.address,
      'latitude': instance.latitude,
      'longitude': instance.longitude,
      'from_date': instance.fromDate,
      'to_date': instance.toDate,
      'accept_status': instance.acceptStatus,
      'created_at': instance.createdAt,
      'updated_at': instance.updatedAt,
      'old_bus': instance.oldBus,
      'student': instance.student,
    };

TemporaryAddressLink _$TemporaryAddressLinkFromJson(
        Map<String, dynamic> json) =>
    TemporaryAddressLink(
      url: json['url'] as String?,
      label: json['label'] as String?,
      active: json['active'] as bool?,
    );

Map<String, dynamic> _$TemporaryAddressLinkToJson(
        TemporaryAddressLink instance) =>
    <String, dynamic>{
      'url': instance.url,
      'label': instance.label,
      'active': instance.active,
    };

Student _$StudentFromJson(Map<String, dynamic> json) => Student(
      id: json['id'] as String?,
      busId: (json['bus_id'] as num?)?.toInt(),
      name: json['name'] as String?,
      address: json['address'] as String?,
      latitude: json['latitude'] as String?,
      longitude: json['longitude'] as String?,
      logoPath: json['logo_path'] as String?,
      bus: json['bus'] == null
          ? null
          : Bus.fromJson(json['bus'] as Map<String, dynamic>),
    );

Map<String, dynamic> _$StudentToJson(Student instance) => <String, dynamic>{
      'id': instance.id,
      'bus_id': instance.busId,
      'name': instance.name,
      'address': instance.address,
      'latitude': instance.latitude,
      'longitude': instance.longitude,
      'logo_path': instance.logoPath,
      'bus': instance.bus,
    };

Bus _$BusFromJson(Map<String, dynamic> json) => Bus(
      id: (json['id'] as num?)?.toInt(),
      name: json['name'] as String?,
      driver: json['driver'],
      admin: json['admin'] == null
          ? null
          : Admin.fromJson(json['admin'] as Map<String, dynamic>),
    );

Map<String, dynamic> _$BusToJson(Bus instance) => <String, dynamic>{
      'id': instance.id,
      'name': instance.name,
      'driver': instance.driver,
      'admin': instance.admin,
    };

Admin _$AdminFromJson(Map<String, dynamic> json) => Admin(
      id: (json['id'] as num?)?.toInt(),
      username: json['username'] as String?,
      name: json['name'] as String?,
      genderId: (json['gender_id'] as num?)?.toInt(),
      schoolId: (json['school_id'] as num?)?.toInt(),
      religionId: json['religion_id'],
      typeBloodId: json['type__blood_id'],
      busId: (json['bus_id'] as num?)?.toInt(),
      joiningDate: json['Joining_Date'],
      address: json['address'] as String?,
      cityName: json['city_name'],
      status: (json['status'] as num?)?.toInt(),
      logo: json['logo'] as String?,
      type: json['type'] as String?,
      phone: json['phone'] as String?,
      birthDate: json['birth_date'],
      emailVerifiedAt: json['email_verified_at'] as String?,
      firebaseToken: json['firebase_token'],
      typeAuth: json['typeAuth'] as String?,
      createdAt: json['created_at'] as String?,
      updatedAt: json['updated_at'] as String?,
      logoPath: json['logo_path'] as String?,
    );

Map<String, dynamic> _$AdminToJson(Admin instance) => <String, dynamic>{
      'id': instance.id,
      'username': instance.username,
      'name': instance.name,
      'gender_id': instance.genderId,
      'school_id': instance.schoolId,
      'religion_id': instance.religionId,
      'type__blood_id': instance.typeBloodId,
      'bus_id': instance.busId,
      'Joining_Date': instance.joiningDate,
      'address': instance.address,
      'city_name': instance.cityName,
      'status': instance.status,
      'logo': instance.logo,
      'type': instance.type,
      'phone': instance.phone,
      'birth_date': instance.birthDate,
      'email_verified_at': instance.emailVerifiedAt,
      'firebase_token': instance.firebaseToken,
      'typeAuth': instance.typeAuth,
      'created_at': instance.createdAt,
      'updated_at': instance.updatedAt,
      'logo_path': instance.logoPath,
    };
