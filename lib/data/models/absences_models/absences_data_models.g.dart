// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'absences_data_models.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

AbsencesDataModels _$AbsencesDataModelsFromJson(Map<String, dynamic> json) =>
    AbsencesDataModels(
      last_page: (json['last_page'] as num?)?.toInt(),
      links: (json['links'] as List<dynamic>?)
          ?.map((e) => StudentLinksModels.fromJson(e as Map<String, dynamic>))
          .toList(),
      to: (json['to'] as num?)?.toInt(),
      prev_page_url: json['prev_page_url'] as String?,
      per_page: (json['per_page'] as num?)?.toInt(),
      next_page_url: json['next_page_url'] as String?,
      last_page_url: json['last_page_url'] as String?,
      first_page_url: json['first_page_url'] as String?,
      from: (json['from'] as num?)?.toInt(),
      path: json['path'] as String?,
      current_page: (json['current_page'] as num?)?.toInt(),
      total: (json['total'] as num?)?.toInt(),
      data: (json['data'] as List<dynamic>?)
          ?.map((e) => AbsencesInfoModels.fromJson(e as Map<String, dynamic>))
          .toList(),
    );

Map<String, dynamic> _$AbsencesDataModelsToJson(AbsencesDataModels instance) =>
    <String, dynamic>{
      'current_page': instance.current_page,
      'first_page_url': instance.first_page_url,
      'from': instance.from,
      'last_page': instance.last_page,
      'last_page_url': instance.last_page_url,
      'next_page_url': instance.next_page_url,
      'path': instance.path,
      'per_page': instance.per_page,
      'prev_page_url': instance.prev_page_url,
      'to': instance.to,
      'total': instance.total,
      'links': instance.links,
      'data': instance.data,
    };
