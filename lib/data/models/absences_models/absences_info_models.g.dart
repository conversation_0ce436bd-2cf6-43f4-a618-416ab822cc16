// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'absences_info_models.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

AbsencesInfoModels _$AbsencesInfoModelsFromJson(Map<String, dynamic> json) =>
    AbsencesInfoModels(
      id: (json['id'] as num?)?.toInt(),
      created_at: json['created_at'] as String?,
      school_id: json['school_id'] as String?,
      updated_at: json['updated_at'] as String?,
      student_id: json['student_id'] as String?,
      my__parent_id: json['my__parent_id'] as String?,
      attendence_type: json['attendence_type'] as String?,
      attendence_date: json['attendence_date'] as String?,
      bus_id: json['bus_id'] as String?,
      classroom_id: json['classroom_id'] as String?,
      grade_id: json['grade_id'] as String?,
      grade: json['grade'] == null
          ? null
          : StudentGradeModels.fromJson(json['grade'] as Map<String, dynamic>),
      classroom: json['classroom'] == null
          ? null
          : StudentClassRoomModels.fromJson(
              json['classroom'] as Map<String, dynamic>),
      bus: json['bus'] == null
          ? null
          : StudentBusModels.fromJson(json['bus'] as Map<String, dynamic>),
      schools: json['schools'] == null
          ? null
          : LoginDataModels.fromJson(json['schools'] as Map<String, dynamic>),
      parent: json['parent'] == null
          ? null
          : AbsencesParentModels.fromJson(
              json['parent'] as Map<String, dynamic>),
      students: json['students'] == null
          ? null
          : AbsencesStudentModels.fromJson(
              json['students'] as Map<String, dynamic>),
    );

Map<String, dynamic> _$AbsencesInfoModelsToJson(AbsencesInfoModels instance) =>
    <String, dynamic>{
      'id': instance.id,
      'grade_id': instance.grade_id,
      'school_id': instance.school_id,
      'classroom_id': instance.classroom_id,
      'bus_id': instance.bus_id,
      'my__parent_id': instance.my__parent_id,
      'student_id': instance.student_id,
      'attendence_date': instance.attendence_date,
      'attendence_type': instance.attendence_type,
      'created_at': instance.created_at,
      'updated_at': instance.updated_at,
      'schools': instance.schools,
      'bus': instance.bus,
      'grade': instance.grade,
      'classroom': instance.classroom,
      'parent': instance.parent,
      'students': instance.students,
    };
