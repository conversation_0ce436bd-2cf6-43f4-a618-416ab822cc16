// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'absences_student_models.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

AbsencesStudentModels _$AbsencesStudentModelsFromJson(
        Map<String, dynamic> json) =>
    AbsencesStudentModels(
      address: json['address'] as String?,
      logo_path: json['logo_path'] as String?,
      deleted_at: json['deleted_at'] as String?,
      phone: json['phone'] as String?,
      status: json['status'] as String?,
      id: (json['id'] as num?)?.toInt(),
      created_at: json['created_at'] as String?,
      updated_at: json['updated_at'] as String?,
      name: json['name'] as String?,
      classroom_id: json['classroom_id'] as String?,
      bus_id: json['bus_id'] as String?,
      school_id: json['school_id'] as String?,
      attendant_admins_id: json['attendant_admins_id'] as String?,
      attendant_driver_id: json['attendant_driver_id'] as String?,
      type__blood_id: json['type__blood_id'] as String?,
      religion_id: json['religion_id'] as String?,
      gender_id: json['gender_id'] as String?,
      city_name: json['city_name'] as String?,
      parent_key: json['parent_key'] as String?,
      latitude: (json['latitude'] as num?)?.toDouble(),
      longitude: (json['longitude'] as num?)?.toDouble(),
      grade_id: json['grade_id'] as String?,
      trip_type: json['trip_type'] as String?,
      parent_secret: json['parent_secret'] as String?,
      Date_Birth: json['Date_Birth'] as String?,
      logo: json['logo'] as String?,
    );

Map<String, dynamic> _$AbsencesStudentModelsToJson(
        AbsencesStudentModels instance) =>
    <String, dynamic>{
      'id': instance.id,
      'name': instance.name,
      'phone': instance.phone,
      'grade_id': instance.grade_id,
      'gender_id': instance.gender_id,
      'school_id': instance.school_id,
      'religion_id': instance.religion_id,
      'type__blood_id': instance.type__blood_id,
      'classroom_id': instance.classroom_id,
      'bus_id': instance.bus_id,
      'address': instance.address,
      'city_name': instance.city_name,
      'status': instance.status,
      'trip_type': instance.trip_type,
      'attendant_driver_id': instance.attendant_driver_id,
      'attendant_admins_id': instance.attendant_admins_id,
      'parent_key': instance.parent_key,
      'parent_secret': instance.parent_secret,
      'Date_Birth': instance.Date_Birth,
      'logo': instance.logo,
      'latitude': instance.latitude,
      'longitude': instance.longitude,
      'deleted_at': instance.deleted_at,
      'created_at': instance.created_at,
      'updated_at': instance.updated_at,
      'logo_path': instance.logo_path,
    };
