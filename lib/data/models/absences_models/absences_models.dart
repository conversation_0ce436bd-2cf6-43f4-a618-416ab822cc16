import 'package:bus/data/models/absences_models/absences_data_models.dart';
import 'package:equatable/equatable.dart';
import 'package:json_annotation/json_annotation.dart';

part 'absences_models.g.dart';

@JsonSerializable()
class AbsencesModels extends Equatable {
  final bool? status;
  final String? message;
  final AbsencesDataModels? data;

  const AbsencesModels({
    this.status,
    this.message,
    this.data,
  });

  factory AbsencesModels.fromJson(Map<String, dynamic> json) {
    return _$AbsencesModelsFromJson(json);
  }

  Map<String, dynamic> toJson() => _$AbsencesModelsToJson(this);

  @override
  List<Object?> get props => [
        status,
        message,
        data,
      ];
}
