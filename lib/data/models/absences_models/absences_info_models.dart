import 'package:bus/data/models/absences_models/absences_parent_models.dart';
import 'package:bus/data/models/absences_models/absences_student_models.dart';
import 'package:bus/data/models/student_models/student_bus_models.dart';
import 'package:bus/data/models/student_models/student_classRoom_models.dart';
import 'package:bus/data/models/student_models/student_grade_models.dart';
import 'package:equatable/equatable.dart';
import 'package:json_annotation/json_annotation.dart';

import '../auth_models/login_models/login_data_models.dart';

part 'absences_info_models.g.dart';

@JsonSerializable()
class AbsencesInfoModels extends Equatable {
  final int? id;
  final String? grade_id;
  final String? school_id;
  final String? classroom_id;
  final String? bus_id;
  final String? my__parent_id;
  final String? student_id;
  final String? attendence_date;
  final String? attendence_type;
  final String? created_at;
  final String? updated_at;
  final LoginDataModels? schools;
  final StudentBusModels? bus;
  final StudentGradeModels? grade;
  final StudentClassRoomModels? classroom;
  final AbsencesParentModels? parent;
  final AbsencesStudentModels? students;

  const AbsencesInfoModels({
    this.id,
    this.created_at,
    this.school_id,
    this.updated_at,
    this.student_id,
    this.my__parent_id,
    this.attendence_type,
    this.attendence_date,
    this.bus_id,
    this.classroom_id,
    this.grade_id,
    this.grade,
    this.classroom,
    this.bus,
    this.schools,
    this.parent,
    this.students,
  });
  factory AbsencesInfoModels.fromJson(Map<String, dynamic> json) {
    return _$AbsencesInfoModelsFromJson(json);
  }

  Map<String, dynamic> toJson() => _$AbsencesInfoModelsToJson(this);
  @override
  List<Object?> get props => [
        id,
        created_at,
        school_id,
        updated_at,
        student_id,
        my__parent_id,
        attendence_type,
        attendence_date,
        bus_id,
        classroom_id,
        grade_id,
        grade,
        classroom,
        bus,
        schools,
        parent,
        students,
      ];
}
