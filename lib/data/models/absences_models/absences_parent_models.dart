import 'package:equatable/equatable.dart';
import 'package:json_annotation/json_annotation.dart';

part 'absences_parent_models.g.dart';

@JsonSerializable()
class AbsencesParentModels extends Equatable {
  final int? id;
  final String? name;
  final String? email;
  final String? phone;
  final String? email_verified_at;
  final String? deleted_at;
  final String? address;
  final String? status;
  final String? logo;
  final String? typeAuth;
  final String? created_at;
  final String? updated_at;
  final String? logo_path;

  const AbsencesParentModels({
    this.name,
    this.updated_at,
    this.created_at,
    this.id,
    this.status,
    this.phone,
    this.deleted_at,
    this.logo_path,
    this.typeAuth,
    this.email_verified_at,
    this.address,
    this.email,
    this.logo,
  });

  factory AbsencesParentModels.fromJson(Map<String, dynamic> json) {
    return _$AbsencesParentModelsFromJson(json);
  }

  Map<String, dynamic> toJson() => _$AbsencesParentModelsToJson(this);

  @override
  List<Object?> get props => [
        name,
        updated_at,
        created_at,
        id,
        status,
        phone,
        deleted_at,
        logo_path,
        typeAuth,
        email_verified_at,
        address,
        email,
        logo,
      ];
}
