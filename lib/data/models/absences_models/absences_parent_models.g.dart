// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'absences_parent_models.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

AbsencesParentModels _$AbsencesParentModelsFromJson(
        Map<String, dynamic> json) =>
    AbsencesParentModels(
      name: json['name'] as String?,
      updated_at: json['updated_at'] as String?,
      created_at: json['created_at'] as String?,
      id: (json['id'] as num?)?.toInt(),
      status: json['status'] as String?,
      phone: json['phone'] as String?,
      deleted_at: json['deleted_at'] as String?,
      logo_path: json['logo_path'] as String?,
      typeAuth: json['typeAuth'] as String?,
      email_verified_at: json['email_verified_at'] as String?,
      address: json['address'] as String?,
      email: json['email'] as String?,
      logo: json['logo'] as String?,
    );

Map<String, dynamic> _$AbsencesParentModelsToJson(
        AbsencesParentModels instance) =>
    <String, dynamic>{
      'id': instance.id,
      'name': instance.name,
      'email': instance.email,
      'phone': instance.phone,
      'email_verified_at': instance.email_verified_at,
      'deleted_at': instance.deleted_at,
      'address': instance.address,
      'status': instance.status,
      'logo': instance.logo,
      'typeAuth': instance.typeAuth,
      'created_at': instance.created_at,
      'updated_at': instance.updated_at,
      'logo_path': instance.logo_path,
    };
