import 'dart:convert';

import '../buses_models/buses_info_models.dart';
import '../parent_models/parent_model.dart';

class StudentModel {
  String? name;
  String? phone;
  String? city_name;
  String? address;
  String? logo_path;
  String? updated_at;
  String? created_at;
  String? deleted_at;
  String? logo;
  String? longitude;
  String? latitude;
  String? attendant_admins_id;
  String? attendant_driver_id;
  int? bus_id;
  int? classroom_id;
  String? Date_Birth;
  int? gender_id;
  int? grade_id;
  String? parent_key;
  String? parent_secret;
  int? religion_id;
  int? school_id;
  String? trip_type;
  int? type__blood_id;
  String? schools;
  String? gender;
  String? religion;
  TypeBlood? type_blood;
  BusesInfoModel? bus;
  String? classroom;
  String? attendant_admins;
  String? attendant_driver;
  String? attendance;
  String? id;
  GradeModel? grade;
  List<DataInfo>? parent;

  StudentModel({
    this.name,
    this.phone,
    this.city_name,
    this.address,
    this.logo_path,
    this.updated_at,
    this.created_at,
    this.deleted_at,
    this.logo,
    this.longitude,
    this.latitude,
    this.attendant_admins_id,
    this.attendant_driver_id,
    this.bus_id,
    this.classroom_id,
    this.Date_Birth,
    this.gender_id,
    this.grade_id,
    this.parent_key,
    this.parent_secret,
    this.religion_id,
    this.school_id,
    this.trip_type,
    this.type__blood_id,
    this.schools,
    this.gender,
    this.religion,
    this.type_blood,
    this.bus,
    this.classroom,
    this.attendant_admins,
    this.attendant_driver,
    this.attendance,
    this.id,
    this.grade,
    this.parent
  });

  StudentModel copyWith({
    String? name,
    String? phone,
    String? city_name,
    String? address,
    String? logo_path,
    String? updated_at,
    String? created_at,
    String? deleted_at,
    String? logo,
    String? longitude,
    String? latitude,
    String? attendant_admins_id,
    String? attendant_driver_id,
    int? bus_id,
    int? classroom_id,
    String? Date_Birth,
    int? gender_id,
    int? grade_id,
    String? parent_key,
    String? parent_secret,
    int? religion_id,
    int? school_id,
    String? trip_type,
    int? type__blood_id,
    String? schools,
    String? gender,
    String? religion,
    TypeBlood? type_blood,
    BusesInfoModel? bus,
    String? classroom,
    String? attendant_admins,
    String? attendant_driver,
    String? attendance,
    String? id,
    GradeModel? grade,
    List<DataInfo>? parent,
  }) {
    return StudentModel(
      name: name ?? this.name,
      phone: phone ?? this.phone,
      city_name: city_name ?? this.city_name,
      address: address ?? this.address,
      logo_path: logo_path ?? this.logo_path,
      updated_at: updated_at ?? this.updated_at,
      created_at: created_at ?? this.created_at,
      deleted_at: deleted_at ?? this.deleted_at,
      logo: logo ?? this.logo,
      longitude: longitude ?? this.longitude,
      latitude: latitude ?? this.latitude,
      attendant_admins_id: attendant_admins_id ?? this.attendant_admins_id,
      attendant_driver_id: attendant_driver_id ?? this.attendant_driver_id,
      bus_id: bus_id ?? this.bus_id,
      classroom_id: classroom_id ?? this.classroom_id,
      Date_Birth: Date_Birth ?? this.Date_Birth,
      gender_id: gender_id ?? this.gender_id,
      grade_id: grade_id ?? this.grade_id,
      parent_key: parent_key ?? this.parent_key,
      parent_secret: parent_secret ?? this.parent_secret,
      religion_id: religion_id ?? this.religion_id,
      school_id: school_id ?? this.school_id,
      trip_type: trip_type ?? this.trip_type,
      type__blood_id: type__blood_id ?? this.type__blood_id,
      schools: schools ?? this.schools,
      gender: gender ?? this.gender,
      religion: religion ?? this.religion,
      type_blood: type_blood ?? this.type_blood,
      bus: bus ?? this.bus,
      classroom: classroom ?? this.classroom,
      attendant_admins: attendant_admins ?? this.attendant_admins,
      attendant_driver: attendant_driver ?? this.attendant_driver,
      attendance: attendance ?? this.attendance,
      id: id ?? this.id,
      grade: grade ?? this.grade,
      parent: parent ?? this.parent
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'name': name,
      'phone': phone,
      'city_name': city_name,
      'address': address,
      'logo_path': logo_path,
      'updated_at': updated_at,
      'created_at': created_at,
      'deleted_at': deleted_at,
      'logo': logo,
      'longitude': longitude,
      'latitude': latitude,
      'attendant_admins_id': attendant_admins_id,
      'attendant_driver_id': attendant_driver_id,
      'bus_id': bus_id,
      'classroom_id': classroom_id,
      'Date_Birth': Date_Birth,
      'gender_id': gender_id,
      'grade_id': grade_id,
      'parent_key': parent_key,
      'parent_secret': parent_secret,
      'religion_id': religion_id,
      'school_id': school_id,
      'trip_type': trip_type,
      'type__blood_id': type__blood_id,
      'schools': schools,
      'gender': gender,
      'religion': religion,
      'type_blood': type_blood?.toMap(),
      'bus': bus,
      'classroom': classroom,
      'attendant_admins': attendant_admins,
      'attendant_driver': attendant_driver,
      'attendance': attendance,
      'id': id,
      'grade': grade?.toMap(),
      'my__parents': parent
    };
  }

  factory StudentModel.fromMap(Map<String, dynamic> map) {
    return StudentModel(
      name: map['name'],
      phone: map['phone'],
      city_name: map['city_name'],
      address: map['address'],
      logo_path: map['logo_path'],
      updated_at: map['updated_at'],
      created_at: map['created_at'],
      deleted_at: map['deleted_at'],
      logo: map['logo'],
      longitude: map['longitude'],
      latitude: map['latitude'],
      attendant_admins_id: map['attendant_admins_id'],
      attendant_driver_id: map['attendant_driver_id'],
      bus_id: map['bus_id'],
      classroom_id: map['classroom_id'],
      Date_Birth: map['Date_Birth'],
      gender_id: map['gender_id'],
      grade_id: map['grade_id'],
      parent_key: map['parent_key'],
      parent_secret: map['parent_secret'],
      religion_id: map['religion_id'],
      school_id: map['school_id'],
      trip_type: map['trip_type'],
      type__blood_id: map['type__blood_id'],
      schools: map['schools'],
      //gender: map['gender'],
      //religion: map['religion'],
      type_blood: map['type_blood'] != null
          ? TypeBlood.fromMap(map['type_blood'])
          : null,
      bus: map['bus'] != null ? BusesInfoModel.fromJson(map['bus']) : null,
      //classroom: map['classroom'],
      attendant_admins: map['attendant_admins'],
      attendant_driver: map['attendant_driver'],
      attendance: map['attendance'],
      id: map['id']?.toString(),
      grade: map['grade'] != null ? GradeModel.fromMap(map['grade']) : null,
      parent: map['my__parents'] == null? [] : List<DataInfo>.from(map["my__parents"]!.map((x) => DataInfo.fromJson(x))),
    );
  }

  String toJson() => json.encode(toMap());

  factory StudentModel.fromJson(Map<String, dynamic> source) =>
      StudentModel.fromMap(source);

  @override
  String toString() {
    return 'StudentModel(name: $name, phone: $phone, city_name: $city_name, address: $address, logo_path: $logo_path, updated_at: $updated_at, created_at: $created_at, deleted_at: $deleted_at, logo: $logo, longitude: $longitude, latitude: $latitude, attendant_admins_id: $attendant_admins_id, attendant_driver_id: $attendant_driver_id, bus_id: $bus_id, classroom_id: $classroom_id, Date_Birth: $Date_Birth, gender_id: $gender_id, grade_id: $grade_id, parent_key: $parent_key, parent_secret: $parent_secret, religion_id: $religion_id, school_id: $school_id, trip_type: $trip_type, type__blood_id: $type__blood_id, schools: $schools, gender: $gender, religion: $religion, type_blood: $type_blood, bus: $bus, classroom: $classroom, attendant_admins: $attendant_admins, attendant_driver: $attendant_driver, attendance: $attendance, id: $id, grade: $grade, parent: $parent)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;

    return other is StudentModel &&
        other.name == name &&
        other.phone == phone &&
        other.city_name == city_name &&
        other.address == address &&
        other.logo_path == logo_path &&
        other.updated_at == updated_at &&
        other.created_at == created_at &&
        other.deleted_at == deleted_at &&
        other.logo == logo &&
        other.longitude == longitude &&
        other.latitude == latitude &&
        other.attendant_admins_id == attendant_admins_id &&
        other.attendant_driver_id == attendant_driver_id &&
        other.bus_id == bus_id &&
        other.classroom_id == classroom_id &&
        other.Date_Birth == Date_Birth &&
        other.gender_id == gender_id &&
        other.grade_id == grade_id &&
        other.parent_key == parent_key &&
        other.parent_secret == parent_secret &&
        other.religion_id == religion_id &&
        other.school_id == school_id &&
        other.trip_type == trip_type &&
        other.type__blood_id == type__blood_id &&
        other.schools == schools &&
        other.gender == gender &&
        other.religion == religion &&
        other.type_blood == type_blood &&
        other.bus == bus &&
        other.classroom == classroom &&
        other.attendant_admins == attendant_admins &&
        other.attendant_driver == attendant_driver &&
        other.attendance == attendance &&
        other.id == id &&
        other.grade == grade &&
        other.parent == parent;
  }

  @override
  int get hashCode {
    return name.hashCode ^
        phone.hashCode ^
        city_name.hashCode ^
        address.hashCode ^
        logo_path.hashCode ^
        updated_at.hashCode ^
        created_at.hashCode ^
        deleted_at.hashCode ^
        logo.hashCode ^
        longitude.hashCode ^
        latitude.hashCode ^
        attendant_admins_id.hashCode ^
        attendant_driver_id.hashCode ^
        bus_id.hashCode ^
        classroom_id.hashCode ^
        Date_Birth.hashCode ^
        gender_id.hashCode ^
        grade_id.hashCode ^
        parent_key.hashCode ^
        parent_secret.hashCode ^
        religion_id.hashCode ^
        school_id.hashCode ^
        trip_type.hashCode ^
        type__blood_id.hashCode ^
        schools.hashCode ^
        gender.hashCode ^
        religion.hashCode ^
        type_blood.hashCode ^
        bus.hashCode ^
        classroom.hashCode ^
        attendant_admins.hashCode ^
        attendant_driver.hashCode ^
        attendance.hashCode ^
        id.hashCode ^
        grade.hashCode ^
        parent.hashCode;
  }
}

class TypeBlood {
  String? name;
  int? id;

  TypeBlood({
    this.name,
    this.id,
  });

  TypeBlood copyWith({
    String? name,
    int? id,
  }) {
    return TypeBlood(
      name: name ?? this.name,
      id: id ?? this.id,
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'name': name,
      'id': id,
    };
  }

  factory TypeBlood.fromMap(Map<String, dynamic> map) {
    return TypeBlood(
      name: map['name'],
      id: map['id'],
    );
  }

  String toJson() => json.encode(toMap());

  factory TypeBlood.fromJson(String source) =>
      TypeBlood.fromMap(json.decode(source));

  @override
  String toString() => 'TypeBlood(name: $name, id: $id)';

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;

    return other is TypeBlood && other.name == name && other.id == id;
  }

  @override
  int get hashCode => name.hashCode ^ id.hashCode;
}

class GradeModel {
  String? name;

  GradeModel({
    this.name,
  });

  GradeModel copyWith({
    String? name,
  }) {
    return GradeModel(
      name: name ?? this.name,
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'name': name,
    };
  }

  factory GradeModel.fromMap(Map<String, dynamic> map) {
    return GradeModel(
      name: map['name'],
    );
  }

  String toJson() => json.encode(toMap());

  factory GradeModel.fromJson(String source) =>
      GradeModel.fromMap(json.decode(source));

  @override
  String toString() => 'GradeModel(name: $name)';

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;

    return other is GradeModel && other.name == name;
  }

  @override
  int get hashCode => name.hashCode;
}
