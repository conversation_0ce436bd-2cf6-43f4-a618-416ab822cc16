import 'package:bus/data/models/student_models/student_model.dart';

class EmptyBusStudentsResponse {
  final int currentPage;
  final List<StudentModel> data;
  final String firstPageUrl;
  final int from;
  final int lastPage;
  final String lastPageUrl;
  final String? nextPageUrl;
  final String path;
  final int perPage;
  final String? prevPageUrl;
  final int to;
  final int total;

  EmptyBusStudentsResponse({
    required this.currentPage,
    required this.data,
    required this.firstPageUrl,
    required this.from,
    required this.lastPage,
    required this.lastPageUrl,
    this.nextPageUrl,
    required this.path,
    required this.perPage,
    this.prevPageUrl,
    required this.to,
    required this.total,
  });

  factory EmptyBusStudentsResponse.fromMap(Map<String, dynamic> map) {
    return EmptyBusStudentsResponse(
      currentPage: map['current_page'] ?? 1,
      data: List<StudentModel>.from(
        (map['data'] as List<dynamic>).map((x) => StudentModel.fromMap(x)),
      ),
      firstPageUrl: map['first_page_url'] ?? '',
      from: map['from'] ?? 0,
      lastPage: map['last_page'] ?? 1,
      lastPageUrl: map['last_page_url'] ?? '',
      nextPageUrl: map['next_page_url'],
      path: map['path'] ?? '',
      perPage: map['per_page'] ?? 10,
      prevPageUrl: map['prev_page_url'],
      to: map['to'] ?? 0,
      total: map['total'] ?? 0,
    );
  }
}
