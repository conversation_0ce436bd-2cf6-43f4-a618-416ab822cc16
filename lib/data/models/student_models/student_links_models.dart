import 'package:equatable/equatable.dart';
import 'package:json_annotation/json_annotation.dart';

part 'student_links_models.g.dart';

@JsonSerializable()
class StudentLinksModels extends Equatable {
  final String? url;
  final String? label;
  final bool? active;

  const StudentLinksModels({
    this.url,
    this.active,
    this.label,
  });

  factory StudentLinksModels.fromJson(Map<String, dynamic> json) {
    return _$StudentLinksModelsFromJson(json);
  }

  Map<String, dynamic> toJson() => _$StudentLinksModelsToJson(this);


  @override
  List<Object?> get props => [
        url,
        active,
        label,
      ];
}
