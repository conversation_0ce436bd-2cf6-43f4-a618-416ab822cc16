import 'package:equatable/equatable.dart';
import 'package:json_annotation/json_annotation.dart';

part 'student_classRoom_models.g.dart';

@JsonSerializable()
class StudentClassRoomModels extends Equatable {
  final int? id;
  final String? name;
  final String? status;
  final String? school_id;
  final String? grade_id;
  final String? created_at;
  final String? updated_at;

  const StudentClassRoomModels({
    this.updated_at,
    this.name,
    this.id,
    this.status,
    this.school_id,
    this.created_at,
    this.grade_id,
  });

  factory StudentClassRoomModels.fromJson(Map<String, dynamic> json) {
    return _$StudentClassRoomModelsFromJson(json);
  }

  Map<String, dynamic> toJson() => _$StudentClassRoomModelsToJson(this);

  @override
  List<Object?> get props => [
        updated_at,
        name,
        id,
        status,
        school_id,
        created_at,
        grade_id,
      ];
}
