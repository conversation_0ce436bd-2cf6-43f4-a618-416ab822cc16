import 'package:bus/data/models/student_models/student_data_models.dart';
import 'package:equatable/equatable.dart';
import 'package:json_annotation/json_annotation.dart';

part 'student_models.g.dart';

@JsonSerializable()
class StudentModels extends Equatable {
  final String? message;
  final bool? status;
  final StudentDataModels? data;

  StudentModels({
    this.message,
    this.status,
    this.data,
  });

  factory StudentModels.fromJson(Map<String, dynamic> json) {
    return _$StudentModelsFromJson(json);
  }

  Map<String, dynamic> toJson() => _$StudentModelsToJson(this);

  @override
  List<Object?> get props => [
        message,
        status,
        data,
      ];
}
