import 'package:equatable/equatable.dart';
import 'package:json_annotation/json_annotation.dart';

part 'student_grade_models.g.dart';

@JsonSerializable()
class StudentGradeModels extends Equatable {
  final int? id;
  final String? name;
  final String? notes;
  final int? status;
  final String? created_at;
  final String? updated_at;

  const StudentGradeModels({
    this.notes,
    this.status,
    this.id,
    this.name,
    this.updated_at,
    this.created_at,
  });

  factory StudentGradeModels.fromJson(Map<String, dynamic> json) {
    return _$StudentGradeModelsFromJson(json);
  }

  Map<String, dynamic> toJson() => _$StudentGradeModelsToJson(this);

  @override
  List<Object?> get props => [
        notes,
        status,
        id,
        name,
        updated_at,
        created_at,
      ];
}
