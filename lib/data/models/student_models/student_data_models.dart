import 'package:bus/data/models/student_models/student_info_models.dart';
import 'package:bus/data/models/student_models/student_links_models.dart';
import 'package:equatable/equatable.dart';
import 'package:json_annotation/json_annotation.dart';

part 'student_data_models.g.dart';

@JsonSerializable()
class StudentDataModels extends Equatable {
  final int? current_page;
  final String? first_page_url;
  final int? from;
  final int? last_page;
  final String? last_page_url;
  final String? next_page_url;
  final String? path;
  final int? per_page;
  final String? prev_page_url;
  final int? to;
  final int? total;
  final List<StudentLinksModels>? links;
  final List<StudentInfoModels>? data;

  const StudentDataModels({
    this.path,
    this.from,
    this.current_page,
    this.first_page_url,
    this.last_page,
    this.last_page_url,
    this.next_page_url,
    this.per_page,
    this.prev_page_url,
    this.to,
    this.total,
    this.links,
    this.data,
  });

  factory StudentDataModels.fromJson(Map<String, dynamic> json) {
    return _$StudentDataModelsFromJson(json);
  }

  Map<String, dynamic> toJson() => _$StudentDataModelsToJson(this);

  @override
  List<Object?> get props => [
        path,
        from,
        current_page,
        first_page_url,
        last_page,
        last_page_url,
        next_page_url,
        per_page,
        prev_page_url,
        to,
        total,
        links,
        data,
      ];
}
