// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'student_attendance_models.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

StudentAttendanceModels _$StudentAttendanceModelsFromJson(
        Map<String, dynamic> json) =>
    StudentAttendanceModels(
      type: json['type'] as String?,
      id: (json['id'] as num?)?.toInt(),
      updated_at: json['updated_at'] as String?,
      school_id: json['school_id'] as String?,
      created_at: json['created_at'] as String?,
      grade_id: json['grade_id'] as String?,
      attendant_admins_id: json['attendant_admins_id'] as String?,
      attendant_driver_id: json['attendant_driver_id'] as String?,
      classroom_id: json['classroom_id'] as String?,
      bus_id: json['bus_id'] as String?,
      attendence_date: json['attendence_date'] as String?,
      attendence_status: json['attendence_status'] as String?,
      attendence_type: json['attendence_type'] as String?,
      my__parent_id: json['my__parent_id'] as String?,
      student_id: json['student_id'] as String?,
      trip_id: json['trip_id'] as String?,
    );

Map<String, dynamic> _$StudentAttendanceModelsToJson(
        StudentAttendanceModels instance) =>
    <String, dynamic>{
      'id': instance.id,
      'grade_id': instance.grade_id,
      'school_id': instance.school_id,
      'classroom_id': instance.classroom_id,
      'bus_id': instance.bus_id,
      'attendant_driver_id': instance.attendant_driver_id,
      'attendant_admins_id': instance.attendant_admins_id,
      'my__parent_id': instance.my__parent_id,
      'student_id': instance.student_id,
      'attendence_date': instance.attendence_date,
      'trip_id': instance.trip_id,
      'type': instance.type,
      'attendence_type': instance.attendence_type,
      'attendence_status': instance.attendence_status,
      'created_at': instance.created_at,
      'updated_at': instance.updated_at,
    };
