import 'dart:convert';

class CouponModel {
  DataSubscribeModels? data;
  String? message;
  bool? status;
  bool? errors;
  String? messages;

  CouponModel({
    this.data,
    this.message,
    this.status,
    this.errors,
    this.messages,
  });

  factory CouponModel.fromJson(String str) =>
      CouponModel.fromMap(json.decode(str));

  String toJson() => json.encode(toMap());

  factory CouponModel.fromMap(Map<String, dynamic> json) => CouponModel(
        data: json["data"] != null
            ? DataSubscribeModels.fromJson(json['data'])
            : null,
        message: json["message"],
        status: json["status"],
        errors: json["errors"],
        messages: json["messages"],
      );

  Map<String, dynamic> toMap() => {
        "data": data!.toJson(),
        "message": message,
        "status": status,
        "errors": errors,
        "messages": messages,
      };
}

class DataSubscribeModels {
  String? planName;
  String? amount;
  String? startDate;
  String? endDate;
  String? paymentMethod;
  String? status;
  int? parantId;
  String? updatedAt;
  String? createdAt;
  int? id;

  DataSubscribeModels(
      {this.planName,
      this.amount,
      this.startDate,
      this.endDate,
      this.paymentMethod,
      this.status,
      this.parantId,
      this.updatedAt,
      this.createdAt,
      this.id});

  DataSubscribeModels.fromJson(Map<String, dynamic> json) {
    planName = json['plan_name'];
    amount = json['amount'];
    startDate = json['start_date'];
    endDate = json['end_date'];
    paymentMethod = json['payment_method'];
    status = json['status'];
    parantId = json['parant_id'];
    updatedAt = json['updated_at'];
    createdAt = json['created_at'];
    id = json['id'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['plan_name'] = planName;
    data['amount'] = amount;
    data['start_date'] = startDate;
    data['end_date'] = endDate;
    data['payment_method'] = paymentMethod;
    data['status'] = status;
    data['parant_id'] = parantId;
    data['updated_at'] = updatedAt;
    data['created_at'] = createdAt;
    data['id'] = id;
    return data;
  }
}
