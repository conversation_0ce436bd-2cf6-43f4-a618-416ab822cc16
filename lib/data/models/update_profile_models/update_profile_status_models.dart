import 'package:equatable/equatable.dart';
import 'package:json_annotation/json_annotation.dart';

part 'update_profile_status_models.g.dart';

@JsonSerializable()
class UpdateProfileStatusModels extends Equatable {
  final int? status;
  final String? messages;

  const UpdateProfileStatusModels({
    this.messages,
    this.status,
  });

  factory UpdateProfileStatusModels.fromJson(Map<String, dynamic> json) {
    return _$UpdateProfileStatusModelsFromJson(json);
  }

  Map<String, dynamic> toJson() => _$UpdateProfileStatusModelsToJson(this);

  @override
  List<Object?> get props => [
        messages,
        status,
      ];
}
