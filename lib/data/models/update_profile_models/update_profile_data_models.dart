import 'package:equatable/equatable.dart';
import 'package:json_annotation/json_annotation.dart';

part 'update_profile_data_models.g.dart';

@JsonSerializable()
class UpdateProfileDataModels extends Equatable {
  final int? id;
  final String? name;
  final String? email;
  final String? phone;
  final String? email_verified_at;
  final String? address;
  final String? city_name;
  final int? status;
  final String? logo;
  final String? created_at;
  final String? updated_at;
  final String? typeAuth;
  final String? latitude;
  final String? longitude;
  final String? firebase_token;
  final String? logo_path;

  const UpdateProfileDataModels({
    this.name,
    this.id,
    this.status,
    this.address,
    this.longitude,
    this.latitude,
    this.city_name,
    this.created_at,
    this.email,
    this.email_verified_at,
    this.firebase_token,
    this.logo,
    this.logo_path,
    this.phone,
    this.typeAuth,
    this.updated_at,
  });


  factory UpdateProfileDataModels.fromJson(Map<String, dynamic> json) {
    return _$UpdateProfileDataModelsFromJson(json);
  }

  Map<String, dynamic> toJson() => _$UpdateProfileDataModelsToJson(this);


  @override
  List<Object?> get props => [
        name,
        id,
        status,
        address,
        longitude,
        latitude,
        city_name,
        created_at,
        email,
        email_verified_at,
        firebase_token,
        logo,
        logo_path,
        phone,
        typeAuth,
        updated_at,
      ];
}
