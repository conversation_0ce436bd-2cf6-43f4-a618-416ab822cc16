import 'package:bus/data/models/update_profile_models/update_profile_data_models.dart';
import 'package:bus/data/models/update_profile_models/update_profile_status_models.dart';
import 'package:equatable/equatable.dart';
import 'package:json_annotation/json_annotation.dart';

part 'update_profile_models.g.dart';

@JsonSerializable()
class UpdateProfileModels extends Equatable {
  final UpdateProfileStatusModels? status;
  final UpdateProfileDataModels? data;

  const UpdateProfileModels({
    this.status,
    this.data,
  });

  factory UpdateProfileModels.fromJson(Map<String, dynamic> json) {
    return _$UpdateProfileModelsFromJson(json);
  }

  Map<String, dynamic> toJson() => _$UpdateProfileModelsToJson(this);

  @override
  List<Object?> get props => [
        status,
        data,
      ];
}
