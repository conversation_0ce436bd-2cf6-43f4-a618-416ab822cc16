import 'package:bus/data/models/driver_models/driver_info_models.dart';
import 'package:bus/data/models/student_models/student_links_models.dart';
import 'package:equatable/equatable.dart';
import 'package:json_annotation/json_annotation.dart';

part 'driver_data_models.g.dart';

@JsonSerializable()
class DriverDataModels extends Equatable {
  final int? current_page;
  final String? first_page_url;
  final int? from;
  final int? last_page;
  final String? last_page_url;
  final String? next_page_url;
  final String? path;
  final int? per_page;
  final String? prev_page_url;
  final int? to;
  final int? total;
  final List<StudentLinksModels>? links;
  final List<DriverInfoModels>? data;

  const DriverDataModels({
    this.current_page,
    this.last_page,
    this.to,
    this.prev_page_url,
    this.per_page,
    this.next_page_url,
    this.last_page_url,
    this.first_page_url,
    this.from,
    this.path,
    this.total,
    this.links,
    this.data,
  });

  factory DriverDataModels.fromJson(Map<String, dynamic> json) {
    return _$DriverDataModelsFromJson(json);
  }

  Map<String, dynamic> toJson() => _$DriverDataModelsToJson(this);


  @override
  List<Object?> get props => [
        current_page,
        last_page,
        to,
        prev_page_url,
        per_page,
        next_page_url,
        last_page_url,
        first_page_url,
        from,
        path,
        total,
        links,
        data,
      ];
}
