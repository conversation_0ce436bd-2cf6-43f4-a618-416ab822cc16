import 'package:bus/data/models/student_models/student_grade_models.dart';
import 'package:equatable/equatable.dart';
import 'package:json_annotation/json_annotation.dart';

import '../auth_models/login_models/login_data_models.dart';
import '../buses_models/buses_info_models.dart';

part 'driver_info_models.g.dart';

@JsonSerializable()
class DriverInfoModels extends Equatable {
  final int? id;
  final String? email;
  final String? username;
  final String? name;
  final int? gender_id;
  final int? school_id;
  final int? religion_id;
  final int? type__blood_id;
  final String? Joining_Date;
  final String? address;
  final int? bus_id;
  final String? city_name;
  final int? status;
  final String? logo;
  final String? type;
  final String? phone;
  final String? birth_date;
  final String? email_verified_at;
  final String? deleted_at;
  final String? created_at;
  final String? updated_at;
  final String? typeAuth;
  final String? logo_path;
  final LoginDataModels? schools;
  final StudentGradeModels? gender;
  final StudentGradeModels? religion;
  final StudentGradeModels? type_blood;
  final BusesInfoModel? bus;

  const DriverInfoModels(
      {this.username,
      this.bus_id,
      this.status,
      this.name,
      this.email,
      this.address,
      this.id,
      this.phone,
      this.type,
      this.created_at,
      this.school_id,
      this.updated_at,
      this.email_verified_at,
      this.Joining_Date,
      this.birth_date,
      this.typeAuth,
      this.logo_path,
      this.city_name,
      this.deleted_at,
      this.gender_id,
      this.religion_id,
      this.type__blood_id,
      this.logo,
      this.schools,
      this.type_blood,
      this.religion,
      this.gender,
      this.bus});

  factory DriverInfoModels.fromJson(Map<String, dynamic> json) {
    return _$DriverInfoModelsFromJson(json);
  }

  Map<String, dynamic> toJson() => _$DriverInfoModelsToJson(this);

  @override
  List<Object?> get props => [
        status,
        name,
        email,
        address,
        id,
        phone,
        type,
        created_at,
        school_id,
        updated_at,
        email_verified_at,
        Joining_Date,
        birth_date,
        typeAuth,
        logo_path,
        city_name,
        deleted_at,
        gender_id,
        religion_id,
        type__blood_id,
        logo,
        schools,
        gender,
        type_blood,
        religion,
        bus
      ];
}
