import 'package:bus/data/models/driver_models/driver_data_models.dart';
import 'package:equatable/equatable.dart';
import 'package:json_annotation/json_annotation.dart';

part 'driver_models.g.dart';

@JsonSerializable()
class DriverModels extends Equatable {
  final bool? status;
  final String? message;
  final DriverDataModels? data;

  const DriverModels({
    this.message,
    this.status,
    this.data,
  });

  factory DriverModels.fromJson(Map<String, dynamic> json) {
    return _$DriverModelsFromJson(json);
  }

  Map<String, dynamic> toJson() => _$DriverModelsToJson(this);


  @override
  List<Object?> get props => [
        message,
        status,
        data,
      ];
}
