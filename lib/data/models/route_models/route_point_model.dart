import 'package:equatable/equatable.dart';
import 'package:json_annotation/json_annotation.dart';

part 'route_point_model.g.dart';

@JsonSerializable()
class RoutePointModel extends Equatable {
  final int? id;
  final int? trip_id;
  final int? bus_id;
  final String? latitude;
  final String? longitude;
  final String? type;
  final String? created_at;

  const RoutePointModel({
    this.id,
    this.trip_id,
    this.bus_id,
    this.latitude,
    this.longitude,
    this.type,
    this.created_at,
  });

  factory RoutePointModel.fromJson(Map<String, dynamic> json) =>
      _$RoutePointModelFromJson(json);

  Map<String, dynamic> toJson() => _$RoutePointModelToJson(this);

  @override
  List<Object?> get props =>
      [id, trip_id, bus_id, latitude, longitude, type, created_at];
}

@JsonSerializable()
class TripRouteModel extends Equatable {
  final int? trip_id;
  final String? start_time;
  final String? end_time;
  final double? total_distance;
  final int? estimated_time;
  final List<RoutePointModel>? route_points;

  const TripRouteModel({
    this.trip_id,
    this.start_time,
    this.end_time,
    this.total_distance,
    this.estimated_time,
    this.route_points,
  });

  factory TripRouteModel.fromJson(Map<String, dynamic> json) =>
      _$TripRouteModelFromJson(json);

  Map<String, dynamic> toJson() => _$TripRouteModelToJson(this);

  @override
  List<Object?> get props => [
        trip_id,
        start_time,
        end_time,
        total_distance,
        estimated_time,
        route_points,
      ];
}

@JsonSerializable()
class TripRouteResponse extends Equatable {
  final bool? status;
  final String? message;
  final TripRouteModel? data;

  const TripRouteResponse({
    this.status,
    this.message,
    this.data,
  });

  factory TripRouteResponse.fromJson(Map<String, dynamic> json) =>
      _$TripRouteResponseFromJson(json);

  Map<String, dynamic> toJson() => _$TripRouteResponseToJson(this);

  @override
  List<Object?> get props => [status, message, data];
}
