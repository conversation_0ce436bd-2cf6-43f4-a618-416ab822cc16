// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'route_point_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

RoutePointModel _$RoutePointModelFromJson(Map<String, dynamic> json) =>
    RoutePointModel(
      id: (json['id'] as num?)?.toInt(),
      trip_id: (json['trip_id'] as num?)?.toInt(),
      bus_id: (json['bus_id'] as num?)?.toInt(),
      latitude: json['latitude'] as String?,
      longitude: json['longitude'] as String?,
      type: json['type'] as String?,
      created_at: json['created_at'] as String?,
    );

Map<String, dynamic> _$RoutePointModelToJson(RoutePointModel instance) =>
    <String, dynamic>{
      'id': instance.id,
      'trip_id': instance.trip_id,
      'bus_id': instance.bus_id,
      'latitude': instance.latitude,
      'longitude': instance.longitude,
      'type': instance.type,
      'created_at': instance.created_at,
    };

TripRouteModel _$TripRouteModelFromJson(Map<String, dynamic> json) =>
    TripRouteModel(
      trip_id: (json['trip_id'] as num?)?.toInt(),
      start_time: json['start_time'] as String?,
      end_time: json['end_time'] as String?,
      total_distance: (json['total_distance'] as num?)?.toDouble(),
      estimated_time: (json['estimated_time'] as num?)?.toInt(),
      route_points: (json['route_points'] as List<dynamic>?)
          ?.map((e) => RoutePointModel.fromJson(e as Map<String, dynamic>))
          .toList(),
    );

Map<String, dynamic> _$TripRouteModelToJson(TripRouteModel instance) =>
    <String, dynamic>{
      'trip_id': instance.trip_id,
      'start_time': instance.start_time,
      'end_time': instance.end_time,
      'total_distance': instance.total_distance,
      'estimated_time': instance.estimated_time,
      'route_points': instance.route_points,
    };

TripRouteResponse _$TripRouteResponseFromJson(Map<String, dynamic> json) =>
    TripRouteResponse(
      status: json['status'] as bool?,
      message: json['message'] as String?,
      data: json['data'] == null
          ? null
          : TripRouteModel.fromJson(json['data'] as Map<String, dynamic>),
    );

Map<String, dynamic> _$TripRouteResponseToJson(TripRouteResponse instance) =>
    <String, dynamic>{
      'status': instance.status,
      'message': instance.message,
      'data': instance.data,
    };
