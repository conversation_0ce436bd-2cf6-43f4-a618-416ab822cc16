import 'dart:convert';

class CreatePasswordModel {
  Status? status;
  dynamic data;
  bool? errors;
  String? messages;

  CreatePasswordModel({
    this.status,
    this.data,
    this.errors,
    this.messages,
  });

  factory CreatePasswordModel.fromJson(dynamic str) {
    if (str is String) {
      return CreatePasswordModel.fromMap(json.decode(str));
    } else if (str is Map<String, dynamic>) {
      return CreatePasswordModel.fromMap(str);
    }
    throw const FormatException('Invalid input format for CreatePasswordModel');
  }

  String toJson() => json.encode(toMap());

  factory CreatePasswordModel.fromMap(Map<String, dynamic> json) =>
      CreatePasswordModel(
        status: json["status"] == null ? null : Status.fromMap(json["status"]),
        data: json["data"],
        errors: json["errors"],
        messages: json["messages"],
      );

  Map<String, dynamic> toMap() => {
        "status": status?.toMap(),
        "data": data,
        "errors": errors,
        "messages": messages,
      };
}

class Status {
  int? status;
  String? messages;

  Status({
    this.status,
    this.messages,
  });

  factory Status.fromJson(dynamic str) {
    if (str is String) {
      return Status.fromMap(json.decode(str));
    } else if (str is Map<String, dynamic>) {
      return Status.fromMap(str);
    }
    throw FormatException('Invalid input format for Status');
  }

  String toJson() => json.encode(toMap());

  factory Status.fromMap(Map<String, dynamic> json) => Status(
        status: json["status"],
        messages: json["messages"],
      );

  Map<String, dynamic> toMap() => {
        "status": status,
        "messages": messages,
      };
}
