import 'package:equatable/equatable.dart';
import 'package:json_annotation/json_annotation.dart';

part 'absent_student_model.g.dart';

@JsonSerializable()
class AbsentStudentModel extends Equatable {
  final String? id;
  final String? name;
  final String? phone;
  final int? grade_id;
  final int? gender_id;
  final int? school_id;
  final int? classroom_id;
  final int? bus_id;
  final String? address;
  final int? status;
  final String? trip_type;
  final String? parent_key;
  final String? parent_secret;
  final String? logo;
  final String? latitude;
  final String? longitude;
  final String? created_at;
  final String? updated_at;
  final String? logo_path;
  final AbsentStudentPivot? pivot;

  const AbsentStudentModel({
    this.id,
    this.name,
    this.phone,
    this.grade_id,
    this.gender_id,
    this.school_id,
    this.classroom_id,
    this.bus_id,
    this.address,
    this.status,
    this.trip_type,
    this.parent_key,
    this.parent_secret,
    this.logo,
    this.latitude,
    this.longitude,
    this.created_at,
    this.updated_at,
    this.logo_path,
    this.pivot,
  });

  factory AbsentStudentModel.fromJson(Map<String, dynamic> json) =>
      _$AbsentStudentModelFromJson(json);

  Map<String, dynamic> toJson() => _$AbsentStudentModelToJson(this);

  @override
  List<Object?> get props => [
        id,
        name,
        phone,
        grade_id,
        gender_id,
        school_id,
        classroom_id,
        bus_id,
        address,
        status,
        trip_type,
        parent_key,
        parent_secret,
        logo,
        latitude,
        longitude,
        created_at,
        updated_at,
        logo_path,
        pivot,
      ];
}

@JsonSerializable()
class AbsentStudentPivot extends Equatable {
  final int? trip_id;
  final String? student_id;
  final int? is_absent;
  final String? onboard_at;
  final String? arrived_at;

  const AbsentStudentPivot({
    this.trip_id,
    this.student_id,
    this.is_absent,
    this.onboard_at,
    this.arrived_at,
  });

  factory AbsentStudentPivot.fromJson(Map<String, dynamic> json) =>
      _$AbsentStudentPivotFromJson(json);

  Map<String, dynamic> toJson() => _$AbsentStudentPivotToJson(this);

  @override
  List<Object?> get props => [
        trip_id,
        student_id,
        is_absent,
        onboard_at,
        arrived_at,
      ];
}

@JsonSerializable()
class AbsentStudentsResponse extends Equatable {
  final String? message;
  final bool? errors;
  final List<AbsentStudentModel>? absentStudents;

  const AbsentStudentsResponse({
    this.message,
    this.errors,
    this.absentStudents,
  });

  factory AbsentStudentsResponse.fromJson(Map<String, dynamic> json) =>
      _$AbsentStudentsResponseFromJson(json);

  Map<String, dynamic> toJson() => _$AbsentStudentsResponseToJson(this);

  @override
  List<Object?> get props => [message, errors, absentStudents];
}
