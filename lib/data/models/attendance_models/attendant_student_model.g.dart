// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'attendant_student_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

AttendantStudentModel _$AttendantStudentModelFromJson(
        Map<String, dynamic> json) =>
    AttendantStudentModel(
      id: json['id'] as String?,
      name: json['name'] as String?,
      phone: json['phone'] as String?,
      grade_id: (json['grade_id'] as num?)?.toInt(),
      gender_id: (json['gender_id'] as num?)?.toInt(),
      school_id: (json['school_id'] as num?)?.toInt(),
      classroom_id: (json['classroom_id'] as num?)?.toInt(),
      bus_id: (json['bus_id'] as num?)?.toInt(),
      address: json['address'] as String?,
      status: (json['status'] as num?)?.toInt(),
      trip_type: json['trip_type'] as String?,
      parent_key: json['parent_key'] as String?,
      parent_secret: json['parent_secret'] as String?,
      logo: json['logo'] as String?,
      latitude: json['latitude'] as String?,
      longitude: json['longitude'] as String?,
      created_at: json['created_at'] as String?,
      updated_at: json['updated_at'] as String?,
      logo_path: json['logo_path'] as String?,
      pivot: json['pivot'] == null
          ? null
          : AttendantStudentPivot.fromJson(
              json['pivot'] as Map<String, dynamic>),
    );

Map<String, dynamic> _$AttendantStudentModelToJson(
        AttendantStudentModel instance) =>
    <String, dynamic>{
      'id': instance.id,
      'name': instance.name,
      'phone': instance.phone,
      'grade_id': instance.grade_id,
      'gender_id': instance.gender_id,
      'school_id': instance.school_id,
      'classroom_id': instance.classroom_id,
      'bus_id': instance.bus_id,
      'address': instance.address,
      'status': instance.status,
      'trip_type': instance.trip_type,
      'parent_key': instance.parent_key,
      'parent_secret': instance.parent_secret,
      'logo': instance.logo,
      'latitude': instance.latitude,
      'longitude': instance.longitude,
      'created_at': instance.created_at,
      'updated_at': instance.updated_at,
      'logo_path': instance.logo_path,
      'pivot': instance.pivot,
    };

AttendantStudentPivot _$AttendantStudentPivotFromJson(
        Map<String, dynamic> json) =>
    AttendantStudentPivot(
      trip_id: (json['trip_id'] as num?)?.toInt(),
      student_id: json['student_id'] as String?,
      is_absent: (json['is_absent'] as num?)?.toInt(),
      onboard_at: json['onboard_at'] as String?,
      arrived_at: json['arrived_at'] as String?,
    );

Map<String, dynamic> _$AttendantStudentPivotToJson(
        AttendantStudentPivot instance) =>
    <String, dynamic>{
      'trip_id': instance.trip_id,
      'student_id': instance.student_id,
      'is_absent': instance.is_absent,
      'onboard_at': instance.onboard_at,
      'arrived_at': instance.arrived_at,
    };

AttendantStudentsResponse _$AttendantStudentsResponseFromJson(
        Map<String, dynamic> json) =>
    AttendantStudentsResponse(
      message: json['message'] as String?,
      errors: json['errors'] as bool?,
      attendantStudents: (json['attendantStudents'] as List<dynamic>?)
          ?.map(
              (e) => AttendantStudentModel.fromJson(e as Map<String, dynamic>))
          .toList(),
    );

Map<String, dynamic> _$AttendantStudentsResponseToJson(
        AttendantStudentsResponse instance) =>
    <String, dynamic>{
      'message': instance.message,
      'errors': instance.errors,
      'attendantStudents': instance.attendantStudents,
    };
