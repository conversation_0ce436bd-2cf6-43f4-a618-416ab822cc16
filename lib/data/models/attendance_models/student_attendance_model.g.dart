// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'student_attendance_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

StudentAttendanceModel _$StudentAttendanceModelFromJson(
        Map<String, dynamic> json) =>
    StudentAttendanceModel(
      id: (json['id'] as num?)?.toInt(),
      name: json['name'] as String?,
      is_present: json['is_present'] as bool?,
      attendance_time: json['attendance_time'] as String?,
      grade: json['grade'] as String?,
      classroom: json['classroom'] as String?,
      profile_image: json['profile_image'] as String?,
    );

Map<String, dynamic> _$StudentAttendanceModelToJson(
        StudentAttendanceModel instance) =>
    <String, dynamic>{
      'id': instance.id,
      'name': instance.name,
      'is_present': instance.is_present,
      'attendance_time': instance.attendance_time,
      'grade': instance.grade,
      'classroom': instance.classroom,
      'profile_image': instance.profile_image,
    };

TripAttendanceResponse _$TripAttendanceResponseFromJson(
        Map<String, dynamic> json) =>
    TripAttendanceResponse(
      status: json['status'] as bool?,
      message: json['message'] as String?,
      data: (json['data'] as List<dynamic>?)
          ?.map(
              (e) => StudentAttendanceModel.fromJson(e as Map<String, dynamic>))
          .toList(),
    );

Map<String, dynamic> _$TripAttendanceResponseToJson(
        TripAttendanceResponse instance) =>
    <String, dynamic>{
      'status': instance.status,
      'message': instance.message,
      'data': instance.data,
    };
