import 'package:equatable/equatable.dart';
import 'package:json_annotation/json_annotation.dart';

part 'student_attendance_model.g.dart';

@JsonSerializable()
class StudentAttendanceModel extends Equatable {
  final int? id;
  final String? name;
  final bool? is_present;
  final String? attendance_time;
  final String? grade;
  final String? classroom;
  final String? profile_image;

  const StudentAttendanceModel({
    this.id,
    this.name,
    this.is_present,
    this.attendance_time,
    this.grade,
    this.classroom,
    this.profile_image,
  });

  factory StudentAttendanceModel.fromJson(Map<String, dynamic> json) =>
      _$StudentAttendanceModelFromJson(json);

  Map<String, dynamic> toJson() => _$StudentAttendanceModelToJson(this);

  @override
  List<Object?> get props => [
        id,
        name,
        is_present,
        attendance_time,
        grade,
        classroom,
        profile_image,
      ];
}

@JsonSerializable()
class TripAttendanceResponse extends Equatable {
  final bool? status;
  final String? message;
  final List<StudentAttendanceModel>? data;

  const TripAttendanceResponse({
    this.status,
    this.message,
    this.data,
  });

  factory TripAttendanceResponse.fromJson(Map<String, dynamic> json) =>
      _$TripAttendanceResponseFromJson(json);

  Map<String, dynamic> toJson() => _$TripAttendanceResponseToJson(this);

  @override
  List<Object?> get props => [status, message, data];
}
