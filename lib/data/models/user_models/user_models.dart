import 'package:equatable/equatable.dart';
import 'package:json_annotation/json_annotation.dart';

part 'user_models.g.dart';

@JsonSerializable()
class UserModel extends Equatable {
  final int? id;
  final bool? errors;
  bool? success;
  final String? message;
  final String? name;
  final String? email;
  final String? phone;
  final String? emailVerifiedAt;
  final String? address;
  final String? cityName;
  final int? status;
  final String? logo;
  final String? deletedAt;
  final String? createdAt;
  final String? updatedAt;
  final String? typeAuth;
  final String? logoPath;
  final String? latitude;
  final String? longitude;

  UserModel({
    this.email,
    this.logo,
    this.emailVerifiedAt,
    this.cityName,
    this.deletedAt,
    this.id,
    this.name,
    this.success,
    this.createdAt,
    this.updatedAt,
    this.logoPath,
    this.address,
    this.phone,
    this.status,
    this.typeAuth,
    this.latitude,
    this.longitude,
    this.message,
    this.errors,
  });

  factory UserModel.fromJson(Map<String, dynamic> json) {
    return _$UserModelFromJson(json);
  }

  Map<String, dynamic> toJson() => _$UserModelToJson(this);

  @override
  List<Object?> get props => [
        email,
        logo,
        emailVerifiedAt,
        cityName,
        deletedAt,
        id,
        name,
        createdAt,
        updatedAt,
        logoPath,
        address,
        phone,
        status,
        typeAuth,
        latitude,
        longitude,
        message,
        errors,
        success,
      ];
}
