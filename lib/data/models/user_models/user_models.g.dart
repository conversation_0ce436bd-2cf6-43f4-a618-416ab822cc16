// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'user_models.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

UserModel _$UserModelFromJson(Map<String, dynamic> json) => UserModel(
      email: json['email'] as String?,
      logo: json['logo'] as String?,
      emailVerifiedAt: json['emailVerifiedAt'] as String?,
      cityName: json['cityName'] as String?,
      deletedAt: json['deletedAt'] as String?,
      id: (json['id'] as num?)?.toInt(),
      name: json['name'] as String?,
      success: json['success'] as bool?,
      createdAt: json['createdAt'] as String?,
      updatedAt: json['updatedAt'] as String?,
      logoPath: json['logoPath'] as String?,
      address: json['address'] as String?,
      phone: json['phone'] as String?,
      status: (json['status'] as num?)?.toInt(),
      typeAuth: json['typeAuth'] as String?,
      latitude: json['latitude'] as String?,
      longitude: json['longitude'] as String?,
      message: json['message'] as String?,
      errors: json['errors'] as bool?,
    );

Map<String, dynamic> _$UserModelToJson(UserModel instance) => <String, dynamic>{
      'id': instance.id,
      'errors': instance.errors,
      'success': instance.success,
      'message': instance.message,
      'name': instance.name,
      'email': instance.email,
      'phone': instance.phone,
      'emailVerifiedAt': instance.emailVerifiedAt,
      'address': instance.address,
      'cityName': instance.cityName,
      'status': instance.status,
      'logo': instance.logo,
      'deletedAt': instance.deletedAt,
      'createdAt': instance.createdAt,
      'updatedAt': instance.updatedAt,
      'typeAuth': instance.typeAuth,
      'logoPath': instance.logoPath,
      'latitude': instance.latitude,
      'longitude': instance.longitude,
    };
