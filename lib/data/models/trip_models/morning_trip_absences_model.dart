import 'package:equatable/equatable.dart';

/// Model for a student absent from the morning trip
class MorningTripAbsentStudent extends Equatable {
  final String? id;
  final String? name;
  final int? busId;
  final String? logoPath;

  const MorningTripAbsentStudent({
    this.id,
    this.name,
    this.busId,
    this.logoPath,
  });

  factory MorningTripAbsentStudent.fromJson(Map<String, dynamic> json) {
    return MorningTripAbsentStudent(
      id: json['id'],
      name: json['name'],
      busId: json['bus_id'],
      logoPath: json['logo_path'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'bus_id': busId,
      'logo_path': logoPath,
    };
  }

  @override
  List<Object?> get props => [id, name, busId, logoPath];
}

/// Response model for the morning trip absences API
class MorningTripAbsencesResponse extends Equatable {
  final List<MorningTripAbsentStudent>? absences;
  final String? message;
  final bool? errors;

  const MorningTripAbsencesResponse({
    this.absences,
    this.message,
    this.errors,
  });

  factory MorningTripAbsencesResponse.fromJson(Map<String, dynamic> json) {
    return MorningTripAbsencesResponse(
      absences: json['absences'] != null
          ? List<MorningTripAbsentStudent>.from(
              json['absences'].map((x) => MorningTripAbsentStudent.fromJson(x)))
          : null,
      message: json['message'],
      errors: json['errors'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'absences': absences?.map((x) => x.toJson()).toList(),
      'message': message,
      'errors': errors,
    };
  }

  @override
  List<Object?> get props => [absences, message, errors];
}
