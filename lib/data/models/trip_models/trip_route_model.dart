import 'package:equatable/equatable.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';

/// Model for a route point in a trip
class RoutePoint extends Equatable {
  final String? id;
  final String? tripId;
  final String? busId;
  final String? latitude;
  final String? longitude;
  final String? type;
  final String? createdAt;

  const RoutePoint({
    this.id,
    this.tripId,
    this.busId,
    this.latitude,
    this.longitude,
    this.type,
    this.createdAt,
  });

  factory RoutePoint.fromJson(Map<String, dynamic> json) {
    return RoutePoint(
      id: json['id']?.toString(),
      tripId: json['trip_id']?.toString(),
      busId: json['bus_id']?.toString(),
      latitude: json['latitude']?.toString(),
      longitude: json['longitude']?.toString(),
      type: json['type'],
      createdAt: json['created_at'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'trip_id': tripId,
      'bus_id': busId,
      'latitude': latitude,
      'longitude': longitude,
      'type': type,
      'created_at': createdAt,
    };
  }

  /// Convert to LatLng for Google Maps
  LatLng? toLatLng() {
    if (latitude == null || longitude == null) return null;
    
    try {
      final lat = double.parse(latitude!);
      final lng = double.parse(longitude!);
      return LatLng(lat, lng);
    } catch (e) {
      return null;
    }
  }

  @override
  List<Object?> get props => [
        id,
        tripId,
        busId,
        latitude,
        longitude,
        type,
        createdAt,
      ];
}

/// Model for a trip with route information
class TripRoute extends Equatable {
  final String? id;
  final String? schoolId;
  final String? busId;
  final String? tripsDate;
  final String? tripType;
  final String? status;
  final String? latitude;
  final String? longitude;
  final String? attendanceType;
  final String? endAt;
  final String? createdAt;
  final String? updatedAt;
  final List<RoutePoint>? routes;

  const TripRoute({
    this.id,
    this.schoolId,
    this.busId,
    this.tripsDate,
    this.tripType,
    this.status,
    this.latitude,
    this.longitude,
    this.attendanceType,
    this.endAt,
    this.createdAt,
    this.updatedAt,
    this.routes,
  });

  factory TripRoute.fromJson(Map<String, dynamic> json) {
    return TripRoute(
      id: json['id']?.toString(),
      schoolId: json['school_id']?.toString(),
      busId: json['bus_id']?.toString(),
      tripsDate: json['trips_date'],
      tripType: json['trip_type'],
      status: json['status']?.toString(),
      latitude: json['latitude'],
      longitude: json['longitude'],
      attendanceType: json['attendance_type'],
      endAt: json['end_at'],
      createdAt: json['created_at'],
      updatedAt: json['updated_at'],
      routes: json['routes'] != null
          ? List<RoutePoint>.from(
              json['routes'].map((x) => RoutePoint.fromJson(x)))
          : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'school_id': schoolId,
      'bus_id': busId,
      'trips_date': tripsDate,
      'trip_type': tripType,
      'status': status,
      'latitude': latitude,
      'longitude': longitude,
      'attendance_type': attendanceType,
      'end_at': endAt,
      'created_at': createdAt,
      'updated_at': updatedAt,
      'routes': routes?.map((x) => x.toJson()).toList(),
    };
  }

  /// Get all route points as LatLng list for Google Maps
  List<LatLng> getRouteLatLngs() {
    if (routes == null) return [];
    
    final List<LatLng> result = [];
    for (final point in routes!) {
      final latLng = point.toLatLng();
      if (latLng != null) {
        result.add(latLng);
      }
    }
    return result;
  }

  @override
  List<Object?> get props => [
        id,
        schoolId,
        busId,
        tripsDate,
        tripType,
        status,
        latitude,
        longitude,
        attendanceType,
        endAt,
        createdAt,
        updatedAt,
        routes,
      ];
}

/// Response model for the trip route API
class TripRouteResponse extends Equatable {
  final TripRoute? data;
  final String? message;
  final bool? status;

  const TripRouteResponse({
    this.data,
    this.message,
    this.status,
  });

  factory TripRouteResponse.fromJson(Map<String, dynamic> json) {
    return TripRouteResponse(
      data: json['data'] != null ? TripRoute.fromJson(json['data']) : null,
      message: json['message'],
      status: json['status'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'data': data?.toJson(),
      'message': message,
      'status': status,
    };
  }

  @override
  List<Object?> get props => [data, message, status];
}
