import 'package:equatable/equatable.dart';

/// Model for a student waiting for the morning trip
class MorningTripWaitingStudent extends Equatable {
  final String? id;
  final String? name;
  final String? phone;
  final int? gradeId;
  final int? genderId;
  final int? schoolId;
  final int? classroomId;
  final int? busId;
  final String? address;
  final int? status;
  final String? tripType;
  final String? parentKey;
  final String? parentSecret;
  final String? logo;
  final String? latitude;
  final String? longitude;
  final String? createdAt;
  final String? updatedAt;
  final String? logoPath;

  const MorningTripWaitingStudent({
    this.id,
    this.name,
    this.phone,
    this.gradeId,
    this.genderId,
    this.schoolId,
    this.classroomId,
    this.busId,
    this.address,
    this.status,
    this.tripType,
    this.parentKey,
    this.parentSecret,
    this.logo,
    this.latitude,
    this.longitude,
    this.createdAt,
    this.updatedAt,
    this.logoPath,
  });

  factory MorningTripWaitingStudent.fromJson(Map<String, dynamic> json) {
    return MorningTripWaitingStudent(
      id: json['id'],
      name: json['name'],
      phone: json['phone'],
      gradeId: json['grade_id'],
      genderId: json['gender_id'],
      schoolId: json['school_id'],
      classroomId: json['classroom_id'],
      busId: json['bus_id'],
      address: json['address'],
      status: json['status'],
      tripType: json['trip_type'],
      parentKey: json['parent_key'],
      parentSecret: json['parent_secret'],
      logo: json['logo'],
      latitude: json['latitude'],
      longitude: json['longitude'],
      createdAt: json['created_at'],
      updatedAt: json['updated_at'],
      logoPath: json['logo_path'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'phone': phone,
      'grade_id': gradeId,
      'gender_id': genderId,
      'school_id': schoolId,
      'classroom_id': classroomId,
      'bus_id': busId,
      'address': address,
      'status': status,
      'trip_type': tripType,
      'parent_key': parentKey,
      'parent_secret': parentSecret,
      'logo': logo,
      'latitude': latitude,
      'longitude': longitude,
      'created_at': createdAt,
      'updated_at': updatedAt,
      'logo_path': logoPath,
    };
  }

  @override
  List<Object?> get props => [
        id,
        name,
        phone,
        gradeId,
        genderId,
        schoolId,
        classroomId,
        busId,
        address,
        status,
        tripType,
        parentKey,
        parentSecret,
        logo,
        latitude,
        longitude,
        createdAt,
        updatedAt,
        logoPath,
      ];
}

/// Response model for the morning trip waiting students API
class MorningTripWaitingResponse extends Equatable {
  final List<MorningTripWaitingStudent>? waiting;
  final String? message;
  final bool? status;

  const MorningTripWaitingResponse({
    this.waiting,
    this.message,
    this.status,
  });

  factory MorningTripWaitingResponse.fromJson(Map<String, dynamic> json) {
    return MorningTripWaitingResponse(
      waiting: json['waiting'] != null
          ? List<MorningTripWaitingStudent>.from(
              json['waiting'].map((x) => MorningTripWaitingStudent.fromJson(x)))
          : null,
      message: json['message'],
      status: json['status'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'waiting': waiting?.map((x) => x.toJson()).toList(),
      'message': message,
      'status': status,
    };
  }

  @override
  List<Object?> get props => [waiting, message, status];
}
