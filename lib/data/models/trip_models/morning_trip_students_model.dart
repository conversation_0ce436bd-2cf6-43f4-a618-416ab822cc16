import 'package:equatable/equatable.dart';

/// Model for a student present on the bus during a morning trip
class MorningTripStudent extends Equatable {
  final String? id;
  final String? name;
  final String? phone;
  final int? gradeId;
  final int? genderId;
  final int? schoolId;
  final int? classroomId;
  final int? busId;
  final String? address;
  final int? status;
  final String? tripType;
  final String? parentKey;
  final String? parentSecret;
  final String? logo;
  final String? latitude;
  final String? longitude;
  final String? createdAt;
  final String? updatedAt;
  final String? logoPath;

  const MorningTripStudent({
    this.id,
    this.name,
    this.phone,
    this.gradeId,
    this.genderId,
    this.schoolId,
    this.classroomId,
    this.busId,
    this.address,
    this.status,
    this.tripType,
    this.parentKey,
    this.parentSecret,
    this.logo,
    this.latitude,
    this.longitude,
    this.createdAt,
    this.updatedAt,
    this.logoPath,
  });

  factory MorningTripStudent.fromJson(Map<String, dynamic> json) {
    return MorningTripStudent(
      id: json['id'],
      name: json['name'],
      phone: json['phone'],
      gradeId: json['grade_id'],
      genderId: json['gender_id'],
      schoolId: json['school_id'],
      classroomId: json['classroom_id'],
      busId: json['bus_id'],
      address: json['address'],
      status: json['status'],
      tripType: json['trip_type'],
      parentKey: json['parent_key'],
      parentSecret: json['parent_secret'],
      logo: json['logo'],
      latitude: json['latitude'],
      longitude: json['longitude'],
      createdAt: json['created_at'],
      updatedAt: json['updated_at'],
      logoPath: json['logo_path'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'phone': phone,
      'grade_id': gradeId,
      'gender_id': genderId,
      'school_id': schoolId,
      'classroom_id': classroomId,
      'bus_id': busId,
      'address': address,
      'status': status,
      'trip_type': tripType,
      'parent_key': parentKey,
      'parent_secret': parentSecret,
      'logo': logo,
      'latitude': latitude,
      'longitude': longitude,
      'created_at': createdAt,
      'updated_at': updatedAt,
      'logo_path': logoPath,
    };
  }

  @override
  List<Object?> get props => [
        id,
        name,
        phone,
        gradeId,
        genderId,
        schoolId,
        classroomId,
        busId,
        address,
        status,
        tripType,
        parentKey,
        parentSecret,
        logo,
        latitude,
        longitude,
        createdAt,
        updatedAt,
        logoPath,
      ];
}

/// Model for trip data
class TripData extends Equatable {
  final int? id;
  final int? schoolId;
  final int? busId;
  final String? tripsDate;
  final String? tripType;
  final int? status;
  final String? latitude;
  final String? longitude;
  final String? attendanceType;
  final String? endAt;
  final String? createdAt;
  final String? updatedAt;

  const TripData({
    this.id,
    this.schoolId,
    this.busId,
    this.tripsDate,
    this.tripType,
    this.status,
    this.latitude,
    this.longitude,
    this.attendanceType,
    this.endAt,
    this.createdAt,
    this.updatedAt,
  });

  factory TripData.fromJson(Map<String, dynamic> json) {
    return TripData(
      id: json['id'],
      schoolId: json['school_id'],
      busId: json['bus_id'],
      tripsDate: json['trips_date'],
      tripType: json['trip_type'],
      status: json['status'],
      latitude: json['latitude'],
      longitude: json['longitude'],
      attendanceType: json['attendance_type'],
      endAt: json['end_at'],
      createdAt: json['created_at'],
      updatedAt: json['updated_at'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'school_id': schoolId,
      'bus_id': busId,
      'trips_date': tripsDate,
      'trip_type': tripType,
      'status': status,
      'latitude': latitude,
      'longitude': longitude,
      'attendance_type': attendanceType,
      'end_at': endAt,
      'created_at': createdAt,
      'updated_at': updatedAt,
    };
  }

  @override
  List<Object?> get props => [
        id,
        schoolId,
        busId,
        tripsDate,
        tripType,
        status,
        latitude,
        longitude,
        attendanceType,
        endAt,
        createdAt,
        updatedAt,
      ];
}

/// Response model for the morning trip students API
class MorningTripStudentsResponse extends Equatable {
  final List<MorningTripStudent>? presentOnBus;
  final bool? trip;
  final TripData? data;
  final String? message;
  final bool? errors;

  const MorningTripStudentsResponse({
    this.presentOnBus,
    this.trip,
    this.data,
    this.message,
    this.errors,
  });

  factory MorningTripStudentsResponse.fromJson(Map<String, dynamic> json) {
    return MorningTripStudentsResponse(
      presentOnBus: json['present_on_bus'] != null
          ? List<MorningTripStudent>.from(
              json['present_on_bus'].map((x) => MorningTripStudent.fromJson(x)))
          : null,
      trip: json['trip'],
      data: json['data'] != null ? TripData.fromJson(json['data']) : null,
      message: json['message'],
      errors: json['errors'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'present_on_bus': presentOnBus?.map((x) => x.toJson()).toList(),
      'trip': trip,
      'data': data?.toJson(),
      'message': message,
      'errors': errors,
    };
  }

  @override
  List<Object?> get props => [presentOnBus, trip, data, message, errors];
}
