import 'package:equatable/equatable.dart';

/// Model for a student's parent
class StudentParent extends Equatable {
  const StudentParent();

  factory StudentParent.fromJson(Map<String, dynamic> json) {
    return const StudentParent();
  }

  Map<String, dynamic> toJson() {
    return {};
  }

  @override
  List<Object?> get props => [];
}

/// Model for a student in the evening trip
class EveningTripStudent extends Equatable {
  final String? id;
  final String? name;
  final String? phone;
  final int? gradeId;
  final int? genderId;
  final int? schoolId;
  final int? classroomId;
  final int? busId;
  final String? address;
  final int? status;
  final String? tripType;
  final String? parentKey;
  final String? parentSecret;
  final String? logo;
  final String? latitude;
  final String? longitude;
  final String? createdAt;
  final String? updatedAt;
  final String? logoPath;
  final List<StudentParent>? myParents;

  const EveningTripStudent({
    this.id,
    this.name,
    this.phone,
    this.gradeId,
    this.genderId,
    this.schoolId,
    this.classroomId,
    this.busId,
    this.address,
    this.status,
    this.tripType,
    this.parentKey,
    this.parentSecret,
    this.logo,
    this.latitude,
    this.longitude,
    this.createdAt,
    this.updatedAt,
    this.logoPath,
    this.myParents,
  });

  factory EveningTripStudent.fromJson(Map<String, dynamic> json) {
    return EveningTripStudent(
      id: json['id'],
      name: json['name'],
      phone: json['phone'],
      gradeId: json['grade_id'],
      genderId: json['gender_id'],
      schoolId: json['school_id'],
      classroomId: json['classroom_id'],
      busId: json['bus_id'],
      address: json['address'],
      status: json['status'],
      tripType: json['trip_type'],
      parentKey: json['parent_key'],
      parentSecret: json['parent_secret'],
      logo: json['logo'],
      latitude: json['latitude'],
      longitude: json['longitude'],
      createdAt: json['created_at'],
      updatedAt: json['updated_at'],
      logoPath: json['logo_path'],
      myParents: json['my__parents'] != null
          ? List<StudentParent>.from(
              json['my__parents'].map((x) => StudentParent.fromJson(x)))
          : [],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'phone': phone,
      'grade_id': gradeId,
      'gender_id': genderId,
      'school_id': schoolId,
      'classroom_id': classroomId,
      'bus_id': busId,
      'address': address,
      'status': status,
      'trip_type': tripType,
      'parent_key': parentKey,
      'parent_secret': parentSecret,
      'logo': logo,
      'latitude': latitude,
      'longitude': longitude,
      'created_at': createdAt,
      'updated_at': updatedAt,
      'logo_path': logoPath,
      'my__parents': myParents?.map((x) => x.toJson()).toList(),
    };
  }

  @override
  List<Object?> get props => [
        id,
        name,
        phone,
        gradeId,
        genderId,
        schoolId,
        classroomId,
        busId,
        address,
        status,
        tripType,
        parentKey,
        parentSecret,
        logo,
        latitude,
        longitude,
        createdAt,
        updatedAt,
        logoPath,
        myParents,
      ];
}

/// Model for a student present on the bus during an evening trip
class EveningTripPresentStudent extends Equatable {
  final String? attendanceType;
  final String? attendenceDate;
  final int? tripId;
  final String? studentId;
  final EveningTripStudent? students;

  const EveningTripPresentStudent({
    this.attendanceType,
    this.attendenceDate,
    this.tripId,
    this.studentId,
    this.students,
  });

  factory EveningTripPresentStudent.fromJson(Map<String, dynamic> json) {
    return EveningTripPresentStudent(
      attendanceType: json['attendance_type'],
      attendenceDate: json['attendence_date'],
      tripId: json['trip_id'],
      studentId: json['student_id'],
      students: json['students'] != null
          ? EveningTripStudent.fromJson(json['students'])
          : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'attendance_type': attendanceType,
      'attendence_date': attendenceDate,
      'trip_id': tripId,
      'student_id': studentId,
      'students': students?.toJson(),
    };
  }

  @override
  List<Object?> get props => [
        attendanceType,
        attendenceDate,
        tripId,
        studentId,
        students,
      ];
}

/// Response model for the evening trip students on bus API
class EveningTripStudentsResponse extends Equatable {
  final List<EveningTripPresentStudent>? presentOnBus;
  final String? message;
  final bool? trip;
  final bool? errors;

  const EveningTripStudentsResponse({
    this.presentOnBus,
    this.message,
    this.trip,
    this.errors,
  });

  factory EveningTripStudentsResponse.fromJson(Map<String, dynamic> json) {
    return EveningTripStudentsResponse(
      presentOnBus: json['present_on_bus'] != null
          ? List<EveningTripPresentStudent>.from(
              json['present_on_bus']
                  .map((x) => EveningTripPresentStudent.fromJson(x)))
          : null,
      message: json['message'],
      trip: json['trip'],
      errors: json['errors'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'present_on_bus': presentOnBus?.map((x) => x.toJson()).toList(),
      'message': message,
      'trip': trip,
      'errors': errors,
    };
  }

  @override
  List<Object?> get props => [presentOnBus, message, trip, errors];
}
