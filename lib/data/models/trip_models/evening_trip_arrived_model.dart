import 'package:equatable/equatable.dart';

/// Model for a student who arrived home during an evening trip
class EveningTripArrivedStudent extends Equatable {
  final String? id;
  final String? name;
  final String? phone;
  final int? gradeId;
  final int? genderId;
  final int? schoolId;
  final int? classroomId;
  final int? busId;
  final String? address;
  final int? status;
  final String? tripType;
  final String? parentKey;
  final String? parentSecret;
  final String? logo;
  final String? latitude;
  final String? longitude;
  final String? createdAt;
  final String? updatedAt;
  final String? logoPath;

  const EveningTripArrivedStudent({
    this.id,
    this.name,
    this.phone,
    this.gradeId,
    this.genderId,
    this.schoolId,
    this.classroomId,
    this.busId,
    this.address,
    this.status,
    this.tripType,
    this.parentKey,
    this.parentSecret,
    this.logo,
    this.latitude,
    this.longitude,
    this.createdAt,
    this.updatedAt,
    this.logoPath,
  });

  factory EveningTripArrivedStudent.fromJson(Map<String, dynamic> json) {
    return EveningTripArrivedStudent(
      id: json['id'],
      name: json['name'],
      phone: json['phone'],
      gradeId: json['grade_id'],
      genderId: json['gender_id'],
      schoolId: json['school_id'],
      classroomId: json['classroom_id'],
      busId: json['bus_id'],
      address: json['address'],
      status: json['status'],
      tripType: json['trip_type'],
      parentKey: json['parent_key'],
      parentSecret: json['parent_secret'],
      logo: json['logo'],
      latitude: json['latitude'],
      longitude: json['longitude'],
      createdAt: json['created_at'],
      updatedAt: json['updated_at'],
      logoPath: json['logo_path'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'phone': phone,
      'grade_id': gradeId,
      'gender_id': genderId,
      'school_id': schoolId,
      'classroom_id': classroomId,
      'bus_id': busId,
      'address': address,
      'status': status,
      'trip_type': tripType,
      'parent_key': parentKey,
      'parent_secret': parentSecret,
      'logo': logo,
      'latitude': latitude,
      'longitude': longitude,
      'created_at': createdAt,
      'updated_at': updatedAt,
      'logo_path': logoPath,
    };
  }

  @override
  List<Object?> get props => [
        id,
        name,
        phone,
        gradeId,
        genderId,
        schoolId,
        classroomId,
        busId,
        address,
        status,
        tripType,
        parentKey,
        parentSecret,
        logo,
        latitude,
        longitude,
        createdAt,
        updatedAt,
        logoPath,
      ];
}

/// Response model for the evening trip arrived students API
class EveningTripArrivedResponse extends Equatable {
  final List<EveningTripArrivedStudent>? arrived;
  final String? message;
  final bool? errors;

  const EveningTripArrivedResponse({
    this.arrived,
    this.message,
    this.errors,
  });

  factory EveningTripArrivedResponse.fromJson(Map<String, dynamic> json) {
    return EveningTripArrivedResponse(
      arrived: json['arrived'] != null
          ? List<EveningTripArrivedStudent>.from(
              json['arrived'].map((x) => EveningTripArrivedStudent.fromJson(x)))
          : null,
      message: json['message'],
      errors: json['errors'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'arrived': arrived?.map((x) => x.toJson()).toList(),
      'message': message,
      'errors': errors,
    };
  }

  @override
  List<Object?> get props => [arrived, message, errors];
}
