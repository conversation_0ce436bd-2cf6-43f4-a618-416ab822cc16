import 'package:bus/data/models/add_bus_models/add_bus_data_models.dart';
import 'package:equatable/equatable.dart';
import 'package:json_annotation/json_annotation.dart';

part 'update_bus_data_models.g.dart';

@JsonSerializable()
class UpdateBusDataModels extends Equatable {
  final AddBusDataModels? bus;

  const UpdateBusDataModels({
    this.bus,
  });

  factory UpdateBusDataModels.fromJson(Map<String, dynamic> json) {
    return _$UpdateBusDataModelsFromJson(json);
  }

  Map<String, dynamic> toJson() => _$UpdateBusDataModelsToJson(this);


  @override
  List<Object?> get props => [
        bus,
      ];
}
