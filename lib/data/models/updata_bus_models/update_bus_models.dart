import 'package:bus/data/models/updata_bus_models/update_bus_data_models.dart';
import 'package:equatable/equatable.dart';
import 'package:json_annotation/json_annotation.dart';

part 'update_bus_models.g.dart';

@JsonSerializable()
class UpdateBusModels extends Equatable {
  final bool? status;
  final String? message;
  final UpdateBusDataModels? data;

  const UpdateBusModels({
    this.message,
    this.status,
    this.data,
  });

  factory UpdateBusModels.fromJson(Map<String, dynamic> json) {
    return _$UpdateBusModelsFromJson(json);
  }

  Map<String, dynamic> toJson() => _$UpdateBusModelsToJson(this);


  @override
  List<Object?> get props => [
        message,
        status,
        data,
      ];
}
