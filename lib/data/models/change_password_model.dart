import 'dart:convert';

class ChangePasswordModel {
  Status? status;
  dynamic data;
  bool? errors;
  String? messages;

  ChangePasswordModel({
    this.status,
    this.data,
    this.errors,
    this.messages,
  });

  factory ChangePasswordModel.fromJson(dynamic str) {
    if (str is String) {
      return ChangePasswordModel.fromMap(json.decode(str));
    } else if (str is Map<String, dynamic>) {
      return ChangePasswordModel.fromMap(str);
    }
    throw const FormatException('Invalid input format for ChangePasswordModel');
  }

  String toJson() => json.encode(toMap());

  factory ChangePasswordModel.fromMap(Map<String, dynamic> json) =>
      ChangePasswordModel(
        status: json["status"] == null ? null : Status.fromMap(json["status"]),
        data: json["data"],
        errors: json["errors"],
        messages: json["messages"],
      );

  Map<String, dynamic> toMap() => {
        "status": status?.toMap(),
        "data": data,
        "errors": errors,
        "messages": messages,
      };
}

class Status {
  int? status;
  String? messages;

  Status({
    this.status,
    this.messages,
  });

  factory Status.fromJson(dynamic str) {
    if (str is String) {
      return Status.fromMap(json.decode(str));
    } else if (str is Map<String, dynamic>) {
      return Status.fromMap(str);
    }
    throw FormatException('Invalid input format for Status');
  }

  String toJson() => json.encode(toMap());

  factory Status.fromMap(Map<String, dynamic> json) => Status(
        status: json["status"],
        messages: json["messages"],
      );

  Map<String, dynamic> toMap() => {
        "status": status,
        "messages": messages,
      };
}
