import 'package:bus/data/models/student_models/student_grade_models.dart';
import 'package:equatable/equatable.dart';
import 'package:json_annotation/json_annotation.dart';

part 'type_blood_models.g.dart';

@JsonSerializable()
class TypeBloodModels extends Equatable {
  final bool? status;
  final String? message;
  final List<StudentGradeModels>? data;

  const TypeBloodModels({
    this.data,
    this.status,
    this.message,
  });

  factory TypeBloodModels.fromJson(Map<String, dynamic> json) {
    return _$TypeBloodModelsFromJson(json);
  }

  Map<String, dynamic> toJson() => _$TypeBloodModelsToJson(this);

  @override
  List<Object?> get props => [
        data,
        status,
        message,
      ];
}
