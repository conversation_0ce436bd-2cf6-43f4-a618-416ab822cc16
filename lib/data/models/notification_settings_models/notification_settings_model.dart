class NotificationSettingsModel {
  final bool? status;
  final String? message;
  final NotificationSettingsData? data;

  NotificationSettingsModel({
    this.status,
    this.message,
    this.data,
  });

  factory NotificationSettingsModel.fromJson(Map<String, dynamic> json) {
    return NotificationSettingsModel(
      status: json['status']?['status'] == 1,
      message: json['status']?['messages'],
      data: json['data'] != null 
          ? NotificationSettingsData.fromJson(json['data']) 
          : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'status': {
        'status': status == true ? 1 : 0,
        'messages': message,
      },
      'data': data?.toJson(),
    };
  }
}

class NotificationSettingsData {
  final int? id;
  final String? name;
  final String? email;
  final String? phone;
  final String? emailVerifiedAt;
  final String? address;
  final String? cityName;
  final int? status;
  final String? logo;
  final String? createdAt;
  final String? updatedAt;
  final String? typeAuth;
  final String? latitude;
  final String? longitude;
  final String? tripStartEndNotificationStatus;
  final String? studentAbsenceNotificationStatus;
  final String? studentAddressNotificationStatus;
  final String? logoPath;
  final bool? subscriptionStatus;
  final bool? identityPreview;

  NotificationSettingsData({
    this.id,
    this.name,
    this.email,
    this.phone,
    this.emailVerifiedAt,
    this.address,
    this.cityName,
    this.status,
    this.logo,
    this.createdAt,
    this.updatedAt,
    this.typeAuth,
    this.latitude,
    this.longitude,
    this.tripStartEndNotificationStatus,
    this.studentAbsenceNotificationStatus,
    this.studentAddressNotificationStatus,
    this.logoPath,
    this.subscriptionStatus,
    this.identityPreview,
  });

  factory NotificationSettingsData.fromJson(Map<String, dynamic> json) {
    return NotificationSettingsData(
      id: json['id'],
      name: json['name'],
      email: json['email'],
      phone: json['phone'],
      emailVerifiedAt: json['email_verified_at'],
      address: json['address'],
      cityName: json['city_name'],
      status: json['status'],
      logo: json['logo'],
      createdAt: json['created_at'],
      updatedAt: json['updated_at'],
      typeAuth: json['typeAuth'],
      latitude: json['latitude'],
      longitude: json['longitude'],
      tripStartEndNotificationStatus: json['trip_start_end_notification_status'],
      studentAbsenceNotificationStatus: json['student_absence_notification_status'],
      studentAddressNotificationStatus: json['student_address_notification_status'],
      logoPath: json['logo_path'],
      subscriptionStatus: json['subscription_status'],
      identityPreview: json['identity_preview'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'email': email,
      'phone': phone,
      'email_verified_at': emailVerifiedAt,
      'address': address,
      'city_name': cityName,
      'status': status,
      'logo': logo,
      'created_at': createdAt,
      'updated_at': updatedAt,
      'typeAuth': typeAuth,
      'latitude': latitude,
      'longitude': longitude,
      'trip_start_end_notification_status': tripStartEndNotificationStatus,
      'student_absence_notification_status': studentAbsenceNotificationStatus,
      'student_address_notification_status': studentAddressNotificationStatus,
      'logo_path': logoPath,
      'subscription_status': subscriptionStatus,
      'identity_preview': identityPreview,
    };
  }
}
