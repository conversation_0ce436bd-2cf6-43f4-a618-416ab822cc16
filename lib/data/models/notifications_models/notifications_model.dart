// import 'dart:convert';

// import 'package:flutter/cupertino.dart';

// class AllNotificationsModel {
//   final Data? data;
//   final String? message;
//   final bool? status;

//   AllNotificationsModel({
//     this.data,
//     this.message,
//     this.status,
//   });

//   AllNotificationsModel copyWith({
//     Data? data,
//     String? message,
//     bool? status,
//   }) =>
//       AllNotificationsModel(
//         data: data ?? this.data,
//         message: message ?? this.message,
//         status: status ?? this.status,
//       );

//   factory AllNotificationsModel.fromRawJson(String str) =>
//       AllNotificationsModel.fromJson(json.decode(str));

//   String toRawJson() => json.encode(toJson());

//   factory AllNotificationsModel.fromJson(Map<String, dynamic> json) =>
//       AllNotificationsModel(
//         data: json["data"] == null ? null : Data.fromJson(json["data"]),
//         message: json["message"],
//         status: json["status"],
//       );

//   Map<String, dynamic> toJson() => {
//         "data": data?.toJson(),
//         "message": message,
//         "status": status,
//       };
// }

// class Data {
//   final UserMessages? userMessages;
//   final int? userMessagesCount;
//   final int? userMessagesNewCount;

//   Data({
//     this.userMessages,
//     this.userMessagesCount,
//     this.userMessagesNewCount,
//   });

//   Data copyWith({
//     UserMessages? userMessages,
//     int? userMessagesCount,
//     int? userMessagesNewCount,
//   }) =>
//       Data(
//         userMessages: userMessages ?? this.userMessages,
//         userMessagesCount: userMessagesCount ?? this.userMessagesCount,
//         userMessagesNewCount: userMessagesNewCount ?? this.userMessagesNewCount,
//       );

//   factory Data.fromRawJson(String str) => Data.fromJson(json.decode(str));

//   String toRawJson() => json.encode(toJson());

//   factory Data.fromJson(Map<String, dynamic> json) => Data(
//         userMessages: json["userMessages"] == null
//             ? null
//             : UserMessages.fromJson(json["userMessages"]),
//         userMessagesCount: json["userMessages_count"],
//         userMessagesNewCount: json["userMessages_new_count"],
//       );

//   Map<String, dynamic> toJson() => {
//         "userMessages": userMessages?.toJson(),
//         "userMessages_count": userMessagesCount,
//         "userMessages_new_count": userMessagesNewCount,
//       };
// }

// class UserMessages {
//   final int? currentPage;
//   final List<NotificationAllData>? data;
//   final String? firstPageUrl;
//   final int? from;
//   final int? lastPage;
//   final String? lastPageUrl;
//   final List<Link>? links;
//   final String? nextPageUrl;
//   final String? path;
//   final int? perPage;
//   final dynamic prevPageUrl;
//   final int? to;
//   final int? total;

//   UserMessages({
//     this.currentPage,
//     this.data,
//     this.firstPageUrl,
//     this.from,
//     this.lastPage,
//     this.lastPageUrl,
//     this.links,
//     this.nextPageUrl,
//     this.path,
//     this.perPage,
//     this.prevPageUrl,
//     this.to,
//     this.total,
//   });

//   UserMessages copyWith({
//     int? currentPage,
//     List<NotificationAllData>? data,
//     String? firstPageUrl,
//     int? from,
//     int? lastPage,
//     String? lastPageUrl,
//     List<Link>? links,
//     String? nextPageUrl,
//     String? path,
//     int? perPage,
//     dynamic prevPageUrl,
//     int? to,
//     int? total,
//   }) =>
//       UserMessages(
//         currentPage: currentPage ?? this.currentPage,
//         data: data ?? this.data,
//         firstPageUrl: firstPageUrl ?? this.firstPageUrl,
//         from: from ?? this.from,
//         lastPage: lastPage ?? this.lastPage,
//         lastPageUrl: lastPageUrl ?? this.lastPageUrl,
//         links: links ?? this.links,
//         nextPageUrl: nextPageUrl ?? this.nextPageUrl,
//         path: path ?? this.path,
//         perPage: perPage ?? this.perPage,
//         prevPageUrl: prevPageUrl ?? this.prevPageUrl,
//         to: to ?? this.to,
//         total: total ?? this.total,
//       );

//   factory UserMessages.fromRawJson(String str) =>
//       UserMessages.fromJson(json.decode(str));

//   String toRawJson() => json.encode(toJson());

//   factory UserMessages.fromJson(Map<String, dynamic> json) => UserMessages(
//         currentPage: json["current_page"],
//         data: json["data"] == null
//             ? []
//             : List<NotificationAllData>.from(
//                 json["data"]!.map((x) => NotificationAllData.fromJson(x))),
//         firstPageUrl: json["first_page_url"],
//         from: json["from"],
//         lastPage: json["last_page"],
//         lastPageUrl: json["last_page_url"],
//         links: json["links"] == null
//             ? []
//             : List<Link>.from(json["links"]!.map((x) => Link.fromJson(x))),
//         nextPageUrl: json["next_page_url"],
//         path: json["path"],
//         perPage: json["per_page"],
//         prevPageUrl: json["prev_page_url"],
//         to: json["to"],
//         total: json["total"],
//       );

//   Map<String, dynamic> toJson() => {
//         "current_page": currentPage,
//         "data": data == null
//             ? []
//             : List<dynamic>.from(data!.map((x) => x.toJson())),
//         "first_page_url": firstPageUrl,
//         "from": from,
//         "last_page": lastPage,
//         "last_page_url": lastPageUrl,
//         "links": links == null
//             ? []
//             : List<dynamic>.from(links!.map((x) => x.toJson())),
//         "next_page_url": nextPageUrl,
//         "path": path,
//         "per_page": perPage,
//         "prev_page_url": prevPageUrl,
//         "to": to,
//         "total": total,
//       };
// }

// class NotificationAllData {
//   final int? id;
//   final int? attendantId;
//   final int? studentId;
//   final DateTime? createdAt;
//   final DateTime? updatedAt;
//   final int? status;
//   final String? message;
//   final String? message_en;
//   final String? title;
//   final TripType? tripType;
//   final int? tripId;
//   final int? busId;
//   final String? notifications_type;
//   final NotificationBusModel? bus;
//   final StudentNotification? student;

//   NotificationAllData({
//     this.id,
//     this.attendantId,
//     this.studentId,
//     this.createdAt,
//     this.updatedAt,
//     this.status,
//     this.message,
//     this.title,
//     this.tripType,
//     this.tripId,
//     this.busId,
//     this.bus,
//     this.message_en,
//     this.notifications_type,
//     this.student,
//   });

//   NotificationAllData copyWith({
//     int? id,
//     int? attendantId,
//     int? studentId,
//     DateTime? createdAt,
//     DateTime? updatedAt,
//     int? status,
//     String? message,
//     String? title,
//     TripType? tripType,
//     int? tripId,
//     int? busId,
//     String? message_en,
//     NotificationBusModel? bus,
//     String? notifications_type,
//     SchoolsCurrent? schools,
//     StudentNotification? student,
//   }) =>
//       NotificationAllData(
//         id: id ?? this.id,
//         attendantId: attendantId ?? this.attendantId,
//         studentId: studentId ?? this.studentId,
//         createdAt: createdAt ?? this.createdAt,
//         updatedAt: updatedAt ?? this.updatedAt,
//         busId: busId ?? this.busId,
//         status: status ?? this.status,
//         message: message ?? this.message,
//         title: title ?? this.title,
//         tripType: tripType ?? this.tripType,
//         tripId: tripId ?? this.tripId,
//         bus: bus ?? this.bus,
//         message_en: message_en ?? this.message_en,
//         notifications_type: notifications_type ?? this.notifications_type,
//         student: student ?? this.student,
//       );

//   factory NotificationAllData.fromRawJson(String str) =>
//       NotificationAllData.fromJson(json.decode(str));

//   String toRawJson() => json.encode(toJson());

//   factory NotificationAllData.fromJson(Map<String, dynamic> json) =>
//       NotificationAllData(
//         id: json["id"],
//         attendantId: json["attendant_id"],
//         studentId: json["student_id"],
//         createdAt: json["created_at"] == null
//             ? null
//             : DateTime.parse(json["created_at"]),
//         updatedAt: json["updated_at"] == null
//             ? null
//             : DateTime.parse(json["updated_at"]),
//         status: json["status"],
//         message: json["message"],
//         message_en: json['message_en'],
//         notifications_type: json["notifications_type"],
//         title: json["title"],
//         tripType: tripTypeValues.map[json["trip_type"]]!,
//         tripId: json["trip_id"],
//         busId: json['bus_id'],
//         bus: json["bus"] == null
//             ? null
//             : NotificationBusModel.fromJson(json["bus"]),
//         student: json["student"] == null
//             ? null
//             : StudentNotification.fromJson(json["student"]),
//       );

//   Map<String, dynamic> toJson() => {
//         "id": id,
//         "attendant_id": attendantId,
//         "student_id": studentId,
//         "created_at": createdAt?.toIso8601String(),
//         "updated_at": updatedAt?.toIso8601String(),
//         "status": status,
//         "message": message,
//         "title": title,
//         "message_en": message_en,
//         "trip_type": tripTypeValues.reverse[tripType],
//         "trip_id": tripId,
//         "bus_id": busId,
//         "bus": bus?.toJson(),
//         "student": student?.toJson(),
//         "notifications_type": notifications_type,
//       };
// }

// enum TripType { eveningTrip, morningTrip }

// final tripTypeValues = EnumValues(
//     {"end_day": TripType.eveningTrip, "start_day": TripType.morningTrip});

// class Link {
//   final String? url;
//   final String? label;
//   final bool? active;

//   Link({
//     this.url,
//     this.label,
//     this.active,
//   });

//   Link copyWith({
//     String? url,
//     String? label,
//     bool? active,
//   }) =>
//       Link(
//         url: url ?? this.url,
//         label: label ?? this.label,
//         active: active ?? this.active,
//       );

//   factory Link.fromRawJson(String str) => Link.fromJson(json.decode(str));

//   String toRawJson() => json.encode(toJson());

//   factory Link.fromJson(Map<String, dynamic> json) => Link(
//         url: json["url"],
//         label: json["label"],
//         active: json["active"],
//       );

//   Map<String, dynamic> toJson() => {
//         "url": url,
//         "label": label,
//         "active": active,
//       };
// }

// class EnumValues<T> {
//   Map<String, T> map;
//   late Map<T, String> reverseMap;

//   EnumValues(this.map);

//   Map<T, String> get reverse {
//     reverseMap = map.map((k, v) => MapEntry(v, k));
//     return reverseMap;
//   }
// }

// class NotificationBusModel {
//   final int? id;
//   final String? name;
//   final String? notes;
//   final int? status;
//   final int? school_id;
//   final String? car_number;
//   final String? created_at;
//   final String? updated_at;
//   final NotificationDataBusModels? admin;
//   final List<CurrentTripTripsModels>? trip;
//   final SchoolsCurrent? schools;

//   NotificationBusModel({
//     this.status,
//     this.name,
//     this.updated_at,
//     this.created_at,
//     this.id,
//     this.school_id,
//     this.car_number,
//     this.admin,
//     this.notes,
//     this.trip,
//     this.schools,
//   });

//   NotificationBusModel copyWith({
//     int? id,
//     String? name,
//     String? notes,
//     int? status,
//     int? school_id,
//     String? car_number,
//     String? created_at,
//     String? updated_at,
//     NotificationDataBusModels? admin,
//     List<CurrentTripTripsModels>? trip,
//     SchoolsCurrent? schools,
//   }) =>
//       NotificationBusModel(
//         id: id ?? this.id,
//         name: name ?? this.name,
//         notes: notes ?? this.notes,
//         status: status ?? this.status,
//         school_id: school_id ?? this.school_id,
//         car_number: car_number ?? this.car_number,
//         created_at: created_at ?? this.created_at,
//         updated_at: updated_at ?? this.updated_at,
//         admin: admin ?? this.admin,
//         trip: trip ?? this.trip,
//         schools: schools ?? this.schools,
//       );

//   factory NotificationBusModel.fromRawJson(String str) =>
//       NotificationBusModel.fromJson(json.decode(str));

//   String toRawJson() => json.encode(toJson());

//   factory NotificationBusModel.fromJson(Map<String, dynamic> json) =>
//       NotificationBusModel(
//         id: json["id"],
//         name: json["name"],
//         notes: json["notes"],
//         status: json["status"],
//         school_id: json["school_id"],
//         car_number: json["car_number"],
//         created_at: json["created_at"],
//         updated_at: json["updated_at"],
//         admin: json["admin"] == null
//             ? null
//             : NotificationDataBusModels.fromJson(json["admin"]),
//         schools: json["schools"] == null
//             ? null
//             : SchoolsCurrent.fromJson(json["schools"]),
//         trip: json["trip"] == null
//             ? []
//             : List<CurrentTripTripsModels>.from(
//                 json["trip"]!.map((x) => CurrentTripTripsModels.fromJson(x))),
//       );

//   Map<String, dynamic> toJson() => {
//         "id": id,
//         "name": name,
//         "notes": notes,
//         "status": status,
//         "school_id": school_id,
//         "car_number": car_number,
//         "created_at": created_at,
//         "updated_at": updated_at,
//         "admin": admin?.toJson(),
//         "schools" : schools?.toJson(),
//         "trip": trip == null
//             ? []
//             : List<dynamic>.from(trip!.map((x) => x.toJson())),
//       };
// }

// class NotificationDataBusModels {
//   final int? id;
//   final String? name;
//   final String? username;
//   final int? gender_id;
//   final int? school_id;
//   final int? religion_id;
//   final int? type__blood_id;
//   final int? bus_id;
//   final String? Joining_Date;
//   final String? address;
//   final String? city_name;
//   final int? status;
//   final String? type;
//   final String? phone;
//   final String? email_verified_at;
//   final String? created_at;
//   final String? updated_at;
//   final String? typeAuth;
//   final String? firebase_token;
//   final String? logo_path;

//   NotificationDataBusModels({
//     this.status,
//     this.name,
//     this.updated_at,
//     this.created_at,
//     this.id,
//     this.school_id,
//     this.address,
//     this.username,
//     this.city_name,
//     this.phone,
//     this.type,
//     this.gender_id,
//     this.bus_id,
//     this.logo_path,
//     this.typeAuth,
//     this.firebase_token,
//     this.email_verified_at,
//     this.Joining_Date,
//     this.religion_id,
//     this.type__blood_id,
//   });

//   NotificationDataBusModels copyWith({
//     status,
//     name,
//     updated_at,
//     created_at,
//     id,
//     school_id,
//     address,
//     username,
//     city_name,
//     phone,
//     type,
//     gender_id,
//     bus_id,
//     logo_path,
//     typeAuth,
//     firebase_token,
//     email_verified_at,
//     Joining_Date,
//     religion_id,
//     type__blood_id,
//   }) =>
//       NotificationDataBusModels(
//         id: id ?? this.id,
//         name: name ?? this.name,
//         status: status ?? this.status,
//         updated_at: updated_at ?? this.updated_at,
//         school_id: school_id ?? this.school_id,
//         typeAuth: typeAuth ?? this.typeAuth,
//         created_at: created_at ?? this.created_at,
//         type__blood_id: type__blood_id ?? this.type__blood_id,
//         address: address ?? this.address,
//         username: username ?? this.username,
//         city_name: city_name ?? this.city_name,
//         phone: phone ?? this.phone,
//         type: type ?? this.type,
//         gender_id: gender_id ?? this.gender_id,
//         bus_id: bus_id ?? this.bus_id,
//         logo_path: logo_path ?? this.logo_path,
//         firebase_token: firebase_token ?? this.firebase_token,
//         email_verified_at: email_verified_at ?? this.email_verified_at,
//         Joining_Date: Joining_Date ?? this.Joining_Date,
//         religion_id: religion_id ?? this.religion_id,
//       );

//   factory NotificationDataBusModels.fromRawJson(String str) =>
//       NotificationDataBusModels.fromJson(json.decode(str));

//   String toRawJson() => json.encode(toJson());

//   factory NotificationDataBusModels.fromJson(Map<String, dynamic> json) =>
//       NotificationDataBusModels(
//         id: json["id"],
//         name: json["name"],
//         status: json["status"],
//         school_id: json["school_id"],
//         created_at: json["created_at"],
//         updated_at: json["updated_at"],
//         username: json['username'],
//         address: json['address'],
//         city_name: json['city_name'],
//         phone: json['phone'],
//         type: json['type'],
//         gender_id: json['gender_id'],
//         bus_id: json['bus_id'],
//         logo_path: json['logo_path'],
//         typeAuth: json['typeAuth'],
//         firebase_token: json['firebase_token'],
//         email_verified_at: json['email_verified_at'],
//         Joining_Date: json['Joining_Date'],
//         religion_id: json['religion_id'],
//         type__blood_id: json['type__blood_id'],
//       );

//   Map<String, dynamic> toJson() => {
//         "id": id,
//         "name": name,
//         "status": status,
//         "school_id": school_id,
//         "created_at": created_at,
//         "updated_at": updated_at,
//         "username": username,
//         "address": address,
//         "city_name": city_name,
//         "phone": phone,
//         "type": type,
//         "gender_id": gender_id,
//         "bus_id": bus_id,
//         "logo_path": logo_path,
//         "typeAuth": typeAuth,
//         "firebase_token": firebase_token,
//         "email_verified_at": email_verified_at,
//         "Joining_Date": Joining_Date,
//         "religion_id": religion_id,
//         "type__blood_id": type__blood_id,
//       };
// }

// class CurrentTripTripsModels {
//   int? id;
//   int? school_id;
//   int? bus_id;
//   String? trips_date;
//   String? trip_type;
//   int? status;
//   dynamic latitude;
//   dynamic longitude;
//   String? created_at;
//   String? updated_at;
//   String? attendance_type;

//   CurrentTripTripsModels({
//     this.trip_type,
//     this.status,
//     this.updated_at,
//     this.created_at,
//     this.longitude,
//     this.latitude,
//     this.bus_id,
//     this.school_id,
//     this.id,
//     this.attendance_type,
//     this.trips_date,
//   });

//   CurrentTripTripsModels copyWith({
//     int? id,
//     int? school_id,
//     int? bus_id,
//     String? trips_date,
//     String? trip_type,
//     int? status,
//     String? latitude,
//     String? longitude,
//     String? created_at,
//     String? updated_at,
//     String? attendance_type,
//   }) =>
//       CurrentTripTripsModels(
//         id: id ?? this.id,
//         school_id: school_id ?? this.school_id,
//         bus_id: bus_id ?? this.bus_id,
//         trips_date: trips_date ?? this.trips_date,
//         trip_type: trip_type ?? this.trip_type,
//         status: status ?? this.status,
//         latitude: latitude ?? this.latitude,
//         longitude: longitude ?? this.longitude,
//         created_at: created_at ?? this.created_at,
//         updated_at: updated_at ?? this.updated_at,
//         attendance_type: attendance_type ?? this.attendance_type,
//       );

//   factory CurrentTripTripsModels.fromRawJson(String str) =>
//       CurrentTripTripsModels.fromJson(json.decode(str));

//   String toRawJson() => json.encode(toJson());

//   factory CurrentTripTripsModels.fromJson(Map<String, dynamic> json) =>
//       CurrentTripTripsModels(
//         id: json["id"],
//         school_id: json["school_id"],
//         bus_id: json["bus_id"],
//         trips_date: json["trips_date"],
//         trip_type: json["trip_type"],
//         status: json["status"],
//         latitude: json["latitude"],
//         longitude: json["longitude"],
//         created_at: json["created_at"],
//         updated_at: json["updated_at"],
//         attendance_type: json["attendance_type"],
//       );

//   Map<String, dynamic> toJson() => {
//         "id": id,
//         "school_id": school_id,
//         "bus_id": bus_id,
//         "trips_date": trips_date,
//         "trip_type": trip_type,
//         "status": status,
//         "latitude": latitude,
//         "longitude": longitude,
//         "created_at": created_at,
//         "updated_at": updated_at,
//         "attendance_type": attendance_type,
//       };
// }

// class SchoolsCurrent {
//   int? id;
//   String? name;
//   String? email;
//   String? phone;
//   String? emailVerifiedAt;
//   String? address;
//   String? cityName;
//   int? status;
//   String? logo;
//   String? createdAt;
//   String? updatedAt;
//   String? typeAuth;
//   String? latitude;
//   String? longitude;
//   String? logoPath;

//   SchoolsCurrent(
//       {this.id,
//       this.name,
//       this.email,
//       this.phone,
//       this.emailVerifiedAt,
//       this.address,
//       this.cityName,
//       this.status,
//       this.logo,
//       this.createdAt,
//       this.updatedAt,
//       this.typeAuth,
//       this.latitude,
//       this.longitude,
//       this.logoPath});

//   SchoolsCurrent.fromJson(Map<String, dynamic> json) {
//     id = json['id'];
//     name = json['name'];
//     email = json['email'];
//     phone = json['phone'];
//     emailVerifiedAt = json['email_verified_at'];
//     address = json['address'];
//     cityName = json['city_name'];
//     status = json['status'];
//     logo = json['logo'];
//     createdAt = json['created_at'];
//     updatedAt = json['updated_at'];
//     typeAuth = json['typeAuth'];
//     latitude = json['latitude'];
//     longitude = json['longitude'];
//     logoPath = json['logo_path'];
//   }

//   Map<String, dynamic> toJson() {
//     final Map<String, dynamic> data = new Map<String, dynamic>();
//     data['id'] = this.id;
//     data['name'] = this.name;
//     data['email'] = this.email;
//     data['phone'] = this.phone;
//     data['email_verified_at'] = this.emailVerifiedAt;
//     data['address'] = this.address;
//     data['city_name'] = this.cityName;
//     data['status'] = this.status;
//     data['logo'] = this.logo;
//     data['created_at'] = this.createdAt;
//     data['updated_at'] = this.updatedAt;
//     data['typeAuth'] = this.typeAuth;
//     data['latitude'] = this.latitude;
//     data['longitude'] = this.longitude;
//     data['logo_path'] = this.logoPath;
//     return data;
//   }
// }

// class StudentNotification {
//   int? id;
//   String? name;
//   String? phone;
//   int? gradeId;
//   int? genderId;
//   int? schoolId;
//   int? religionId;
//   int? typeBloodId;
//   int? classroomId;
//   int? busId;
//   String? address;
//   String? cityName;
//   int? status;
//   String? tripType;
//   String? parentKey;
//   String? parentSecret;
//   String? dateBirth;
//   String? logo;
//   dynamic latitude;
//   double? longitude;
//   String? createdAt;
//   String? updatedAt;
//   String? logoPath;

//   StudentNotification(
//       {this.id,
//       this.name,
//       this.phone,
//       this.gradeId,
//       this.genderId,
//       this.schoolId,
//       this.religionId,
//       this.typeBloodId,
//       this.classroomId,
//       this.busId,
//       this.address,
//       this.cityName,
//       this.status,
//       this.tripType,
//       this.parentKey,
//       this.parentSecret,
//       this.dateBirth,
//       this.logo,
//       this.latitude,
//       this.longitude,
//       this.createdAt,
//       this.updatedAt,
//       this.logoPath});

//   StudentNotification.fromJson(Map<String, dynamic> json) {
//     id = json['id'];
//     name = json['name'];
//     phone = json['phone'];
//     gradeId = json['grade_id'];
//     genderId = json['gender_id'];
//     schoolId = json['school_id'];
//     religionId = json['religion_id'];
//     typeBloodId = json['type__blood_id'];
//     classroomId = json['classroom_id'];
//     busId = json['bus_id'];
//     address = json['address'];
//     cityName = json['city_name'];
//     status = json['status'];
//     tripType = json['trip_type'];
//     parentKey = json['parent_key'];
//     parentSecret = json['parent_secret'];
//     dateBirth = json['Date_Birth'];
//     logo = json['logo'];
//     latitude = json['latitude'];
//     longitude = json['longitude'];
//     createdAt = json['created_at'];
//     updatedAt = json['updated_at'];
//     logoPath = json['logo_path'];
//   }

//   Map<String, dynamic> toJson() {
//     final Map<String, dynamic> data = new Map<String, dynamic>();
//     data['id'] = this.id;
//     data['name'] = this.name;
//     data['phone'] = this.phone;
//     data['grade_id'] = this.gradeId;
//     data['gender_id'] = this.genderId;
//     data['school_id'] = this.schoolId;
//     data['religion_id'] = this.religionId;
//     data['type__blood_id'] = this.typeBloodId;
//     data['classroom_id'] = this.classroomId;
//     data['bus_id'] = this.busId;
//     data['address'] = this.address;
//     data['city_name'] = this.cityName;
//     data['status'] = this.status;
//     data['trip_type'] = this.tripType;
//     data['parent_key'] = this.parentKey;
//     data['parent_secret'] = this.parentSecret;
//     data['Date_Birth'] = this.dateBirth;
//     data['logo'] = this.logo;
//     data['latitude'] = this.latitude;
//     data['longitude'] = this.longitude;
//     data['created_at'] = this.createdAt;
//     data['updated_at'] = this.updatedAt;
//     data['logo_path'] = this.logoPath;
//     return data;
//   }
// }

import 'dart:convert';

class AllNotificationsModel1 {
  int? currentPage;
  List<NotificationAllData>? data;
  int? total;
  int? perPage;
  int? lastPage;

  AllNotificationsModel1(
      {this.currentPage, this.data, this.total, this.perPage, this.lastPage});

  AllNotificationsModel1.fromJson(Map<String, dynamic> json) {
    currentPage = json['current_page'];
    if (json['data'] != null) {
      data = <NotificationAllData>[];
      json['data'].forEach((v) {
        data!.add(NotificationAllData.fromJson(v));
      });
    }
    total = json['total'];
    perPage = json['per_page'];
    lastPage = json['last_page'];
  }
}

class NotificationAllData {
  final int? id;
  // final int? attendantId;
  // final int? studentId;
  final DateTime? createdAt;
  final DateTime? updatedAt;
  // final int? status;
  Additional? additional;
  String? body;
  // final String? message_en;
  final String? title;
  // final int? tripId;
  // final int? busId;
  final String? notifications_type;

  NotificationAllData({
    this.id,
    // this.attendantId,
    // this.studentId,
    this.createdAt,
    this.updatedAt,
    // this.status,
    this.body,
    this.title,
    this.additional,
    // this.tripId,
    // this.busId,
    // this.message_en,
    this.notifications_type,
  });

  NotificationAllData copyWith({
    int? id,
    // int? attendantId,
    // int? studentId,
    DateTime? createdAt,
    DateTime? updatedAt,
    // int? status,
    String? body,
    String? title,
    Additional? additional,

    // int? tripId,
    // int? busId,

    String? notifications_type,
  }) =>
      NotificationAllData(
        id: id ?? this.id,
        // attendantId: attendantId ?? this.attendantId,
        // studentId: studentId ?? this.studentId,
        createdAt: createdAt ?? this.createdAt,
        updatedAt: updatedAt ?? this.updatedAt,
        // busId: busId ?? this.busId,
        // status: status ?? this.status,
        additional: this.additional,

        body: body ?? this.body,
        title: title ?? this.title,
        // tripId: tripId ?? this.tripId,
        // message_en: message_en ?? this.message_en,
        notifications_type: notifications_type ?? this.notifications_type,
      );

  factory NotificationAllData.fromRawJson(String str) =>
      NotificationAllData.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory NotificationAllData.fromJson(Map<String, dynamic> json) =>
      NotificationAllData(
        id: json["id"],
        // attendantId: json["attendant_id"],
        // studentId: json["student_id"],
        createdAt: json["created_at"] == null
            ? null
            : DateTime.parse(json["created_at"]),
        updatedAt: json["updated_at"] == null
            ? null
            : DateTime.parse(json["updated_at"]),
        // status: json["status"],
        body: json['body'],
        // message_en: json['message_en'],
        notifications_type: json["type"],
        title: json["title"],
        // additional : json['additional'],
        additional: json['additional'] != null
            ? Additional.fromJson(json['additional'])
            : null,
        // tripId: json["trip_id"],
        // busId: json['bus_id'],
      );

  Map<String, dynamic> toJson() => {
    "id": id,
    // "attendant_id": attendantId,
    // "student_id": studentId,
    "created_at": createdAt?.toIso8601String(),
    "updated_at": updatedAt?.toIso8601String(),
    // "status": status,
    "body": body,
    'additional': additional,
    //      if (additional != null) {
    //   data['additional'] = additional!.toJson();
    // },
    "title": title,
    // "message_en": message_en,
    // "trip_id": tripId,
    // "bus_id": busId,

    "type": notifications_type,
  };
}

// class NotificationAllData {
//   int? id;
//   String? body;
//   String? from;
//   String? type;
//   String? title;
//   Additional? additional;
//   bool? readAt;
//   String? createdAt;
//   String? updatedAt;

//   NotificationAllData(
//       {this.id,
//       this.body,
//       this.from,
//       this.type,
//       this.title,
//       this.additional,
//       this.readAt,
//       this.createdAt,
//       this.updatedAt});

//   NotificationAllData.fromJson(Map<String, dynamic> json) {
//     id = json['id'];
//     body = json['body'];
//     from = json['from'];
//     type = json['type'];
//     title = json['title'];
// additional = json['additional'] != null
//     ? new Additional.fromJson(json['additional'])
//     : null;
//     readAt = json['read_at'];
//     createdAt = json['created_at'];
//     updatedAt = json['updated_at'];
//   }

// }

class Additional {
  int? tripId;

  Additional({this.tripId});

  Additional.fromJson(Map<String, dynamic> json) {
    tripId = json['trip_id'];
  }
}

// class AllNotificationsModel1 {
//   int? currentPage;
//   List<NotificationAllData>? data;
//   int? total;
//   int? perPage;
//   int? lastPage;

//   AllNotificationsModel1(
//       {this.currentPage, this.data, this.total, this.perPage, this.lastPage});

//   AllNotificationsModel1.fromJson(Map<String, dynamic> json) {
//     currentPage = json['current_page'];
//     if (json['data'] != null) {
//       data = <NotificationAllData>[];
//       json['data'].forEach((v) {
//         data!.add(new NotificationAllData.fromJson(v));
//       });
//     }
//     total = json['total'];
//     perPage = json['per_page'];
//     lastPage = json['last_page'];
//   }

//   // Map<String, dynamic> toJson() {
//   //   final Map<String, dynamic> data = new Map<String, dynamic>();
//   //   data['current_page'] = this.currentPage;
//   //   if (this.data != null) {
//   //     data['data'] = this.data!.map((v) => v.toJson()).toList();
//   //   }
//   //   data['total'] = this.total;
//   //   data['per_page'] = this.perPage;
//   //   data['last_page'] = this.lastPage;
//   //   return data;
//   // }
// }

// class NotificationAllData {
int? id;
String? body;
String? from;
String? type;
String? title;
// Null? readAt;
String? createdAt;
String? updatedAt;

// NotificationAllData(
//     {this.id,
//     this.body,
//     this.from,
//     this.type,
//     this.title,
//     // this.readAt,
//     this.createdAt,
//     this.updatedAt});

// NotificationAllData.fromJson(Map<String, dynamic> json) {
//   id = json['id'];
//   body = json['body'];
//   from = json['from'];
//   type = json['type'];
//   title = json['title'];
//   // readAt = json['read_at'];
//   createdAt = json['created_at'];
//   updatedAt = json['updated_at'];
// }

// Map<String, dynamic> toJson() {
//   final Map<String, dynamic> data = new Map<String, dynamic>();
//   data['id'] = this.id;
//   data['body'] = this.body;
//   // data['from'] = this.from;
//   data['type'] = this.type;
//   data['title'] = this.title;
//   // data['read_at'] = this.readAt;
//   data['created_at'] = this.createdAt;
//   data['updated_at'] = this.updatedAt;
//   return data;
// }
// }