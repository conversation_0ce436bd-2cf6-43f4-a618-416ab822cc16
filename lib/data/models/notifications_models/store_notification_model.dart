import 'dart:convert';

class StoreNotificationModel {
  final String? data;
  final String? message;
  final bool? status;

  StoreNotificationModel({
    this.data,
    this.message,
    this.status,
  });

  StoreNotificationModel copyWith({
    String? data,
    String? message,
    bool? status,
  }) =>
      StoreNotificationModel(
        data: data ?? this.data,
        message: message ?? this.message,
        status: status ?? this.status,
      );

  factory StoreNotificationModel.fromJson(String str) => StoreNotificationModel.fromMap(json.decode(str));

  String toJson() => json.encode(toMap());

  factory StoreNotificationModel.fromMap(Map<String, dynamic> json) => StoreNotificationModel(
    data: json["data"],
    message: json["message"],
    status: json["status"],
  );

  Map<String, dynamic> toMap() => {
    "data": data,
    "message": message,
    "status": status,
  };
}
