import 'dart:convert';

class FCMResponse {
  final double? multicastId;
  final int? success;
  final int? failure;
  final int? canonicalIds;
  final List<Result>? results;

  FCMResponse({
    this.multicastId,
    this.success,
    this.failure,
    this.canonicalIds,
    this.results,
  });

  FCMResponse copyWith({
    double? multicastId,
    int? success,
    int? failure,
    int? canonicalIds,
    List<Result>? results,
  }) =>
      FCMResponse(
        multicastId: multicastId ?? this.multicastId,
        success: success ?? this.success,
        failure: failure ?? this.failure,
        canonicalIds: canonicalIds ?? this.canonicalIds,
        results: results ?? this.results,
      );

  factory FCMResponse.fromJson(String str) => FCMResponse.fromMap(json.decode(str));

  String toJson() => json.encode(toMap());

  factory FCMResponse.fromMap(Map<String, dynamic> json) => FCMResponse(
    multicastId: json["multicast_id"]?.toDouble(),
    success: json["success"],
    failure: json["failure"],
    canonicalIds: json["canonical_ids"],
    results: json["results"] == null ? [] : List<Result>.from(json["results"]!.map((x) => Result.fromMap(x))),
  );

  Map<String, dynamic> toMap() => {
    "multicast_id": multicastId,
    "success": success,
    "failure": failure,
    "canonical_ids": canonicalIds,
    "results": results == null ? [] : List<dynamic>.from(results!.map((x) => x.toMap())),
  };
}

class Result {
  final String? messageId;
  final String? error;

  Result({
    this.messageId,
    this.error,
  });

  Result copyWith({
    String? messageId,
    String? error,
  }) =>
      Result(
        messageId: messageId ?? this.messageId,
        error: error ?? this.error,
      );

  factory Result.fromJson(String str) => Result.fromMap(json.decode(str));

  String toJson() => json.encode(toMap());

  factory Result.fromMap(Map<String, dynamic> json) => Result(
    messageId: json["message_id"],
    error: json["error"],
  );

  Map<String, dynamic> toMap() => {
    "message_id": messageId,
    "error": error,
  };
}
