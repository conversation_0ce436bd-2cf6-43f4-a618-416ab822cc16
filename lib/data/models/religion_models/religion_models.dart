import 'package:bus/data/models/student_models/student_grade_models.dart';
import 'package:equatable/equatable.dart';
import 'package:json_annotation/json_annotation.dart';

part 'religion_models.g.dart';

@JsonSerializable()
class ReligionModels extends Equatable {
  final bool? status;
  final String? message;
  final List<StudentGradeModels>? data;

  const ReligionModels({
    this.data,
    this.status,
    this.message,
  });

  factory ReligionModels.fromJson(Map<String, dynamic> json) {
    return _$ReligionModelsFromJson(json);
  }

  Map<String, dynamic> toJson() => _$ReligionModelsToJson(this);

  @override
  List<Object?> get props => [
        data,
        status,
        message,
      ];
}
