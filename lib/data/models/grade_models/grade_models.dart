import 'package:bus/data/models/student_models/student_grade_models.dart';
import 'package:equatable/equatable.dart';
import 'package:json_annotation/json_annotation.dart';

part 'grade_models.g.dart';

@JsonSerializable()
class GradeModels extends Equatable {
  final bool? status;
  final String? message;
  final List<StudentGradeModels>? data;

  const GradeModels({
    this.status,
    this.message,
    this.data,
  });

  factory GradeModels.fromJson(Map<String, dynamic> json) {
    return _$GradeModelsFromJson(json);
  }

  Map<String, dynamic> toJson() => _$GradeModelsToJson(this);

  @override
  List<Object?> get props => [
        status,
        message,
        data,
      ];
}
