// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'add_driver_data_models.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

AddDriverDataModels _$AddDriverDataModelsFromJson(Map<String, dynamic> json) =>
    AddDriverDataModels(
      name: json['name'] as String?,
      school_id: (json['school_id'] as num?)?.toInt(),
      id: (json['id'] as num?)?.toInt(),
      created_at: json['created_at'] as String?,
      phone: json['phone'] as String?,
      logo_path: json['logo_path'] as String?,
      bus_id: (json['bus_id'] as num?)?.toInt(),
      gender_id: json['gender_id'],
      type: json['type'] as String?,
      updated_at: json['updated_at'] as String?,
      username: json['username'] as String?,
    );

Map<String, dynamic> _$AddDriverDataModelsToJson(
        AddDriverDataModels instance) =>
    <String, dynamic>{
      'type': instance.type,
      'name': instance.name,
      'school_id': instance.school_id,
      'gender_id': instance.gender_id,
      'username': instance.username,
      'phone': instance.phone,
      'bus_id': instance.bus_id,
      'updated_at': instance.updated_at,
      'created_at': instance.created_at,
      'id': instance.id,
      'logo_path': instance.logo_path,
    };
