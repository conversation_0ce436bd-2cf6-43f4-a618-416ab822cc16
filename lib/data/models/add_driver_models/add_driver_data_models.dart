import 'package:equatable/equatable.dart';
import 'package:json_annotation/json_annotation.dart';

part 'add_driver_data_models.g.dart';

@JsonSerializable()
class AddDriverDataModels extends Equatable {
  final String? type;
  final String? name;
  final int? school_id;
  final dynamic gender_id;
  final String? username;
  final String? phone;
  final int? bus_id;
  final String? updated_at;
  final String? created_at;
  final int? id;
  final String? logo_path;

  const AddDriverDataModels({
    this.name,
    this.school_id,
    this.id,
    this.created_at,
    this.phone,
    this.logo_path,
    this.bus_id,
    this.gender_id,
    this.type,
    this.updated_at,
    this.username,
  });

  factory AddDriverDataModels.fromJson(Map<String, dynamic> json) {
    return _$AddDriverDataModelsFromJson(json);
  }

  Map<String, dynamic> toJson() => _$AddDriverDataModelsToJson(this);

  @override
  List<Object?> get props => [
        name,
        school_id,
        id,
        created_at,
        phone,
        logo_path,
        bus_id,
        gender_id,
        type,
        updated_at,
        username,
      ];
}
