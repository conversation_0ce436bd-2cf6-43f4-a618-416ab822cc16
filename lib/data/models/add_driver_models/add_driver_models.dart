import 'package:bus/data/models/add_driver_models/add_driver_data_models.dart';
import 'package:equatable/equatable.dart';
import 'package:json_annotation/json_annotation.dart';

part 'add_driver_models.g.dart';

@JsonSerializable()
class AddDriverModels extends Equatable {
  final bool? errors;
  final String? message;
  final String? messages;
  final AddDriverDataModels? data;

  const AddDriverModels({
    this.message,
    this.errors,
    this.data,
    this.messages,
  });

  factory AddDriverModels.fromJson(Map<String, dynamic> json) {
    return _$AddDriverModelsFromJson(json);
  }

  Map<String, dynamic> toJson() => _$AddDriverModelsToJson(this);

  @override
  List<Object?> get props => [
        message,
        errors,
        data,
        messages,
      ];
}
