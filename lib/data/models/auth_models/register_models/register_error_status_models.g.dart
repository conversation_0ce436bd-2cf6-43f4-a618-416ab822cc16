// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'register_error_status_models.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

RegisterErrorStatusModels _$RegisterErrorStatusModelsFromJson(
        Map<String, dynamic> json) =>
    RegisterErrorStatusModels(
      status: (json['status'] as num?)?.toInt(),
      server_titm: (json['server_titm'] as num?)?.toInt(),
      registerErrorStatusMessageModels: json['messages'] == null
          ? null
          : RegisterErrorStatusMessageModels.fromJson(
              json['messages'] as Map<String, dynamic>),
    );

Map<String, dynamic> _$RegisterErrorStatusModelsToJson(
        RegisterErrorStatusModels instance) =>
    <String, dynamic>{
      'status': instance.status,
      'server_titm': instance.server_titm,
      'messages': instance.registerErrorStatusMessageModels,
    };

RegisterErrorStatusMessageModels _$RegisterErrorStatusMessageModelsFromJson(
        Map<String, dynamic> json) =>
    RegisterErrorStatusMessageModels(
      email:
          (json['email'] as List<dynamic>?)?.map((e) => e as String).toList(),
      phone:
          (json['phone'] as List<dynamic>?)?.map((e) => e as String).toList(),
    );

Map<String, dynamic> _$RegisterErrorStatusMessageModelsToJson(
        RegisterErrorStatusMessageModels instance) =>
    <String, dynamic>{
      'email': instance.email,
      'phone': instance.phone,
    };
