import 'package:equatable/equatable.dart';
import 'package:json_annotation/json_annotation.dart';

part 'register_error_models.g.dart';

@JsonSerializable()
class RegisterErrorModels extends Equatable {
  final String? data;
  final RegisterErrorModels? registerErrorModels;

  const RegisterErrorModels({
    this.data,
    this.registerErrorModels,
  });

  factory RegisterErrorModels.fromJson(Map<String, dynamic> json) {
    return _$RegisterErrorModelsFromJson(json);
  }

  Map<String, dynamic> toJson() => _$RegisterErrorModelsToJson(this);

  @override
  List<Object?> get props => [
        data,
        registerErrorModels,
      ];
}
