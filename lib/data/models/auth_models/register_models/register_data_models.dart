import 'package:equatable/equatable.dart';
import 'package:json_annotation/json_annotation.dart';

part 'register_data_models.g.dart';

@JsonSerializable()
class RegisterDataModels extends Equatable {
  final String? name;
  final String? email;
  final String? phone;
  final String? address;
  final String? updated_at;
  final String? created_at;
  final int? id;
  final String? logo_path;

  const RegisterDataModels({
    this.address,
    this.name,
    this.email,
    this.created_at,
    this.id,
    this.phone,
    this.updated_at,
    this.logo_path,
  });

  factory RegisterDataModels.fromJson(Map<String, dynamic> json) {
    return _$RegisterDataModelsFromJson(json);
  }

  Map<String, dynamic> toJson() => _$RegisterDataModelsToJson(this);

  @override
  List<Object?> get props => [
        address,
        name,
        email,
        created_at,
        updated_at,
        phone,
        logo_path,
        id,
      ];
}
