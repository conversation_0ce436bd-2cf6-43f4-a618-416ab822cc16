import 'package:bus/data/models/auth_models/register_models/register_data_models.dart';
import 'package:equatable/equatable.dart';
import 'package:json_annotation/json_annotation.dart';

part 'register_models.g.dart';

@JsonSerializable()
class RegisterModels extends Equatable {
  final String? massage;
  final String? token;
  @J<PERSON><PERSON>ey(name: 'data')
  final RegisterDataModels? registerDataModels;

  const RegisterModels({
    this.token,
    this.massage,
    this.registerDataModels,
  });

  factory RegisterModels.fromJson(Map<String, dynamic> json) {
    return _$RegisterModelsFromJson(json);
  }

  Map<String, dynamic> toJson() => _$RegisterModelsToJson(this);

  @override
  List<Object?> get props => [
        massage,
        token,
        registerDataModels,
      ];
}
