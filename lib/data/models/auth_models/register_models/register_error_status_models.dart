import 'package:equatable/equatable.dart';
import 'package:json_annotation/json_annotation.dart';

part 'register_error_status_models.g.dart';

@JsonSerializable()
class RegisterErrorStatusModels extends Equatable {
  final int? status;
  final int? server_titm;
  @JsonKey(name: 'messages')
  final RegisterErrorStatusMessageModels? registerErrorStatusMessageModels;

  const RegisterErrorStatusModels({
    this.status,
    this.server_titm,
    this.registerErrorStatusMessageModels,
  });

  factory RegisterErrorStatusModels.fromJson(Map<String, dynamic> json) {
    return _$RegisterErrorStatusModelsFromJson(json);
  }

  Map<String, dynamic> toJson() => _$RegisterErrorStatusModelsToJson(this);

  @override
  List<Object?> get props => [
        status,
        server_titm,
        registerErrorStatusMessageModels,
      ];
}

@JsonSerializable()
class RegisterErrorStatusMessageModels extends Equatable {
  final List<String>? email;
  final List<String>? phone;

  const RegisterErrorStatusMessageModels({
    this.email,
    this.phone,
  });

  factory RegisterErrorStatusMessageModels.fromJson(Map<String, dynamic> json) {
    return _$RegisterErrorStatusMessageModelsFromJson(json);
  }

  Map<String, dynamic> toJson() => _$RegisterErrorStatusMessageModelsToJson(this);

  @override
  List<Object?> get props => [
        email,
        phone,
      ];
}
