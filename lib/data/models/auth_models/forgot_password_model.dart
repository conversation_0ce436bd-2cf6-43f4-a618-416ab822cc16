import 'dart:convert';

class ForgotPasswordModel {
  final bool? errors;
  final Messages? messages;

  ForgotPasswordModel({
    this.errors,
    this.messages,
  });

  ForgotPasswordModel copyWith({
    bool? errors,
    Messages? messages,
  }) =>
      ForgotPasswordModel(
        errors: errors ?? this.errors,
        messages: messages ?? this.messages,
      );

  factory ForgotPasswordModel.fromJson(String str) => ForgotPasswordModel.fromMap(json.decode(str));

  String toJson() => json.encode(toMap());

  factory ForgotPasswordModel.fromMap(Map<String, dynamic> json) => ForgotPasswordModel(
    errors: json["errors"],
    messages: json["messages"] == null ? null : Messages.fromMap(json["messages"]),
  );

  Map<String, dynamic> toMap() => {
    "errors": errors,
    "messages": messages?.toMap(),
  };
}

class Messages {
  final List<String>? email;

  Messages({
    this.email,
  });

  Messages copyWith({
    List<String>? email,
  }) =>
      Messages(
        email: email ?? this.email,
      );

  factory Messages.fromJson(String str) => Messages.fromMap(json.decode(str));

  String toJson() => json.encode(toMap());

  factory Messages.fromMap(Map<String, dynamic> json) => Messages(
    email: json["email"] == null ? [] : List<String>.from(json["email"]!.map((x) => x)),
  );

  Map<String, dynamic> toMap() => {
    "email": email == null ? [] : List<dynamic>.from(email!.map((x) => x)),
  };
}
