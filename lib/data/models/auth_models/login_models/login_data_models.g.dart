// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'login_data_models.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

LoginDataModels _$LoginDataModelsFromJson(Map<String, dynamic> json) =>
    LoginDataModels(
      email: json['email'] as String?,
      status: json['status'] as String?,
      phone: json['phone'] as String?,
      address: json['address'] as String?,
      logo_path: json['logo_path'] as String?,
      updated_at: json['updated_at'] as String?,
      created_at: json['created_at'] as String?,
      name: json['name'] as String?,
      id: (json['id'] as num?)?.toInt(),
      deleted_at: json['deleted_at'] as String?,
      city_name: json['city_name'] as String?,
      email_verified_at: json['email_verified_at'] as String?,
      logo: json['logo'] as String?,
      typeAuth: json['typeAuth'] as String?,
      latitude: json['latitude'] as String?,
      longitude: json['longitude'] as String?,
    );

Map<String, dynamic> _$LoginDataModelsToJson(LoginDataModels instance) =>
    <String, dynamic>{
      'id': instance.id,
      'name': instance.name,
      'email': instance.email,
      'phone': instance.phone,
      'email_verified_at': instance.email_verified_at,
      'address': instance.address,
      'city_name': instance.city_name,
      'status': instance.status,
      'logo': instance.logo,
      'deleted_at': instance.deleted_at,
      'created_at': instance.created_at,
      'updated_at': instance.updated_at,
      'typeAuth': instance.typeAuth,
      'logo_path': instance.logo_path,
      'latitude': instance.latitude,
      'longitude': instance.longitude,
    };
