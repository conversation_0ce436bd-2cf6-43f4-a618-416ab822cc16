import 'package:equatable/equatable.dart';
import 'package:json_annotation/json_annotation.dart';

import 'login_data_models.dart';

part 'login_models.g.dart';

@JsonSerializable()
class LoginModels extends Equatable {
  final String? token;
  final String? massage;
  final LoginDataModels? loginDataModels;

  const LoginModels({
    this.token,
    this.massage,
    this.loginDataModels,
  });


  factory LoginModels.fromJson(Map<String, dynamic> json) {
    return _$LoginModelsFromJson(json);
  }

  Map<String, dynamic> toJson() => _$LoginModelsToJson(this);

  @override
  List<Object?> get props => [
        token,
        massage,
        loginDataModels,
      ];
}
