import 'package:equatable/equatable.dart';
import 'package:json_annotation/json_annotation.dart';

part 'login_data_models.g.dart';

@JsonSerializable()
class LoginDataModels extends Equatable {
  final int? id;
  final String? name;
  final String? email;
  final String? phone;
  final String? email_verified_at;
  final String? address;
  final String? city_name;
  final String? status;
  final String? logo;
  final String? deleted_at;
  final String? created_at;
  final String? updated_at;
  final String? typeAuth;
  final String? logo_path;
  final String? latitude;
  final String? longitude;

  const LoginDataModels({
    this.email,
    this.status,
    this.phone,
    this.address,
    this.logo_path,
    this.updated_at,
    this.created_at,
    this.name,
    this.id,
    this.deleted_at,
    this.city_name,
    this.email_verified_at,
    this.logo,
    this.typeAuth,
    this.latitude,
    this.longitude,
  });

  factory LoginDataModels.fromJson(Map<String, dynamic> json) {
    return _$LoginDataModelsFromJson(json);
  }

  Map<String, dynamic> toJson() => _$LoginDataModelsToJson(this);

  @override
  List<Object?> get props => [
        email,
        status,
        phone,
        address,
        logo_path,
        updated_at,
        created_at,
        name,
        id,
        deleted_at,
        city_name,
        email_verified_at,
        logo,
        typeAuth,
        latitude,
        longitude,
      ];
}
