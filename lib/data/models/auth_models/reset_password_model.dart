import 'dart:convert';

class ResetPasswordModel {
  final Status? status;
  final Data? data;
  final bool? errors;
  final String? messages;

  ResetPasswordModel({
    this.status,
    this.data,
    this.errors,
    this.messages,
  });

  ResetPasswordModel copyWith({
    Status? status,
    Data? data,
    bool? errors,
    String? messages,
  }) =>
      ResetPasswordModel(
        status: status ?? this.status,
        data: data ?? this.data,
        errors: errors ?? this.errors,
        messages: messages ?? this.messages,
      );

  factory ResetPasswordModel.fromJson(String str) => ResetPasswordModel.fromMap(json.decode(str));

  String toJson() => json.encode(toMap());

  factory ResetPasswordModel.fromMap(Map<String, dynamic> json) => ResetPasswordModel(
    status: json["status"] == null ? null : Status.fromMap(json["status"]),
    data: json["data"] == null ? null : Data.fromMap(json["data"]),
    errors: json["errors"],
    messages: json["messages"],
  );

  Map<String, dynamic> toMap() => {
    "status": status?.toMap(),
    "data": data?.toMap(),
    "errors": errors,
    "messages": messages,
  };
}

class Data {
  final String? token;

  Data({
    this.token,
  });

  Data copyWith({
    String? token,
  }) =>
      Data(
        token: token ?? this.token,
      );

  factory Data.fromJson(String str) => Data.fromMap(json.decode(str));

  String toJson() => json.encode(toMap());

  factory Data.fromMap(Map<String, dynamic> json) => Data(
    token: json["token"],
  );

  Map<String, dynamic> toMap() => {
    "token": token,
  };
}

class Status {
  final int? status;
  final dynamic messages;

  Status({
    this.status,
    this.messages,
  });

  Status copyWith({
    int? status,
    dynamic messages,
  }) =>
      Status(
        status: status ?? this.status,
        messages: messages ?? this.messages,
      );

  factory Status.fromJson(String str) => Status.fromMap(json.decode(str));

  String toJson() => json.encode(toMap());

  factory Status.fromMap(Map<String, dynamic> json) => Status(
    status: json["status"],
    messages: json["messages"],
  );

  Map<String, dynamic> toMap() => {
    "status": status,
    "messages": messages,
  };
}

class Messages {
  final List<String>? code;
  final List<String>? email;
  final List<String>? password;

  Messages({
    this.code,
    this.email,
    this.password,
  });

  Messages copyWith({
    List<String>? code,
    List<String>? email,
    List<String>? password,
  }) =>
      Messages(
        code: code ?? this.code,
        email: email ?? this.email,
        password: password ?? this.password,
      );

  factory Messages.fromJson(String str) => Messages.fromMap(json.decode(str));

  String toJson() => json.encode(toMap());

  factory Messages.fromMap(Map<String, dynamic> json) => Messages(
    code: json["code"] == null ? [] : List<String>.from(json["code"]!.map((x) => x)),
    email: json["email"] == null ? [] : List<String>.from(json["email"]!.map((x) => x)),
    password: json["password"] == null ? [] : List<String>.from(json["password"]!.map((x) => x)),
  );

  Map<String, dynamic> toMap() => {
    "code": code == null ? [] : List<dynamic>.from(code!.map((x) => x)),
    "email": email == null ? [] : List<dynamic>.from(email!.map((x) => x)),
    "password": password == null ? [] : List<dynamic>.from(password!.map((x) => x)),
  };
}
