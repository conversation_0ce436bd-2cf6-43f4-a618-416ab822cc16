// class SendCodeVerificationModels {
//   String? data;
//   String? massage;
//   bool? status;

//   SendCodeVerificationModels({this.data, this.massage, this.status,});

//   SendCodeVerificationModels.fromJson(Map<String, dynamic> json) {
//     data = json['data'];
//     massage = json['massage'];
//   }

//   Map<String, dynamic> toJson() {
//     final Map<String, dynamic> data = new Map<String, dynamic>();
//     data['data'] = this.data;
//     data['massage'] = this.massage;
//     return data;
//   }
// }


class SendCodeVerificationModels {
  Data? data;
  String? message;
  bool? status;

  SendCodeVerificationModels({this.data, this.message, this.status});

  SendCodeVerificationModels.fromJson(Map<String, dynamic> json) {
    data = json['data'] != null ? new Data.fromJson(json['data']) : null;
    message = json['message'];
    status = json['status'];
  }

  // Map<String, dynamic> toJson() {
  //   final Map<String, dynamic> data = new Map<String, dynamic>();
  //   if (this.data != null) {
  //     data['data'] = this.data!.toJson();
  //   }
  //   data['message'] = this.message;
  //   data['status'] = this.status;
  //   return data;
  // }
}

class Data {
  int? id;
  String? name;
  String? email;
  String? phone;
  String? emailVerifiedAt;
  String? address;
  // Null? cityName;
  int? status;
  String? logo;
  String? createdAt;
  String? updatedAt;
  String? typeAuth;
  String? latitude;
  String? longitude;
  String? logoPath;

  Data(
      {this.id,
      this.name,
      this.email,
      this.phone,
      this.emailVerifiedAt,
      this.address,
      // this.cityName,
      this.status,
      this.logo,
      this.createdAt,
      this.updatedAt,
      this.typeAuth,
      this.latitude,
      this.longitude,
      this.logoPath});

  Data.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    name = json['name'];
    email = json['email'];
    phone = json['phone'];
    emailVerifiedAt = json['email_verified_at'];
    address = json['address'];
    // cityName = json['city_name'];
    status = json['status'];
    logo = json['logo'];
    createdAt = json['created_at'];
    updatedAt = json['updated_at'];
    typeAuth = json['typeAuth'];
    latitude = json['latitude'];
    longitude = json['longitude'];
    logoPath = json['logo_path'];
  }

  // Map<String, dynamic> toJson() {
  //   final Map<String, dynamic> data = new Map<String, dynamic>();
  //   data['id'] = this.id;
  //   data['name'] = this.name;
  //   data['email'] = this.email;
  //   data['phone'] = this.phone;
  //   data['email_verified_at'] = this.emailVerifiedAt;
  //   data['address'] = this.address;
  //   data['city_name'] = this.cityName;
  //   data['status'] = this.status;
  //   data['logo'] = this.logo;
  //   data['created_at'] = this.createdAt;
  //   data['updated_at'] = this.updatedAt;
  //   data['typeAuth'] = this.typeAuth;
  //   data['latitude'] = this.latitude;
  //   data['longitude'] = this.longitude;
  //   data['logo_path'] = this.logoPath;
  //   return data;
  // }
}