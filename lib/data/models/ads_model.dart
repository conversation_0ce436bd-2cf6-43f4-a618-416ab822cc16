import 'dart:convert';

class AdsModel {
  List<Datum>? data;
  String? message;
  bool? status;

  AdsModel({
    this.data,
    this.message,
    this.status,
  });

  factory AdsModel.fromJson(String str) => AdsModel.fromMap(json.decode(str));

  String toJson() => json.encode(toMap());

  factory AdsModel.fromMap(Map<String, dynamic> json) => AdsModel(
    data: json["data"] == null ? [] : List<Datum>.from(json["data"]!.map((x) => Datum.fromMap(x))),
    message: json["message"],
    status: json["status"],
  );

  Map<String, dynamic> toMap() => {
    "data": data == null ? [] : List<dynamic>.from(data!.map((x) => x.toMap())),
    "message": message,
    "status": status,
  };
}

class Datum {
  int? adsId;
  Ads? ads;

  Datum({
    this.adsId,
    this.ads,
  });

  factory Datum.fromJson(String str) => Datum.fromMap(json.decode(str));

  String toJson() => json.encode(toMap());

  factory Datum.fromMap(Map<String, dynamic> json) => Datum(
    adsId: json["ades_id"],
    ads: json["ades"] == null ? null : Ads.fromMap(json["ades"]),
  );

  Map<String, dynamic> toMap() => {
    "ades_id": adsId,
    "ades": ads?.toMap(),
  };
}

class Ads {
  int? id;
  String? title;
  String? body;
  String? link;
  String? image;
  String? alt;
  DateTime? createdAt;
  DateTime? updatedAt;
  String? imagePath;

  Ads({
    this.id,
    this.title,
    this.body,
    this.link,
    this.image,
    this.alt,
    this.createdAt,
    this.updatedAt,
    this.imagePath,
  });

  factory Ads.fromJson(String str) => Ads.fromMap(json.decode(str));

  String toJson() => json.encode(toMap());

  factory Ads.fromMap(Map<String, dynamic> json) => Ads(
    id: json["id"],
    title: json["title"],
    body: json["body"],
    link: json["link"],
    image: json["image"],
    alt: json["alt"],
    createdAt: json["created_at"] == null ? null : DateTime.parse(json["created_at"]),
    updatedAt: json["updated_at"] == null ? null : DateTime.parse(json["updated_at"]),
    imagePath: json["image_path"],
  );

  Map<String, dynamic> toMap() => {
    "id": id,
    "title": title,
    "body": body,
    "link": link,
    "image": image,
    "alt": alt,
    "created_at": createdAt?.toIso8601String(),
    "updated_at": updatedAt?.toIso8601String(),
    "image_path": imagePath,
  };
}
