// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'supervisor_info_models.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

SupervisorInfoModels _$SupervisorInfoModelsFromJson(
        Map<String, dynamic> json) =>
    SupervisorInfoModels(
      username: json['username'] as String?,
      bus_id: (json['bus_id'] as num?)?.toInt(),
      status: (json['status'] as num?)?.toInt(),
      address: json['address'] as String?,
      name: json['name'] as String?,
      logo: json['logo'] as String?,
      city_name: json['city_name'] as String?,
      gender_id: (json['gender_id'] as num?)?.toInt(),
      religion_id: (json['religion_id'] as num?)?.toInt(),
      type__blood_id: (json['type__blood_id'] as num?)?.toInt(),
      school_id: (json['school_id'] as num?)?.toInt(),
      updated_at: json['updated_at'] as String?,
      created_at: json['created_at'] as String?,
      id: (json['id'] as num?)?.toInt(),
      phone: json['phone'] as String?,
      type: json['type'] as String?,
      deleted_at: json['deleted_at'] as String?,
      logo_path: json['logo_path'] as String?,
      email: json['email'] as String?,
      email_verified_at: json['email_verified_at'] as String?,
      typeAuth: json['typeAuth'] as String?,
      birth_date: json['birth_date'] as String?,
      Joining_Date: json['Joining_Date'] as String?,
      drivers: json['drivers'] as String?,
      schools: json['schools'] == null
          ? null
          : LoginDataModels.fromJson(json['schools'] as Map<String, dynamic>),
      gender: json['gender'] == null
          ? null
          : StudentGradeModels.fromJson(json['gender'] as Map<String, dynamic>),
      religion: json['religion'] == null
          ? null
          : StudentGradeModels.fromJson(
              json['religion'] as Map<String, dynamic>),
      type_blood: json['type_blood'] == null
          ? null
          : StudentGradeModels.fromJson(
              json['type_blood'] as Map<String, dynamic>),
      bus: json['bus'] == null
          ? null
          : SupervisorBusAdminModels.fromJson(
              json['bus'] as Map<String, dynamic>),
    );

Map<String, dynamic> _$SupervisorInfoModelsToJson(
        SupervisorInfoModels instance) =>
    <String, dynamic>{
      'id': instance.id,
      'email': instance.email,
      'username': instance.username,
      'name': instance.name,
      'gender_id': instance.gender_id,
      'school_id': instance.school_id,
      'religion_id': instance.religion_id,
      'type__blood_id': instance.type__blood_id,
      'Joining_Date': instance.Joining_Date,
      'address': instance.address,
      'city_name': instance.city_name,
      'status': instance.status,
      'logo': instance.logo,
      'type': instance.type,
      'bus_id': instance.bus_id,
      'drivers': instance.drivers,
      'phone': instance.phone,
      'birth_date': instance.birth_date,
      'email_verified_at': instance.email_verified_at,
      'deleted_at': instance.deleted_at,
      'created_at': instance.created_at,
      'updated_at': instance.updated_at,
      'typeAuth': instance.typeAuth,
      'logo_path': instance.logo_path,
      'schools': instance.schools,
      'gender': instance.gender,
      'religion': instance.religion,
      'type_blood': instance.type_blood,
      'bus': instance.bus,
    };
