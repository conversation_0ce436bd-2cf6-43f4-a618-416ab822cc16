import 'package:bus/data/models/supervisor_mdodels/supervisor_data_models.dart';
import 'package:equatable/equatable.dart';
import 'package:json_annotation/json_annotation.dart';

part 'supervisor_models.g.dart';

@JsonSerializable()
class SupervisorModels extends Equatable {
  final bool? status;
  final String? message;
  final SupervisorDataModels? data;

  const SupervisorModels({
    this.status,
    this.message,
    this.data,
  });

  factory SupervisorModels.fromJson(Map<String, dynamic> json) {
    return _$SupervisorModelsFromJson(json);
  }

  Map<String, dynamic> toJson() => _$SupervisorModelsToJson(this);

  @override
  List<Object?> get props => [
        status,
        message,
        data,
      ];
}
