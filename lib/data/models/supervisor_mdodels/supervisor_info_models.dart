import 'package:bus/data/models/student_models/student_grade_models.dart';
import 'package:bus/data/models/supervisor_mdodels/supervisor_bus_admin_models.dart';
import 'package:equatable/equatable.dart';
import 'package:json_annotation/json_annotation.dart';

import '../auth_models/login_models/login_data_models.dart';

part 'supervisor_info_models.g.dart';

@JsonSerializable()
class SupervisorInfoModels extends Equatable {
  final int? id;
  final String? email;
  final String? username;
  final String? name;
  final int? gender_id;
  final int? school_id;
  final int? religion_id;
  final int? type__blood_id;
  final String? Joining_Date;
  final String? address;
  final String? city_name;
  final int? status;
  final String? logo;
  final String? type;
  final int? bus_id;
  final String? drivers;
  final String? phone;
  final String? birth_date;
  final String? email_verified_at;
  final String? deleted_at;
  final String? created_at;
  final String? updated_at;
  final String? typeAuth;
  final String? logo_path;
  final LoginDataModels? schools;
  final StudentGradeModels? gender;
  final StudentGradeModels? religion;
  final StudentGradeModels? type_blood;
  final SupervisorBusAdminModels? bus;

  const SupervisorInfoModels({
    this.username,
    this.bus_id,
    this.status,
    this.address,
    this.name,
    this.logo,
    this.city_name,
    this.gender_id,
    this.religion_id,
    this.type__blood_id,
    this.school_id,
    this.updated_at,
    this.created_at,
    this.id,
    this.phone,
    this.type,
    this.deleted_at,
    this.logo_path,
    this.email,
    this.email_verified_at,
    this.typeAuth,
    this.birth_date,
    this.Joining_Date,
    this.drivers,
    this.schools,
    this.gender,
    this.religion,
    this.type_blood,
    this.bus,
  });

  factory SupervisorInfoModels.fromJson(Map<String, dynamic> json) {
    return _$SupervisorInfoModelsFromJson(json);
  }

  Map<String, dynamic> toJson() => _$SupervisorInfoModelsToJson(this);

  @override
  List<Object?> get props => [
        status,
        address,
        name,
        username,
        logo,
        city_name,
        gender_id,
        religion_id,
        type__blood_id,
        school_id,
        updated_at,
        created_at,
        id,
        phone,
        type,
        deleted_at,
        logo_path,
        email,
        email_verified_at,
        typeAuth,
        birth_date,
        Joining_Date,
        drivers,
        schools,
        gender,
        religion,
        type_blood,
        bus,
      ];
}
