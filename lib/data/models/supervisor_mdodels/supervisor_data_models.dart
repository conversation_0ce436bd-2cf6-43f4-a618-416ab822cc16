import 'package:bus/data/models/student_models/student_links_models.dart';
import 'package:bus/data/models/supervisor_mdodels/supervisor_info_models.dart';
import 'package:equatable/equatable.dart';
import 'package:json_annotation/json_annotation.dart';

part 'supervisor_data_models.g.dart';

@JsonSerializable()
class SupervisorDataModels extends Equatable {
  final int? current_page;
  final String? first_page_url;
  final int? from;
  final int? last_page;
  final String? last_page_url;
  final String? next_page_url;
  final String? path;
  final dynamic per_page;
  final String? prev_page_url;
  final int? to;
  final int? total;
  final List<StudentLinksModels>? links;
  final List<SupervisorInfoModels>? data;

  const SupervisorDataModels({
    this.current_page,
    this.last_page,
    this.path,
    this.from,
    this.first_page_url,
    this.last_page_url,
    this.next_page_url,
    this.per_page,
    this.prev_page_url,
    this.to,
    this.links,
    this.total,
    this.data,
  });

  factory SupervisorDataModels.fromJson(Map<String, dynamic> json) {
    return _$SupervisorDataModelsFromJson(json);
  }

  Map<String, dynamic> toJson() => _$SupervisorDataModelsToJson(this);

  @override
  List<Object?> get props => [
        current_page,
        last_page,
        path,
        from,
        first_page_url,
        last_page_url,
        next_page_url,
        per_page,
        prev_page_url,
        to,
        links,
        total,
        data,
      ];
}
