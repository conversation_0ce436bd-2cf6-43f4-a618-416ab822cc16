import 'package:equatable/equatable.dart';
import 'package:json_annotation/json_annotation.dart';

part 'supervisor_bus_admin_models.g.dart';

@JsonSerializable()
class SupervisorBusAdminModels extends Equatable {
  final int? id;
  final String? name;
  final String? notes;
  final int? status;
  final int? school_id;
  final String? attendant_driver_id;
  final String? attendant_admins_id;
  final String? car_number;
  final String? created_at;
  final String? updated_at;

  const SupervisorBusAdminModels({
    this.id,
    this.created_at,
    this.school_id,
    this.name,
    this.status,
    this.attendant_driver_id,
    this.attendant_admins_id,
    this.updated_at,
    this.car_number,
    this.notes,
  });

  factory SupervisorBusAdminModels.fromJson(Map<String, dynamic> json) {
    return _$SupervisorBusAdminModelsFromJson(json);
  }

  Map<String, dynamic> toJson() => _$SupervisorBusAdminModelsToJson(this);

  @override
  List<Object?> get props => [
        id,
        created_at,
        school_id,
        name,
        status,
        attendant_driver_id,
        attendant_admins_id,
        updated_at,
        car_number,
        notes,
      ];
}
