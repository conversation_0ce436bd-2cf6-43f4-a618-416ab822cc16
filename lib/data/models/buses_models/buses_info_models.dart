import 'package:bus/data/models/student_models/student_a_d_models.dart';
import 'package:bus/data/models/student_models/student_models.dart';
import 'package:bus/data/models/supervisor_mdodels/supervisor_info_models.dart';
import 'package:equatable/equatable.dart';
import 'package:json_annotation/json_annotation.dart';

import '../auth_models/login_models/login_data_models.dart';

part 'buses_info_models.g.dart';

@JsonSerializable()
class BusesInfoModel extends Equatable {
  final int? id;
  final Admin? admin;
  final Driver? driver;

  final String? name;
  final String? notes;
  final int? status;
  final int? school_id;
  final int? attendant_driver_id;
  final int? attendant_admins_id;
  final String? car_number;
  final String? created_at;
  final String? updated_at;
  final LoginDataModels? schools;
  final StudentADModels? attendant_driver;
  final SupervisorInfoModels? supervisor;
  final StudentModels? students;

  const BusesInfoModel({
    this.students,
    this.admin,
    this.driver,

    this.supervisor,
    this.status,
    this.name,
    this.schools,
    this.updated_at,
    this.school_id,
    this.created_at,
    this.id,
    this.attendant_driver_id,
    this.attendant_driver,
    this.notes,
    this.attendant_admins_id,
    this.car_number,
  });

  factory BusesInfoModel.fromJson(Map<String, dynamic> json) {
    return _$BusesInfoModelFromJson(json);
  }

  Map<String, dynamic> toJson() => _$BusesInfoModelToJson(this);

  @override
  List<Object?> get props => [
        status,
        admin,
        driver,

        name,
        schools,
        updated_at,
        school_id,
        created_at,
        id,
        attendant_driver_id,
        attendant_driver,
        notes,
        attendant_admins_id,
        car_number,
      ];
}



@JsonSerializable()
class Admin extends Equatable {
  final int? id;
  final String? username;
  final String? name;

  const Admin({
    this.id,
    this.username,
    this.name,
  });

  factory Admin.fromJson(Map<String, dynamic> json) => _$AdminFromJson(json);

  Map<String, dynamic> toJson() => _$AdminToJson(this);

  @override
  List<Object?> get props => [id, username, name];
}


@JsonSerializable()
class Driver extends Equatable {
  final int? id;
  final String? username;
  final String? name;

  const Driver({
    this.id,
    this.username,
    this.name,
  });

  factory Driver.fromJson(Map<String, dynamic> json) => _$DriverFromJson(json);

  Map<String, dynamic> toJson() => _$DriverToJson(this);

  @override
  List<Object?> get props => [id, username, name];
}