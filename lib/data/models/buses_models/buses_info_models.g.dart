// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'buses_info_models.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

BusesInfoModel _$BusesInfoModelFromJson(Map<String, dynamic> json) =>
    BusesInfoModel(
      students: json['students'] == null
          ? null
          : StudentModels.fromJson(json['students'] as Map<String, dynamic>),
      admin: json['admin'] == null
          ? null
          : Admin.fromJson(json['admin'] as Map<String, dynamic>),
      driver: json['driver'] == null
          ? null
          : Driver.fromJson(json['driver'] as Map<String, dynamic>),
      supervisor: json['supervisor'] == null
          ? null
          : SupervisorInfoModels.fromJson(
              json['supervisor'] as Map<String, dynamic>),
      status: (json['status'] as num?)?.toInt(),
      name: json['name'] as String?,
      schools: json['schools'] == null
          ? null
          : LoginDataModels.fromJson(json['schools'] as Map<String, dynamic>),
      updated_at: json['updated_at'] as String?,
      school_id: (json['school_id'] as num?)?.toInt(),
      created_at: json['created_at'] as String?,
      id: (json['id'] as num?)?.toInt(),
      attendant_driver_id: (json['attendant_driver_id'] as num?)?.toInt(),
      attendant_driver: json['attendant_driver'] == null
          ? null
          : StudentADModels.fromJson(
              json['attendant_driver'] as Map<String, dynamic>),
      notes: json['notes'] as String?,
      attendant_admins_id: (json['attendant_admins_id'] as num?)?.toInt(),
      car_number: json['car_number'] as String?,
    );

Map<String, dynamic> _$BusesInfoModelToJson(BusesInfoModel instance) =>
    <String, dynamic>{
      'id': instance.id,
      'admin': instance.admin,
      'driver': instance.driver,
      'name': instance.name,
      'notes': instance.notes,
      'status': instance.status,
      'school_id': instance.school_id,
      'attendant_driver_id': instance.attendant_driver_id,
      'attendant_admins_id': instance.attendant_admins_id,
      'car_number': instance.car_number,
      'created_at': instance.created_at,
      'updated_at': instance.updated_at,
      'schools': instance.schools,
      'attendant_driver': instance.attendant_driver,
      'supervisor': instance.supervisor,
      'students': instance.students,
    };

Admin _$AdminFromJson(Map<String, dynamic> json) => Admin(
      id: (json['id'] as num?)?.toInt(),
      username: json['username'] as String?,
      name: json['name'] as String?,
    );

Map<String, dynamic> _$AdminToJson(Admin instance) => <String, dynamic>{
      'id': instance.id,
      'username': instance.username,
      'name': instance.name,
    };

Driver _$DriverFromJson(Map<String, dynamic> json) => Driver(
      id: (json['id'] as num?)?.toInt(),
      username: json['username'] as String?,
      name: json['name'] as String?,
    );

Map<String, dynamic> _$DriverToJson(Driver instance) => <String, dynamic>{
      'id': instance.id,
      'username': instance.username,
      'name': instance.name,
    };
