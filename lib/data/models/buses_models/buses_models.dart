import 'package:bus/data/models/buses_models/buses_data_models.dart';
import 'package:equatable/equatable.dart';
import 'package:json_annotation/json_annotation.dart';

part 'buses_models.g.dart';

@JsonSerializable()
class BusesModel extends Equatable {
  final bool? status;
  final String? message;
  final BusesDataModels? data;

  const BusesModel({
    this.status,
    this.message,
    this.data,
  });

  factory BusesModel.fromJson(Map<String, dynamic> json) {
    return _$BusesModelFromJson(json);
  }

  Map<String, dynamic> toJson() => _$BusesModelToJson(this);

  @override
  List<Object?> get props => [
        status,
        message,
        data,
      ];
}
