import 'package:equatable/equatable.dart';
import 'package:json_annotation/json_annotation.dart';

part 'delete_driver_supervisor_models.g.dart';

@JsonSerializable()
class DeleteDriverSupervisorModels extends Equatable {
  final String? data;
  final String? message;
  final bool? status;

  const DeleteDriverSupervisorModels({
    this.message,
    this.data,
    this.status,
  });

  factory DeleteDriverSupervisorModels.fromJson(Map<String, dynamic> json) {
    return _$DeleteDriverSupervisorModelsFromJson(json);
  }

  Map<String, dynamic> toJson() => _$DeleteDriverSupervisorModelsToJson(this);

  @override
  List<Object?> get props => [
        message,
        data,
        status,
      ];
}
