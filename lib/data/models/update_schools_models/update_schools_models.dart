import 'package:bus/data/models/user_models/user_models.dart';
import 'package:equatable/equatable.dart';
import 'package:json_annotation/json_annotation.dart';

import '../auth_models/reset_password_model.dart';

part 'update_schools_models.g.dart';

@JsonSerializable()
class UpdateSchoolsModels extends Equatable {
  final Status? status;
  final UserModel? data;

  const UpdateSchoolsModels({
    this.data,
    this.status,
  });

  factory UpdateSchoolsModels.fromJson(Map<String, dynamic> json) {
    return _$UpdateSchoolsModelsFromJson(json);
  }

  Map<String, dynamic> toJson() => _$UpdateSchoolsModelsToJson(this);

  @override
  List<Object?> get props => [
        data,
        status,
      ];
}
