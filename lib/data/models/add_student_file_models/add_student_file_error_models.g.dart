// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'add_student_file_error_models.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

AddStudentFileErrorModels _$AddStudentFileErrorModelsFromJson(
        Map<String, dynamic> json) =>
    AddStudentFileErrorModels(
      error: json['error'] as bool?,
      messages: json['messages'] as String?,
    );

Map<String, dynamic> _$AddStudentFileErrorModelsToJson(
        AddStudentFileErrorModels instance) =>
    <String, dynamic>{
      'error': instance.error,
      'messages': instance.messages,
    };

AddStudentFileMessageModels _$AddStudentFileMessageModelsFromJson(
        Map<String, dynamic> json) =>
    AddStudentFileMessageModels(
      classroom_id: json['classroom_id'] as String?,
      grade_id: json['grade_id'] as String?,
    );

Map<String, dynamic> _$AddStudentFileMessageModelsToJson(
        AddStudentFileMessageModels instance) =>
    <String, dynamic>{
      'grade_id': instance.grade_id,
      'classroom_id': instance.classroom_id,
    };
