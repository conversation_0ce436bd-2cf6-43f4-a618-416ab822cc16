import 'package:equatable/equatable.dart';
import 'package:json_annotation/json_annotation.dart';

part 'add_student_file_error_models.g.dart';

@JsonSerializable()
class AddStudentFileErrorModels extends Equatable {
  final bool? error;
  final String? messages;

  const AddStudentFileErrorModels({
    this.error,
    this.messages,
  });

  factory AddStudentFileErrorModels.fromJson(Map<String, dynamic> json) {
    return _$AddStudentFileErrorModelsFromJson(json);
  }

  Map<String, dynamic> toJson() => _$AddStudentFileErrorModelsToJson(this);

  @override
  List<Object?> get props => [
        error,
        messages,
      ];
}

@JsonSerializable()
class AddStudentFileMessageModels extends Equatable {
  final String? grade_id;
  final String? classroom_id;

  const AddStudentFileMessageModels({
    this.classroom_id,
    this.grade_id,
  });

  factory AddStudentFileMessageModels.fromJson(Map<String, dynamic> json) {
    return _$AddStudentFileMessageModelsFromJson(json);
  }

  Map<String, dynamic> toJson() => _$AddStudentFileMessageModelsToJson(this);

  @override
  List<Object?> get props => [
        classroom_id,
        grade_id,
      ];
}
