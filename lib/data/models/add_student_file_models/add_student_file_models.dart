import 'package:bus/data/models/add_student_file_models/add_student_file_error_models.dart';
import 'package:equatable/equatable.dart';
import 'package:json_annotation/json_annotation.dart';

part 'add_student_file_models.g.dart';

@JsonSerializable()
class AddStudentFileModels extends Equatable {
  final String? data;
  final String? message;
  final bool? status;
  final AddStudentFileErrorModels? errors;

  const AddStudentFileModels({
    this.data,
    this.errors,
    this.status,
    this.message,
  });

  factory AddStudentFileModels.fromJson(Map<String, dynamic> json) {
    return _$AddStudentFileModelsFromJson(json);
  }

  Map<String, dynamic> toJson() => _$AddStudentFileModelsToJson(this);

  @override
  List<Object?> get props => [
        data,
        errors,
        status,
        message,
      ];
}
