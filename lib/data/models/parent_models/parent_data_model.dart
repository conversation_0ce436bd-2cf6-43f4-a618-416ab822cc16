import 'dart:convert';

class ParentDataModel {
  final Data? data;
  final String? message;
  final bool? status;

  ParentDataModel({
    this.data,
    this.message,
    this.status,
  });

  ParentDataModel copyWith({
    Data? data,
    String? message,
    bool? status,
  }) =>
      ParentDataModel(
        data: data ?? this.data,
        message: message ?? this.message,
        status: status ?? this.status,
      );

  factory ParentDataModel.fromJson(String str) => ParentDataModel.fromMap(json.decode(str));

  String toJson() => json.encode(toMap());

  factory ParentDataModel.fromMap(Map<String, dynamic> json) => ParentDataModel(
    data: json["data"] == null ? null : Data.fromMap(json["data"]),
    message: json["message"],
    status: json["status"],
  );

  Map<String, dynamic> toMap() => {
    "data": data?.toMap(),
    "message": message,
    "status": status,
  };
}

class Data {
  final Parent? parent;
  final List<Child>? children;

  Data({
    this.parent,
    this.children,
  });

  Data copyWith({
    Parent? parent,
    List<Child>? children,
  }) =>
      Data(
        parent: parent ?? this.parent,
        children: children ?? this.children,
      );

  factory Data.fromJson(String str) => Data.fromMap(json.decode(str));

  String toJson() => json.encode(toMap());

  factory Data.fromMap(Map<String, dynamic> json) => Data(
    parent: json["parent"] == null ? null : Parent.fromMap(json["parent"]),
    children: json["children"] == null ? [] : List<Child>.from(json["children"]!.map((x) => Child.fromMap(x))),
  );

  Map<String, dynamic> toMap() => {
    "parent": parent?.toMap(),
    "children": children == null ? [] : List<dynamic>.from(children!.map((x) => x.toMap())),
  };
}

class Child {
  final String? id;
  final String? name;
  final String? phone;
  final int? gradeId;
  final int? genderId;
  final int? schoolId;
  final int? religionId;
  final int? typeBloodId;
  final int? classroomId;
  final int? busId;
  final String? address;
  final String? cityName;
  final int? status;
  final String? tripType;
  final String? parentKey;
  final String? parentSecret;
  final DateTime? dateBirth;
  final String? logo;
  final String? latitude;
  final String? longitude;
  final DateTime? createdAt;
  final DateTime? updatedAt;
  final String? logoPath;
  final Pivot? pivot;
  final Grade? grade;

  Child({
    this.id,
    this.name,
    this.phone,
    this.gradeId,
    this.genderId,
    this.schoolId,
    this.religionId,
    this.typeBloodId,
    this.classroomId,
    this.busId,
    this.address,
    this.cityName,
    this.status,
    this.tripType,
    this.parentKey,
    this.parentSecret,
    this.dateBirth,
    this.logo,
    this.latitude,
    this.longitude,
    this.createdAt,
    this.updatedAt,
    this.logoPath,
    this.pivot,
    this.grade,
  });

  Child copyWith({
    String? id,
    String? name,
    String? phone,
    int? gradeId,
    int? genderId,
    int? schoolId,
    int? religionId,
    int? typeBloodId,
    int? classroomId,
    int? busId,
    String? address,
    String? cityName,
    int? status,
    String? tripType,
    String? parentKey,
    String? parentSecret,
    DateTime? dateBirth,
    String? logo,
    String? latitude,
    String? longitude,
    DateTime? createdAt,
    DateTime? updatedAt,
    String? logoPath,
    Pivot? pivot,
    Grade? grade,
  }) =>
      Child(
        id: id ?? this.id,
        name: name ?? this.name,
        phone: phone ?? this.phone,
        gradeId: gradeId ?? this.gradeId,
        genderId: genderId ?? this.genderId,
        schoolId: schoolId ?? this.schoolId,
        religionId: religionId ?? this.religionId,
        typeBloodId: typeBloodId ?? this.typeBloodId,
        classroomId: classroomId ?? this.classroomId,
        busId: busId ?? this.busId,
        address: address ?? this.address,
        cityName: cityName ?? this.cityName,
        status: status ?? this.status,
        tripType: tripType ?? this.tripType,
        parentKey: parentKey ?? this.parentKey,
        parentSecret: parentSecret ?? this.parentSecret,
        dateBirth: dateBirth ?? this.dateBirth,
        logo: logo ?? this.logo,
        latitude: latitude ?? this.latitude,
        longitude: longitude ?? this.longitude,
        createdAt: createdAt ?? this.createdAt,
        updatedAt: updatedAt ?? this.updatedAt,
        logoPath: logoPath ?? this.logoPath,
        pivot: pivot ?? this.pivot,
        grade: grade ?? this.grade,
      );

  factory Child.fromJson(String str) => Child.fromMap(json.decode(str));

  String toJson() => json.encode(toMap());

  factory Child.fromMap(Map<String, dynamic> json) => Child(
    id: json["id"],
    name: json["name"],
    phone: json["phone"],
    gradeId: json["grade_id"],
    genderId: json["gender_id"],
    schoolId: json["school_id"],
    religionId: json["religion_id"],
    typeBloodId: json["type__blood_id"],
    classroomId: json["classroom_id"],
    busId: json["bus_id"],
    address: json["address"],
    cityName: json["city_name"],
    status: json["status"],
    tripType: json["trip_type"],
    parentKey: json["parent_key"],
    parentSecret: json["parent_secret"],
    dateBirth: json["Date_Birth"] == null ? null : DateTime.parse(json["Date_Birth"]),
    logo: json["logo"],
    latitude: json["latitude"],
    longitude: json["longitude"],
    createdAt: json["created_at"] == null ? null : DateTime.parse(json["created_at"]),
    updatedAt: json["updated_at"] == null ? null : DateTime.parse(json["updated_at"]),
    logoPath: json["logo_path"],
    pivot: json["pivot"] == null ? null : Pivot.fromMap(json["pivot"]),
    grade: json["grade"] == null ? null : Grade.fromMap(json["grade"]),
  );

  Map<String, dynamic> toMap() => {
    "id": id,
    "name": name,
    "phone": phone,
    "grade_id": gradeId,
    "gender_id": genderId,
    "school_id": schoolId,
    "religion_id": religionId,
    "type__blood_id": typeBloodId,
    "classroom_id": classroomId,
    "bus_id": busId,
    "address": address,
    "city_name": cityName,
    "status": status,
    "trip_type": tripType,
    "parent_key": parentKey,
    "parent_secret": parentSecret,
    "Date_Birth": "${dateBirth!.year.toString().padLeft(4, '0')}-${dateBirth!.month.toString().padLeft(2, '0')}-${dateBirth!.day.toString().padLeft(2, '0')}",
    "logo": logo,
    "latitude": latitude,
    "longitude": longitude,
    "created_at": createdAt?.toIso8601String(),
    "updated_at": updatedAt?.toIso8601String(),
    "logo_path": logoPath,
    "pivot": pivot?.toMap(),
    "grade": grade?.toMap(),
  };
}

class Grade {
  final int? id;
  final String? name;
  final dynamic notes;
  final int? status;
  final DateTime? createdAt;
  final DateTime? updatedAt;
  final String? defaultClassrooms;

  Grade({
    this.id,
    this.name,
    this.notes,
    this.status,
    this.createdAt,
    this.updatedAt,
    this.defaultClassrooms,
  });

  Grade copyWith({
    int? id,
    String? name,
    dynamic notes,
    int? status,
    DateTime? createdAt,
    DateTime? updatedAt,
    String? defaultClassrooms,
  }) =>
      Grade(
        id: id ?? this.id,
        name: name ?? this.name,
        notes: notes ?? this.notes,
        status: status ?? this.status,
        createdAt: createdAt ?? this.createdAt,
        updatedAt: updatedAt ?? this.updatedAt,
        defaultClassrooms: defaultClassrooms ?? this.defaultClassrooms,
      );

  factory Grade.fromJson(String str) => Grade.fromMap(json.decode(str));

  String toJson() => json.encode(toMap());

  factory Grade.fromMap(Map<String, dynamic> json) => Grade(
    id: json["id"],
    name: json["name"],
    notes: json["notes"],
    status: json["status"],
    createdAt: json["created_at"] == null ? null : DateTime.parse(json["created_at"]),
    updatedAt: json["updated_at"] == null ? null : DateTime.parse(json["updated_at"]),
    defaultClassrooms: json["default_classrooms"],
  );

  Map<String, dynamic> toMap() => {
    "id": id,
    "name": name,
    "notes": notes,
    "status": status,
    "created_at": createdAt?.toIso8601String(),
    "updated_at": updatedAt?.toIso8601String(),
    "default_classrooms": defaultClassrooms,
  };
}

class Pivot {
  final int? myParentId;
  final String? studentId;

  Pivot({
    this.myParentId,
    this.studentId,
  });

  Pivot copyWith({
    int? myParentId,
    String? studentId,
  }) =>
      Pivot(
        myParentId: myParentId ?? this.myParentId,
        studentId: studentId ?? this.studentId,
      );

  factory Pivot.fromJson(String str) => Pivot.fromMap(json.decode(str));

  String toJson() => json.encode(toMap());

  factory Pivot.fromMap(Map<String, dynamic> json) => Pivot(
    myParentId: json["my__parent_id"],
    studentId: json["student_id"],
  );

  Map<String, dynamic> toMap() => {
    "my__parent_id": myParentId,
    "student_id": studentId,
  };
}

class Parent {
  final int? id;
  final String? name;
  final String? email;
  final String? phone;
  final String? emailVerifiedAt;
  final String? address;
  final int? status;
  final String? logo;
  final String? typeAuth;
  final DateTime? createdAt;
  final DateTime? updatedAt;
  final String? logoPath;

  Parent({
    this.id,
    this.name,
    this.email,
    this.phone,
    this.emailVerifiedAt,
    this.address,
    this.status,
    this.logo,
    this.typeAuth,
    this.createdAt,
    this.updatedAt,
    this.logoPath,
  });

  Parent copyWith({
    int? id,
    String? name,
    String? email,
    String? phone,
    String? emailVerifiedAt,
    String? address,
    int? status,
    String? logo,
    String? typeAuth,
    DateTime? createdAt,
    DateTime? updatedAt,
    String? logoPath,
  }) =>
      Parent(
        id: id ?? this.id,
        name: name ?? this.name,
        email: email ?? this.email,
        phone: phone ?? this.phone,
        emailVerifiedAt: emailVerifiedAt ?? this.emailVerifiedAt,
        address: address ?? this.address,
        status: status ?? this.status,
        logo: logo ?? this.logo,
        typeAuth: typeAuth ?? this.typeAuth,
        createdAt: createdAt ?? this.createdAt,
        updatedAt: updatedAt ?? this.updatedAt,
        logoPath: logoPath ?? this.logoPath,
      );

  factory Parent.fromJson(String str) => Parent.fromMap(json.decode(str));

  String toJson() => json.encode(toMap());

  factory Parent.fromMap(Map<String, dynamic> json) => Parent(
    id: json["id"],
    name: json["name"],
    email: json["email"],
    phone: json["phone"],
    emailVerifiedAt: json["email_verified_at"],
    address: json["address"],
    status: json["status"],
    logo: json["logo"],
    typeAuth: json["typeAuth"],
    createdAt: json["created_at"] == null ? null : DateTime.parse(json["created_at"]),
    updatedAt: json["updated_at"] == null ? null : DateTime.parse(json["updated_at"]),
    logoPath: json["logo_path"],
  );

  Map<String, dynamic> toMap() => {
    "id": id,
    "name": name,
    "email": email,
    "phone": phone,
    "email_verified_at": emailVerifiedAt,
    "address": address,
    "status": status,
    "logo": logo,
    "typeAuth": typeAuth,
    "created_at": createdAt?.toIso8601String(),
    "updated_at": updatedAt?.toIso8601String(),
    "logo_path": logoPath,
  };
}
