// import 'dart:convert';
//
// class ParentModel {
//   int? id;
//   String? name;
//   String? email;
//   String? phone;
//
//   String? address;
//   int? status;
//
//   String? logo_path;
//   ParentModel({
//     this.id,
//     this.name,
//     this.email,
//     this.phone,
//     this.address,
//     this.status,
//     this.logo_path,
//   });
//
//   ParentModel copyWith({
//     int? id,
//     String? name,
//     String? email,
//     String? phone,
//     String? address,
//     int? status,
//     String? logo_path,
//   }) {
//     return ParentModel(
//       id: id ?? this.id,
//       name: name ?? this.name,
//       email: email ?? this.email,
//       phone: phone ?? this.phone,
//       address: address ?? this.address,
//       status: status ?? this.status,
//       logo_path: logo_path ?? this.logo_path,
//     );
//   }
//
//   Map<String, dynamic> toMap() {
//     return {
//       'id': id,
//       'name': name,
//       'email': email,
//       'phone': phone,
//       'address': address,
//       'status': status,
//       'logo_path': logo_path,
//     };
//   }
//
//   factory ParentModel.fromMap(Map<String, dynamic> map) {
//     return ParentModel(
//       id: map['id']?.toInt(),
//       name: map['name'],
//       email: map['email'],
//       phone: map['phone'],
//       address: map['address'],
//       status: map['status'],
//       logo_path: map['logo_path'],
//     );
//   }
//
//   String toJson() => json.encode(toMap());
//
//   factory ParentModel.fromJson(String source) =>
//       ParentModel.fromMap(json.decode(source));
//
//   @override
//   String toString() {
//     return 'ParentModel(id: $id, name: $name, email: $email, phone: $phone, address: $address, status: $status, logo_path: $logo_path)';
//   }
//
//   @override
//   bool operator ==(Object other) {
//     if (identical(this, other)) return true;
//
//     return other is ParentModel &&
//         other.id == id &&
//         other.name == name &&
//         other.email == email &&
//         other.phone == phone &&
//         other.address == address &&
//         other.status == status &&
//         other.logo_path == logo_path;
//   }
//
//   @override
//   int get hashCode {
//     return id.hashCode ^
//         name.hashCode ^
//         email.hashCode ^
//         phone.hashCode ^
//         address.hashCode ^
//         status.hashCode ^
//         logo_path.hashCode;
//   }
// }

class ParentModels {
  Data? data;
  String? message;
  bool? status;

  ParentModels({this.data, this.message, this.status});

  ParentModels.fromJson(Map<String, dynamic> json) {
    data = json['data'] != null ? new Data.fromJson(json['data']) : null;
    message = json['message'];
    status = json['status'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    if (this.data != null) {
      data['data'] = this.data!.toJson();
    }
    data['message'] = this.message;
    data['status'] = this.status;
    return data;
  }
}

class Data {
  int? currentPage;
  List<DataInfo>? data;
  String? firstPageUrl;
  int? from;
  int? lastPage;
  String? lastPageUrl;
  List<Links>? links;
  String? nextPageUrl;
  String? path;
  int? perPage;
  String? prevPageUrl;
  int? to;
  int? total;

  Data(
      {this.currentPage,
      this.data,
      this.firstPageUrl,
      this.from,
      this.lastPage,
      this.lastPageUrl,
      this.links,
      this.nextPageUrl,
      this.path,
      this.perPage,
      this.prevPageUrl,
      this.to,
      this.total});

  Data.fromJson(Map<String, dynamic> json) {
    currentPage = json['current_page'];
    if (json['data'] != null) {
      data = <DataInfo>[];
      json['data'].forEach((v) {
        data!.add(new DataInfo.fromJson(v));
      });
    }
    firstPageUrl = json['first_page_url'];
    from = json['from'];
    lastPage = json['last_page'];
    lastPageUrl = json['last_page_url'];
    if (json['links'] != null) {
      links = <Links>[];
      json['links'].forEach((v) {
        links!.add(new Links.fromJson(v));
      });
    }
    nextPageUrl = json['next_page_url'];
    path = json['path'];
    perPage = json['per_page'];
    prevPageUrl = json['prev_page_url'];
    to = json['to'];
    total = json['total'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['current_page'] = this.currentPage;
    if (this.data != null) {
      data['data'] = this.data!.map((v) => v.toJson()).toList();
    }
    data['first_page_url'] = this.firstPageUrl;
    data['from'] = this.from;
    data['last_page'] = this.lastPage;
    data['last_page_url'] = this.lastPageUrl;
    if (this.links != null) {
      data['links'] = this.links!.map((v) => v.toJson()).toList();
    }
    data['next_page_url'] = this.nextPageUrl;
    data['path'] = this.path;
    data['per_page'] = this.perPage;
    data['prev_page_url'] = this.prevPageUrl;
    data['to'] = this.to;
    data['total'] = this.total;
    return data;
  }
}

class DataInfo {
  int? id;
  String? name;
  String? email;
  String? phone;
  String? emailVerifiedAt;
  String? address;
  int? status;
  String? logo;
  String? typeAuth;
  String? firebaseToken;
  String? createdAt;
  String? updatedAt;
  String? logoPath;
  bool? subscriptionStatus;

  DataInfo(
      {this.id,
      this.name,
      this.email,
      this.phone,
      this.emailVerifiedAt,
      this.address,
      this.status,
      this.logo,
      this.typeAuth,
      this.firebaseToken,
      this.createdAt,
      this.updatedAt,
      this.logoPath,
      this.subscriptionStatus});

  DataInfo.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    name = json['name'];
    email = json['email'];
    phone = json['phone'];
    emailVerifiedAt = json['email_verified_at'];
    address = json['address'];
    status = json['status'];
    logo = json['logo'];
    typeAuth = json['typeAuth'];
    firebaseToken = json['firebase_token'];
    createdAt = json['created_at'];
    updatedAt = json['updated_at'];
    logoPath = json['logo_path'];
    subscriptionStatus = json['subscription_status'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['id'] = this.id;
    data['name'] = this.name;
    data['email'] = this.email;
    data['phone'] = this.phone;
    data['email_verified_at'] = this.emailVerifiedAt;
    data['address'] = this.address;
    data['status'] = this.status;
    data['logo'] = this.logo;
    data['typeAuth'] = this.typeAuth;
    data['firebase_token'] = this.firebaseToken;
    data['created_at'] = this.createdAt;
    data['updated_at'] = this.updatedAt;
    data['logo_path'] = this.logoPath;
    data['subscription_status'] = this.subscriptionStatus;
    return data;
  }
}

class Links {
  String? url;
  String? label;
  bool? active;

  Links({this.url, this.label, this.active});

  Links.fromJson(Map<String, dynamic> json) {
    url = json['url'];
    label = json['label'];
    active = json['active'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['url'] = this.url;
    data['label'] = this.label;
    data['active'] = this.active;
    return data;
  }
}
