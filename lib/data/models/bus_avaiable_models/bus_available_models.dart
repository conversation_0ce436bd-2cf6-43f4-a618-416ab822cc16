import 'package:bus/data/models/bus_avaiable_models/bus_available_data_models.dart';
import 'package:equatable/equatable.dart';
import 'package:json_annotation/json_annotation.dart';

part 'bus_available_models.g.dart';

@JsonSerializable()
class BusAvailableModels extends Equatable {
  final bool? status;
  final String? message;
  final List<BusAvailableDataModels>? data;

  const BusAvailableModels({
    this.message,
    this.status,
    this.data,
  });

  factory BusAvailableModels.fromJson(Map<String, dynamic> json) {
    return _$BusAvailableModelsFromJson(json);
  }

  Map<String, dynamic> toJson() => _$BusAvailableModelsToJson(this);

  @override
  List<Object?> get props => [
        message,
        status,
        data,
      ];
}
