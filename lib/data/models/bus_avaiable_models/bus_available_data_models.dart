import 'package:equatable/equatable.dart';
import 'package:json_annotation/json_annotation.dart';

part 'bus_available_data_models.g.dart';

@JsonSerializable()
class BusAvailableDataModels extends Equatable {
  final int? id;
  final String? name;

  const BusAvailableDataModels({
    this.id,
    this.name,
  });

  factory BusAvailableDataModels.fromJson(Map<String, dynamic> json) {
    return _$BusAvailableDataModelsFromJson(json);
  }

  Map<String, dynamic> toJson() => _$BusAvailableDataModelsToJson(this);

  @override
  List<Object?> get props => [
        id,
        name,
      ];
}
