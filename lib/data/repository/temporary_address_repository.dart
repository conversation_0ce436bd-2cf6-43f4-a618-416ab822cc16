import 'package:bus/config/config_base.dart';
import 'package:bus/data/models/temporary_address_models/temporary_address_models.dart';
import 'package:bus/helper/network_serviecs.dart';
import 'package:flutter/material.dart';

class TemporaryAddressRepository {
  static final _dio = NetworkService();

  static Future<TemporaryAddressModels> getTemporaryAddresses({
    String? status,
    int page = 1,
  }) async {
    try {
      // تحضير المعلمات للطلب
      Map<String, dynamic> queryParameters = {
        'page': page,
      };

      // إضافة حالة القبول إذا كانت موجودة
      if (status != null && status.isNotEmpty) {
        queryParameters['accept_status'] = _mapStatusToApiStatus(status);
      }

      // إضافة معرف المدرسة (school_id)
      // نستخدم معرف المدرسة من الملف الشخصي للمستخدم
      await _addSchoolIdToParams(queryParameters);

      // إرسال الطلب إلى الـ API
      final response = await _dio.get(
        url: ConfigBase.temporaryAddresses,
        queryParameters: queryParameters,
        isAuth: true,
      );

      // تحويل البيانات المستلمة إلى نموذج
      return TemporaryAddressModels.fromJson(response.data);
    } catch (e) {
      debugPrint("Error fetching temporary addresses: $e");
      // إرجاع نموذج فارغ في حالة حدوث خطأ
      return const TemporaryAddressModels(
        status: false,
        data: null,
      );
    }
  }

  /// استجابة لطلب العنوان المؤقت (قبول أو رفض)
  ///
  /// [temporaryAddressId] معرف طلب العنوان المؤقت
  /// [acceptStatus] حالة القبول: 1 للقبول، 2 للرفض
  static Future<Map<String, dynamic>> respondToTemporaryAddressRequest({
    required int temporaryAddressId,
    required int acceptStatus,
  }) async {
    try {
      // تحضير بيانات الطلب
      Map<String, dynamic> data = {
        'accept_status': acceptStatus,
      };

      // إرسال الطلب إلى الـ API باستخدام PATCH
      final response = await _dio.patch(
        url: "temporary-addresses/respond-to-request/$temporaryAddressId",
        body: data,
        isAuth: true,
      );

      // التحقق من نجاح الطلب
      if (response.statusCode == 200) {
        return {
          'status': true,
          'message': response.data['message'] ??
                    (acceptStatus == 1 ? 'تم قبول الطلب بنجاح' : 'تم رفض الطلب بنجاح'),
          'data': response.data
        };
      } else {
        return {
          'status': false,
          'message': response.data['message'] ?? 'حدث خطأ أثناء معالجة الطلب',
          'data': response.data
        };
      }
    } catch (e) {
      debugPrint("Error responding to temporary address request: $e");
      return {
        'status': false,
        'message': 'حدث خطأ أثناء الاتصال بالخادم',
        'error': e.toString()
      };
    }
  }

  // تحويل حالة الطلب من التطبيق إلى حالة API
  static String _mapStatusToApiStatus(String status) {
    switch (status) {
      case 'new':
        return '0'; // Processing
      case 'accept':
        return '1'; // Approved
      case 'unaccept':
        return '2'; // Rejected
      default:
        return '';
    }
  }

  // إضافة معرف المدرسة إلى المعلمات
  static Future<void> _addSchoolIdToParams(Map<String, dynamic> params) async {
    try {
      // الحصول على معلومات المستخدم من الملف الشخصي
      final response = await _dio.get(
        url: ConfigBase.profile,
        isAuth: true,
      );

      if (response.statusCode == 200 && response.data != null) {
        // إضافة معرف المدرسة إلى المعلمات
        if (response.data['id'] != null) {
          params['school_id'] = response.data['id'].toString();
        }
      }
    } catch (e) {
      debugPrint("Error getting school_id: $e");
      // في حالة حدوث خطأ، نستمر بدون إضافة معرف المدرسة
    }
  }
}
