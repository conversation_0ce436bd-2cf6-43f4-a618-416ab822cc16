import 'package:bus/bloc/absences_cubit/absences_states.dart';
import 'package:bus/config/config_base.dart';
import 'package:bus/helper/network_serviecs.dart';
import 'package:flutter/material.dart';

class AbsencesRepo {
  final _dio = NetworkService();

  Future<List<dynamic>> repo({
    int? pageNumber,
  }) async {
    try {
      final _request = await _dio.get(
        url: "${ConfigBase.absences}?page=$pageNumber",
        isAuth: true,
      );
      // debugPrint(_request.data);
      //Logger().wtf(_request.data);
      // debugPrint("student from repo ${_request.statusCode}");
      List<AbsencesModel> absencesModels = [];
      if (_request.statusCode == 200 &&
          (_request.data["data"]["absences"]["data"] as List<dynamic>)
              .isNotEmpty) {
        for (var element
            in (_request.data["data"]["absences"]["data"] as List<dynamic>)) {
          absencesModels.add(AbsencesModel.fromMap(element));
        }
        debugPrint("Good");
        debugPrint(absencesModels.length.toString());
        return [
          absencesModels,
          "",
          _request.data["data"]["absences"]["current_page"],
          _request.data["data"]["absences"]["last_page"],
          true
        ];
      } else {
        debugPrint("no data");
        return [
          absencesModels,
          "error",
          _request.data["data"]["absences"]["current_page"],
          _request.data["data"]["absences"]["last_page"],
          false
        ];
      }
    } catch (e, s) {
      debugPrint("catch error $s");
      debugPrint("catch error at repo $e");
      return [[], e.toString(), 1, 1, false];
    }
  }

  Future<List<dynamic>> getAbsencesWithFiltter({
    String? busid,
    int? pageNumber,
    String? attendType,
    String? studentName,
    String? date,
  }) async {
    try {
      "absences/index?&text=$studentName";
      bool isStarted = false;
      String url = ConfigBase.absences + "?";
      if (attendType != "") {
        url += "attendence_type=$attendType";
        isStarted = true;
      }
      if (studentName != "") {
        url += "${isStarted ? "&" : ""}text=$studentName";
        isStarted = true;
      }
      if (date != "") {
        url += "${isStarted ? "&" : ""}attendence_date=$date";
        isStarted = true;
      }
      if (pageNumber != null) {
        url += "${isStarted ? "&" : ""}page=$pageNumber";
        isStarted = true;
      }
      if (busid != null && busid != "") {
        debugPrint(busid);
        url += "${isStarted ? "&" : ""}bus_id=$busid";
        isStarted = true;
      }
      debugPrint(url);
      final _request = await _dio.get(
        url: url,
        isAuth: true,
      );

      // Logger().wtf(_request.data);

      List<AbsencesModel> absencesModels = [];
      if (_request.statusCode == 200 &&
          (_request.data["data"]["absences"]["data"] as List<dynamic>)
              .isNotEmpty) {
        for (var element
            in (_request.data["data"]["absences"]["data"] as List<dynamic>)) {
          absencesModels.add(AbsencesModel.fromMap(element));
        }

        return [
          absencesModels,
          "",
          _request.data["data"]["absences"]["current_page"],
          _request.data["data"]["absences"]["last_page"],
          true
        ];
      } else {
        debugPrint("no data");
        return [
          absencesModels,
          "error",
          _request.data["data"]["absences"]["current_page"],
          _request.data["data"]["absences"]["last_page"],
          false
        ];
      }
    } catch (e, s) {
      debugPrint("catch error $s");
      debugPrint("catch error at repo $e");
      return [[], e.toString(), 1, 1, false];
    }
  }
}
