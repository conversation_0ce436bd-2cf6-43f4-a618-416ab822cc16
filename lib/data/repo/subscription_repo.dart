import 'dart:io';

import 'package:flutter/foundation.dart';
import '../../config/config_base.dart';
import '../../helper/network_serviecs.dart';
import '../models/subscription/coupon_model.dart';

class SubscriptionsRepo {
  final _dio = NetworkService();

  Future<CouponModel> couponSubscribe({
    required String code,
  }) async {
    try {
      final request = await _dio.post(
        url: '${ConfigBase.coupon}/$code',
        isAuth: true,
      );
      CouponModel? couponModel;
      if (request.statusCode == 200) {
        couponModel = CouponModel.fromMap(request.data);
      } else {
        couponModel = CouponModel.fromMap(request.data);
      }
      return couponModel;
    } on Exception catch (e, stackTrace) {
      debugPrint("catch error at Absence repo: $e");
      debugPrint(stackTrace.toString());
      debugPrint("error $e");
      return CouponModel(message: e.toString());
    }
  }

  Future<VerifyPurchaseResponse> verifyPurchase(String purchaseId) async {
    try {
      final response = await _dio.post(
        url: '${ConfigBase.baseUrl}/verify-purchase', 
        body: {
          'purchase_id': purchaseId,
          'platform': Platform.isIOS ? 'ios' : 'android'
        },
        isAuth: true,
      );
      
      if (response.statusCode == 200) {
        return VerifyPurchaseResponse.fromJson(response.data);
      } else {
        throw Exception('Verification failed: ${response.data['message']}');
      }
    } catch (e) {
      debugPrint('Verification error: $e');
      throw Exception('Failed to verify purchase: $e');
    }
  }
}

// Define the VerifyPurchaseResponse model if not already defined
class VerifyPurchaseResponse {
  final bool status;
  final String message;

  VerifyPurchaseResponse({required this.status, required this.message});

  factory VerifyPurchaseResponse.fromJson(Map<String, dynamic> json) {
    return VerifyPurchaseResponse(
      status: json['status'],
      message: json['message'],
    );
  }
}
