import 'package:bus/config/config_base.dart';
import 'package:bus/helper/network_serviecs.dart';
import 'package:flutter/material.dart';

class DeleteBusRepo {
  final _dio = NetworkService();

  Future<Map<String, dynamic>> repo({
    int? id,
  }) async {
    // debugPrint("id $id ");
    try {
      final _request = await _dio.delete(
        url: "${ConfigBase.deleteBus}$id",
        isAuth: true,
        body: {},
      );
      // Logger().wtf(_request.data);
      // debugPrint("===================== +++++++++++ ${_request.data} ++++++++++++ =========");
      Map<String, dynamic>? deleteStudentMap;
      if (_request.data['errors'] == false) {
        deleteStudentMap = <String, dynamic>{
          "errors": false,
          "message": _request.data['message'],
        };
      } else {
        deleteStudentMap = <String, dynamic>{
          "errors": true,
          "message": _request.data['message'],
        };
      }
      return deleteStudentMap;
    } catch (e, s) {
      // Logger().wtf(s);
      debugPrint("catch error $s");
      debugPrint("catch error $e");
      return {
        "errors": true,
        "message": e.toString(),
      };
    }
  }
}
