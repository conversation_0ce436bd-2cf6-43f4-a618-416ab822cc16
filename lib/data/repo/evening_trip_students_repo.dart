import 'package:bus/data/models/trip_models/evening_trip_students_model.dart';
import 'package:bus/helper/network_serviecs.dart';
import 'package:flutter/material.dart';

class EveningTripStudentsRepo {
  final _dio = NetworkService();

  /// Fetches students present on the bus for an evening trip
  /// 
  /// [tripId] is the ID of the trip to get students for
  Future<EveningTripStudentsResponse> getPresentStudents({
    required String tripId,
  }) async {
    try {
      final request = await _dio.get(
        url: "trips/evening/onbus/$tripId",
        isAuth: true,
      );

      debugPrint("Evening trip students API response: ${request.statusCode}");

      if (request.statusCode == 200) {
        return EveningTripStudentsResponse.fromJson(request.data);
      } else {
        // Handle non-200 status codes
        return EveningTripStudentsResponse(
          errors: true,
          message: request.data['message'] ?? "Failed to fetch students",
          presentOnBus: [],
        );
      }
    } catch (e, s) {
      // Log the error for debugging
      debugPrint("Error fetching evening trip students: $e");
      debugPrint("Stack trace: $s");

      // Return error response
      return EveningTripStudentsResponse(
        errors: true,
        message: "Failed to fetch students: $e",
        presentOnBus: [],
      );
    }
  }
}
