import 'dart:developer';
import 'dart:io';
import 'package:bus/config/config_base.dart';
import 'package:bus/data/models/add_driver_models/add_driver_models.dart';
import 'package:bus/helper/network_serviecs.dart';
import 'package:dio/dio.dart';
import 'package:flutter/material.dart';
import 'package:logger/logger.dart';

class AddDriverRepo {
  final _dio = NetworkService();

  Future<AddDriverModels> repo({
    String? name,
    MultipartFile? file__lol,
    String? fileName,
    String? username,
    int? genderId,
    int? religionId,
    int? typeBloodId,
    int? busId,
    String? birthDate,
    String? joiningDate,
    String? password,
    String? password_confirmation,
    String? address,
    String? cityName,
    String? phone,
    File? image,
  }) async {
    try {
      fileName = image!.path.split('/').last;
      file__lol = await MultipartFile.fromFile(image.path, filename: fileName);
    } catch (e, s) {
      debugPrint("catch error $s");
      debugPrint(e.toString());
    }
    Map<String, dynamic> datamap = {
      "name": name,
      "username": username,
      "Joining_Date": joiningDate,
      "birth_date": birthDate,
      "gender_id": genderId == 0 ? null : genderId,
      "religion_id": religionId == 0 ? null : religionId,
      "type__blood_id": typeBloodId == 0 ? null : typeBloodId,
      "bus_id": busId == 0 ? null : busId,
      "address": address,
      "city_name": cityName,
      "phone": phone,
    };
    if (file__lol != null) {
      datamap.addAll({"logo": file__lol});
    }

    datamap.addAll({
      "password_confirmation": password_confirmation,
      "password": password,
    });
    log("datamap:${datamap.toString()}  ");
    FormData formData = FormData.fromMap(datamap);

    try {
      final _request = await _dio.post(
        url: "${ConfigBase.baseUrl}attendants/store/driver",
        body: formData,
        isAuth: true,
      );
      // debugPrint(_request.data);
      Logger().wtf(_request.data);
      // debugPrint("student from repo ${_request.statusCode}");
      AddDriverModels? addDriverModels;
      if (_request.statusCode == 200) {
        addDriverModels = AddDriverModels.fromJson(_request.data);
      } else {
        addDriverModels = AddDriverModels(messages: _request.data['messages']['username'][0]);
      }
      return addDriverModels;
    } catch (e, s) {
      debugPrint("catch error $s");
      debugPrint("catch error $e");
      return AddDriverModels(message: e.toString());
    }
  }
}
