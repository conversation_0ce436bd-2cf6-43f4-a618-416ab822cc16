import 'package:bus/config/config_base.dart';
import 'package:bus/config/global_variable.dart';
import 'package:bus/helper/data_state.dart';
import 'package:bus/helper/network_serviecs.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/cupertino.dart';
import 'package:logger/logger.dart';

import '../../models/auth_models/register_models/register_models.dart';

class RegisterRepo {
  final _dio = NetworkService();

  Future<DataState<RegisterModels>> repo({
    String? name,
    String? email,
    String? password,
    String? confirmedPassword,
    String? phone,
    String? address,
    String? latitude,
    String? longitude,
    BuildContext? context,
  }) async {
    String urls = context!.locale.toString() == "ar"
        ? "${ConfigBase.register}/default_classrooms_ar"
        : ConfigBase.register;
    final request = await _dio.post(url: urls, body: {
      "name": name,
      "password": password,
      "email": email,
      "confirmed": confirmedPassword,
      "phone": phone,
      "address": address,
      "latitude": latitude,
      "longitude": longitude,
      "firebase_token": fCMToken,

    });
    Logger().w(request.data);
    debugPrint("Request done");
    if (request.statusMessage == "Created") {
      return DataSuccess(
        RegisterModels.fromJson(request.data),
      );
    } else {
      String? data = (request.data?['messages'] as Map<String, dynamic>?)
          ?.values
          .toString();
      debugPrint("response data ::$data");
      return DataFailed(
        message: data,
      );
    }
  }


  Future<DataState<Map<String, dynamic>>> getUserStatus(String name) async {
    try {
      final response = await _dio.get( url: ConfigBase.statusUrl, queryParameters: {
        "name": name,
      });

      if (response.statusCode == 200) {
        // Assuming the response contains a boolean status field
        
        return DataSuccess(response.data);
      } else {
        return const DataFailed(message: "Failed to fetch status");
      }
    } catch (e) {
      return DataFailed(message: e.toString());
    }
  }
}
