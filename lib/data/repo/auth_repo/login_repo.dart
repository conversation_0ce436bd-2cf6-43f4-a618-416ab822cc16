import 'dart:convert';

import 'package:bus/config/config_base.dart';
import 'package:bus/config/global_variable.dart';
import 'package:bus/helper/data_state.dart';
import 'package:bus/helper/network_serviecs.dart';
import 'package:dio/dio.dart';
import 'package:flutter/material.dart';

class LoginRepo {
  final _dio = NetworkService();

  Future<DataState<LoginDataModelh>> repo({
    String? email,
    String? password,
  }) async {
    final _request = await _dio.post(
      url: ConfigBase.login,
      body: {"email": email, "password": password, "firebase_token": fCMToken},
    );
    // Logger().w(_request.data);
    if (_request.statusMessage == "OK") {
      debugPrint(_request.data["token"]);
      var logindata = LoginDataModelh.fromMap(_request.data);
      tempToken = _request.data["token"];
      debugPrint(logindata.toString());
      return DataSuccess(
        logindata,
      );
    } else {
      return DataFailed(
        message: _request.data['massage'],
      );
    }
  }

  Future<DataState<LoginDataModelh>> firebaseRepo({
    String? accessToken,
    String? idToken,
    //  required String? loginFCMToken,
  }) async {
    try {
      debugPrint("Initiating Firebase login with token");

      final request = await _dio.post(
        url: ConfigBase.firebase,
        body: {"accessToken": accessToken, "idToken": idToken,"firebase_token": fCMToken},
      );

      debugPrint(
          "Firebase login response - Status: ${request.statusCode}, Message: ${request.statusMessage}");
      debugPrint("Firebase login response data: ${request.data}");

      if (request.statusMessage == "OK") {
        var logindata = LoginDataModelh.fromMap(request.data);
        tempToken = request.data["token"];
        debugPrint("Firebase login successful: ${logindata.toString()}");
        return DataSuccess(logindata);
      } else {
        debugPrint(
            "Firebase login failed with message: ${request.data.toString()}");
        return DataFailed(
          message: request.data['massage'],
        );
      }
    } on DioException catch (e, stackTrace) {
      debugPrint(
          "DioException in Firebase login: ${e.message}" "$e" "$stackTrace");
      debugPrint("Status code: ${e.response?.statusCode}");

      // Return appropriate error response
      return DataFailed(
        message: e.response?.data?['massage'] ??
            e.message ??
            "Network error occurred",
      );
    } catch (e, stackTrace) {
      debugPrint("Unexpected error in Firebase login: $e" " $e" "$stackTrace");
      return const DataFailed(
        message: "An unexpected error occurred",
      );
    }
  }
}

class LoginDataModelh {
  String? token;
  final int? id;
  final String? name;
  final String? email;
  final String? phone;
  final String? email_verified_at;
  final String? address;
  final String? city_name;
  final int? status;
  final String? logo;
  final String? deleted_at;
  final String? created_at;
  final String? updated_at;
  final String? typeAuth;
  final String? logo_path;
  final String? latitude;
  final String? longitude;
  final bool? identity_preview;

  LoginDataModelh({
    this.token,
    this.id,
    this.name,
    this.email,
    this.phone,
    this.email_verified_at,
    this.address,
    this.city_name,
    this.status,
    this.logo,
    this.deleted_at,
    this.created_at,
    this.updated_at,
    this.typeAuth,
    this.logo_path,
    this.latitude,
    this.longitude,
    this.identity_preview,
  });

  LoginDataModelh copyWith({
    int? id,
    String? name,
    String? email,
    String? phone,
    String? email_verified_at,
    String? address,
    String? city_name,
    int? status,
    String? logo,
    String? deleted_at,
    String? created_at,
    String? updated_at,
    String? typeAuth,
    String? logo_path,
    String? latitude,
    String? longitude,
    bool? identity_preview,
  }) {
    return LoginDataModelh(
      id: id ?? this.id,
      name: name ?? this.name,
      email: email ?? this.email,
      phone: phone ?? this.phone,
      email_verified_at: email_verified_at ?? this.email_verified_at,
      address: address ?? this.address,
      city_name: city_name ?? this.city_name,
      status: status ?? this.status,
      logo: logo ?? this.logo,
      deleted_at: deleted_at ?? this.deleted_at,
      created_at: created_at ?? this.created_at,
      updated_at: updated_at ?? this.updated_at,
      typeAuth: typeAuth ?? this.typeAuth,
      logo_path: logo_path ?? this.logo_path,
      latitude: latitude ?? this.latitude,
      longitude: longitude ?? this.longitude,
      identity_preview: identity_preview ?? this.identity_preview,
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'name': name,
      'email': email,
      'phone': phone,
      'email_verified_at': email_verified_at,
      'address': address,
      'city_name': city_name,
      'status': status,
      'logo': logo,
      'deleted_at': deleted_at,
      'created_at': created_at,
      'updated_at': updated_at,
      'typeAuth': typeAuth,
      'logo_path': logo_path,
      'latitude': latitude,
      'longitude': longitude,
      'identity_preview': identity_preview,
    };
  }

  factory LoginDataModelh.fromMap(Map<String, dynamic> map) {
    return LoginDataModelh(
      token: map["token"],
      id: map["data"]['id'],
      name: map["data"]['name'],
      email: map["data"]['email'],
      phone: map["data"]['phone'],
      email_verified_at: map["data"]['email_verified_at'],
      address: map["data"]['address'],
      city_name: map["data"]['city_name'],
      status: map["data"]['status'],
      logo: map["data"]['logo'],
      deleted_at: map["data"]['deleted_at'],
      created_at: map["data"]['created_at'],
      updated_at: map["data"]['updated_at'],
      typeAuth: map["data"]['typeAuth'],
      logo_path: map["data"]['logo_path'],
      latitude: map["data"]['latitude'],
      longitude: map["data"]['longitude'],
      identity_preview: map["data"]['identity_preview'] != null
          ? map["data"]['identity_preview'] as bool
          : false,
    );
  }

  String toJson() => json.encode(toMap());

  factory LoginDataModelh.fromJson(String source) =>
      LoginDataModelh.fromMap(json.decode(source));

  @override
  String toString() {
    return 'LoginDataModelh(token :$token id: $id, name: $name, email: $email, phone: $phone, email_verified_at: $email_verified_at, address: $address, city_name: $city_name, status: $status, logo: $logo, deleted_at: $deleted_at, created_at: $created_at, updated_at: $updated_at, typeAuth: $typeAuth, logo_path: $logo_path, latitude: $latitude, longitude: $longitude, identity_preview: $identity_preview)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;

    return other is LoginDataModelh &&
        other.id == id &&
        other.name == name &&
        other.email == email &&
        other.phone == phone &&
        other.email_verified_at == email_verified_at &&
        other.address == address &&
        other.city_name == city_name &&
        other.status == status &&
        other.logo == logo &&
        other.deleted_at == deleted_at &&
        other.created_at == created_at &&
        other.updated_at == updated_at &&
        other.typeAuth == typeAuth &&
        other.logo_path == logo_path &&
        other.latitude == latitude &&
        other.longitude == longitude &&
        other.identity_preview == identity_preview;
  }

  @override
  int get hashCode {
    return id.hashCode ^
        name.hashCode ^
        email.hashCode ^
        phone.hashCode ^
        email_verified_at.hashCode ^
        address.hashCode ^
        city_name.hashCode ^
        status.hashCode ^
        logo.hashCode ^
        deleted_at.hashCode ^
        created_at.hashCode ^
        updated_at.hashCode ^
        typeAuth.hashCode ^
        logo_path.hashCode ^
        latitude.hashCode ^
        longitude.hashCode ^
        identity_preview.hashCode;
  }
}
