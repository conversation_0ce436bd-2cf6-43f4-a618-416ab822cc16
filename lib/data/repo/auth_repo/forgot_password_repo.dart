import 'package:bus/data/models/auth_models/forgot_password_model.dart';
import 'package:flutter/material.dart';
import 'package:logger/logger.dart';

import '../../../config/config_base.dart';
import '../../../helper/network_serviecs.dart';
import '../../models/auth_models/reset_password_model.dart';

class ForgotPasswordRepo {
  final _dio = NetworkService();

  Future <ForgotPasswordModel> forgotPasswordRepo({
    String? email,
  }) async {
    try {
      final request = await _dio.post(
        url: ConfigBase.forgotPassword,
        body: {
          "email": email,
        },
      );
      Logger().w(request.data);
      ForgotPasswordModel? forgotPasswordModel;
      if (request.statusCode == 200) {
        forgotPasswordModel = ForgotPasswordModel.fromMap(request.data);
      } else {
        forgotPasswordModel = ForgotPasswordModel.fromMap(request.data);
      }
      return forgotPasswordModel;
    } on Exception catch (e) {
      debugPrint("error $e");
      return ForgotPasswordModel();
    }
  }

  Future <ResetPasswordModel> resetPasswordRepo({
    String? email, code, password, passwordConfirmation
  }) async {
    try {
      final request = await _dio.post(
        url: ConfigBase.resetPassword,
        body: {
          "email": email,
          "code": code,
          "password": password,
          "password_confirmation": passwordConfirmation,
        },
      );
      ResetPasswordModel? resetPasswordModel;
      if (request.statusCode == 200) {
        resetPasswordModel = ResetPasswordModel.fromMap(request.data);
      } else {
        resetPasswordModel = ResetPasswordModel.fromMap(request.data);
      }
      return resetPasswordModel;
    } on Exception catch (e) {
      debugPrint("error $e");
      return ResetPasswordModel();
    }
  }

}