import 'package:bus/config/config_base.dart';
import 'package:bus/config/global_variable.dart';
import 'package:bus/helper/data_state.dart';
import 'package:bus/helper/network_serviecs.dart';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:logger/logger.dart';

import '../../models/change_password_model.dart';

class LogoutRepo {
  final _dio = NetworkService();

  Future<DataState<ChangePasswordModel>> repo() async {
    final _request = await _dio.post(
      url: ConfigBase.logout,
      body: {"firebase_token": fCMToken},
      isAuth: true,
    );

    if (_request.statusCode == 200) {
      // await FirebaseMessaging.instance.deleteToken();
      String? newToken = await FirebaseMessaging.instance.getToken();
      Logger().e("Newwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwww Token: $newToken");
      Logger().e('=======================${_request.data}');
      return DataSuccess(
        ChangePasswordModel.fromMap(
          _request.data,
        ),
      );
    } else {
      return const DataFailed();
    }
  }
}
