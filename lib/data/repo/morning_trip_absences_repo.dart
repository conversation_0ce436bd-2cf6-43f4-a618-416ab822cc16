import 'package:bus/data/models/trip_models/morning_trip_absences_model.dart';
import 'package:bus/helper/network_serviecs.dart';
import 'package:flutter/material.dart';

class MorningTripAbsencesRepo {
  final _dio = NetworkService();

  /// Fetches students absent from the morning trip
  /// 
  /// [busId] is the ID of the bus to get absent students for
  Future<MorningTripAbsencesResponse> getAbsentStudents({
    required String busId,
  }) async {
    try {
      final request = await _dio.get(
        url: "trips/morning/absences/$busId",
        isAuth: true,
      );

      debugPrint("Morning trip absences API response: ${request.statusCode}");

      if (request.statusCode == 200) {
        return MorningTripAbsencesResponse.fromJson(request.data);
      } else {
        // Handle non-200 status codes
        return MorningTripAbsencesResponse(
          errors: true,
          message: request.data['message'] ?? "Failed to fetch absent students",
          absences: [],
        );
      }
    } catch (e, s) {
      // Log the error for debugging
      debugPrint("Error fetching morning trip absences: $e");
      debugPrint("Stack trace: $s");

      // Return error response
      return MorningTripAbsencesResponse(
        errors: true,
        message: "Failed to fetch absent students: $e",
        absences: [],
      );
    }
  }
}
