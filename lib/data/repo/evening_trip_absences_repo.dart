import 'package:bus/data/models/trip_models/evening_trip_absences_model.dart';
import 'package:bus/helper/network_serviecs.dart';
import 'package:flutter/material.dart';

class EveningTripAbsencesRepo {
  final _dio = NetworkService();

  /// Fetches students absent from the evening trip
  /// 
  /// [busId] is the ID of the bus to get absent students for
  Future<EveningTripAbsencesResponse> getAbsentStudents({
    required String busId,
  }) async {
    try {
      final request = await _dio.get(
        url: "trips/evening/absences/$busId",
        isAuth: true,
      );

      debugPrint("Evening trip absences API response: ${request.statusCode}");

      if (request.statusCode == 200) {
        return EveningTripAbsencesResponse.fromJson(request.data);
      } else {
        // Handle non-200 status codes
        return EveningTripAbsencesResponse(
          errors: true,
          message: request.data['message'] ?? "Failed to fetch absent students",
          absences: [],
        );
      }
    } catch (e, s) {
      // Log the error for debugging
      debugPrint("Error fetching evening trip absences: $e");
      debugPrint("Stack trace: $s");

      // Return error response
      return EveningTripAbsencesResponse(
        errors: true,
        message: "Failed to fetch absent students: $e",
        absences: [],
      );
    }
  }
}
