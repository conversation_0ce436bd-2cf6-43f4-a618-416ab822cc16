import 'dart:developer';
import 'dart:io';
import 'package:bus/config/config_base.dart';
import 'package:bus/data/models/add_driver_models/add_driver_models.dart';
import 'package:bus/helper/network_serviecs.dart';
import 'package:dio/dio.dart';
import 'package:flutter/material.dart';

class UpdateDriverRepo {
  final _dio = NetworkService();

  Future<AddDriverModels> repo({
    int? driverId,
    String? name,
    MultipartFile? file__lol,
    String? fileName,
    String? username,
    int? genderId,
    int? religionId,
    int? typeBloodId,
    int? busId,
    String? birthDate,
    String? joiningDate,
    String? password,
    String? address,
    String? cityName,
    String? phone,
    File? image,
  }) async {
    try {
      if (image != null) {
        fileName = image.path.split('/').last;
        file__lol =
            await MultipartFile.fromFile(image.path, filename: fileName);
      }
    } catch (e, stackTrack) {
      debugPrint(e.toString());
      debugPrint(stackTrack.toString());
    }

    final datamap = {
      if (name != null && name.isNotEmpty) "name": name,
      if (username != null && username.isNotEmpty) "username": username,
      if (joiningDate != null && joiningDate.isNotEmpty)
        "Joining_Date": joiningDate,
      if (birthDate != null && birthDate.isNotEmpty) "birth_date": birthDate,
      if (genderId != null && genderId != 0) "gender_id": genderId,
      if (religionId != null && religionId != 0) "religion_id": religionId,
      if (typeBloodId != null && typeBloodId != 0)
        "type__blood_id": typeBloodId,
      if (busId != null) "bus_id": busId == 0 ? null : busId,
      if (address != null && address.isNotEmpty) "address": address,
      if (cityName != null && cityName.isNotEmpty) "city_name": cityName,
      if (phone != null && phone.isNotEmpty) "phone": phone,
      if (file__lol != null) "logo": file__lol,
      if (password != null && password.isNotEmpty) "password": password,
    };

    log("datamap:${datamap.toString()}  ");
    FormData formData = FormData.fromMap(datamap);

    try {
      final _request = await _dio.post(
        url: "${ConfigBase.baseUrl}attendants/update/driver/$driverId",
        body: formData,
        isAuth: true,
      );

      AddDriverModels? addDriverModels;
      if (_request.statusCode == 200) {
        addDriverModels = AddDriverModels.fromJson(_request.data);
      } else {
        addDriverModels =
            AddDriverModels(messages: _request.data['messages']['username'][0]);
      }
      return addDriverModels;
    } catch (e, stackTrace) {
      debugPrint(stackTrace.toString());
      debugPrint("catch error at student repo $e");
      return AddDriverModels(message: e.toString());
    }
  }
}
