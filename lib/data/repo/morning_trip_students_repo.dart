import 'package:bus/data/models/trip_models/morning_trip_students_model.dart';
import 'package:bus/helper/network_serviecs.dart';
import 'package:flutter/material.dart';

class MorningTripStudentsRepo {
  final _dio = NetworkService();

  /// Fetches students present on the bus for a morning trip
  /// 
  /// [busId] is the ID of the bus to get students for
  Future<MorningTripStudentsResponse> getPresentStudents({
    required String busId,
  }) async {
    try {
      final request = await _dio.get(
        url: "trips/morning/onbus/$busId",
        isAuth: true,
      );

      debugPrint("Morning trip students API response: ${request.statusCode}");

      if (request.statusCode == 200) {
        return MorningTripStudentsResponse.fromJson(request.data);
      } else {
        // Handle non-200 status codes
        return MorningTripStudentsResponse(
          errors: true,
          message: request.data['message'] ?? "Failed to fetch students",
          presentOnBus: [],
        );
      }
    } catch (e, s) {
      // Log the error for debugging
      debugPrint("Error fetching morning trip students: $e");
      debugPrint("Stack trace: $s");

      // Return error response
      return MorningTripStudentsResponse(
        errors: true,
        message: "Failed to fetch students: $e",
        presentOnBus: [],
      );
    }
  }
}
