import 'dart:developer';
import 'package:bus/config/config_base.dart';
import 'package:bus/data/models/updata_bus_models/update_bus_models.dart';
import 'package:bus/helper/network_serviecs.dart';
import 'package:dio/dio.dart';
import 'package:flutter/material.dart';

class UpdateBusRepo {
  final _dio = NetworkService();

  Future<UpdateBusModels> repo({
    String? name,
    String? notes,
    String? car_number,
    String? busId,
  }) async {
    Map<String, dynamic> datamap = {
      "name": name,
      "notes": notes,
      "car_number": car_number,
    };

    log("datamap:${datamap.toString()}  ");
    FormData formData = FormData.fromMap(datamap);

    try {
      final _request = await _dio.post(
        url: "${ConfigBase.baseUrl}buses/update/$busId",
        body: formData,
        isAuth: true,
      );
      // debugPrint(_request.data);
      // Logger().wtf(_request.data);
      // debugPrint("student from repo ${_request.statusCode}");
      UpdateBusModels? updateBusModels;
      if (_request.statusCode == 200) {
        updateBusModels = UpdateBusModels.fromJson(_request.data);
      } else {
        updateBusModels = UpdateBusModels.fromJson(_request.data);
      }
      return updateBusModels;
    } catch (e, stackTrace) {
      debugPrint(stackTrace.toString());
      debugPrint("catch error at student repo $e");
      return UpdateBusModels(message: e.toString());
    }
  }
}
