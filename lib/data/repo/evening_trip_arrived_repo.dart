import 'package:bus/data/models/trip_models/evening_trip_arrived_model.dart';
import 'package:bus/helper/network_serviecs.dart';
import 'package:flutter/material.dart';

class EveningTripArrivedRepo {
  final _dio = NetworkService();

  /// Fetches students who arrived home during an evening trip
  /// 
  /// [busId] is the ID of the bus to get arrived students for
  Future<EveningTripArrivedResponse> getArrivedStudents({
    required String busId,
  }) async {
    try {
      final request = await _dio.get(
        url: "trips/evening/arrived/$busId",
        isAuth: true,
      );

      debugPrint("Evening trip arrived students API response: ${request.statusCode}");

      if (request.statusCode == 200) {
        return EveningTripArrivedResponse.fromJson(request.data);
      } else {
        // Handle non-200 status codes
        return EveningTripArrivedResponse(
          errors: true,
          message: request.data['message'] ?? "Failed to fetch arrived students",
          arrived: [],
        );
      }
    } catch (e, s) {
      // Log the error for debugging
      debugPrint("Error fetching evening trip arrived students: $e");
      debugPrint("Stack trace: $s");

      // Return error response
      return EveningTripArrivedResponse(
        errors: true,
        message: "Failed to fetch arrived students: $e",
        arrived: [],
      );
    }
  }
}
