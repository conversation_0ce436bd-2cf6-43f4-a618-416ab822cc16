import 'package:bus/config/config_base.dart';
import 'package:bus/data/models/religion_models/religion_models.dart';
import 'package:bus/helper/network_serviecs.dart';
import 'package:flutter/material.dart';

class ReligionRepo {
  final _dio = NetworkService();

  Future<ReligionModels> repo() async {
    try {
      final _request = await _dio.get(
        url: ConfigBase.religion,
        isAuth: true,
      );
      // debugPrint(_request.data);
      // Logger().wtf(_request.data);
      // debugPrint("student from repo ${_request.statusCode}");
      ReligionModels? religionModels;
      if (_request.statusCode == 200) {
        religionModels = ReligionModels.fromJson(_request.data);
      } else {
        religionModels = ReligionModels.fromJson(_request.data);
      }
      return religionModels;
    } catch (e) {
      debugPrint("catch error $e");
      return ReligionModels(message: e.toString());
    }
  }
}
