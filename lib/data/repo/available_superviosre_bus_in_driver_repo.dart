import 'package:bus/config/config_base.dart';
import 'package:bus/data/models/bus_avaiable_models/bus_available_models.dart';
import 'package:bus/helper/network_serviecs.dart';
import 'package:flutter/material.dart';

class AvailableSupervisorBusInDriverRepo {
  final _dio = NetworkService();

  Future<BusAvailableModels> repo() async {
    try {
      final _request = await _dio.get(
        url: "${ConfigBase.availableBusInDriverSupervisor}",
        isAuth: true,
      );
      // debugPrint(_request.data);
      // Logger().wtf(_request.data);
      // debugPrint("student from repo ${_request.statusCode}");
      BusAvailableModels? busAvailableModels;
      if (_request.statusCode == 200) {
        busAvailableModels = BusAvailableModels.fromJson(_request.data);
      } else {
        busAvailableModels = BusAvailableModels.fromJson(_request.data);
      }
      return busAvailableModels;
    } catch (e, s) {
      debugPrint( "$e $s");
      return BusAvailableModels(message: e.toString());
    }
  }
}
