import 'package:bus/config/config_base.dart';
import 'package:bus/data/models/bus_avaiable_models/bus_available_models.dart';
import 'package:bus/helper/network_serviecs.dart';
import 'package:flutter/material.dart';

class BusAvailableRepo {
  final _dio = NetworkService();

  Future<BusAvailableModels> repo() async {
    try {
      final _request = await _dio.get(
        url:
            "${ConfigBase.availableBusInDriver}", //https://test.busatyapp.com/api/schools/buses/show/availableAdd/driver
        isAuth: true,
      );
      debugPrint(_request.data.toString());
      debugPrint("student from repo ${_request.statusCode}");
      BusAvailableModels? busAvailableModels;
      if (_request.statusCode == 200) {
        busAvailableModels = BusAvailableModels.fromJson(_request.data);
      } else {
        busAvailableModels = BusAvailableModels.fromJson(_request.data);
      }
      return busAvailableModels;
    } catch (e, stackTrace) {
      debugPrint("stackTrace $stackTrace");
      debugPrint("catch error $e");
      return BusAvailableModels(message: e.toString());
    }
  }
}
