import 'package:bus/config/config_base.dart';
import 'package:bus/data/models/notification_settings_models/notification_settings_model.dart';
import 'package:bus/helper/network_serviecs.dart';
import 'package:flutter/material.dart';

class NotificationSettingsRepo {
  final _dio = NetworkService();

  Future<NotificationSettingsModel> updateNotificationSettings({
    required bool tripStartEndNotificationStatus,
    required bool studentAbsenceNotificationStatus,
    required bool studentAddressNotificationStatus,
  }) async {
    try {
      final request = await _dio.post(
        url: ConfigBase.notificationSettings,
        body: {
          "trip_start_end_notification_status": tripStartEndNotificationStatus ? 1 : 0,
          "student_absence_notification_status": studentAbsenceNotificationStatus ? 1 : 0,
          "student_address_notification_status": studentAddressNotificationStatus ? 1 : 0,
        },
        isAuth: true,
      );
      
      debugPrint("Notification Settings API Response: ${request.data}");
      
      NotificationSettingsModel? notificationSettingsModel;
      if (request.statusCode == 200) {
        notificationSettingsModel = NotificationSettingsModel.fromJson(request.data);
      } else {
        notificationSettingsModel = NotificationSettingsModel.fromJson(request.data);
      }
      
      return notificationSettingsModel;
    } catch (e) {
      debugPrint("catch error $e");
      return NotificationSettingsModel(
        status: false,
        message: e.toString(),
      );
    }
  }
}
