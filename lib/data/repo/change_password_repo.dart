import 'package:bus/config/config_base.dart';
import 'package:bus/helper/data_state.dart';
import 'package:bus/helper/network_serviecs.dart';

import '../models/change_password_model.dart';

class ChangePasswordRepo {
  final _dio = NetworkService();

  Future<DataState<ChangePasswordModel>> repo(
      {String? currentPassword,
      String? newPassword,
      String? confirmedPassword}) async {
    final request = await _dio.post(
      url: ConfigBase.changePassword,
      body: {
        "current_password": currentPassword,
        "password": newPassword,
        "password_confirmation": confirmedPassword,
      },
      isAuth: true,
    );
    if (request.statusMessage == "OK") {
      return DataSuccess(
        ChangePasswordModel.fromMap(request.data),
      );
    } else {
      return DataFailed(message: request.data["message"]);
    }
  }
}
