import 'package:bus/config/config_base.dart';
import 'package:bus/data/models/delete_driver_supervisor_models/delete_driver_supervisor_models.dart';
import 'package:bus/helper/network_serviecs.dart';
import 'package:flutter/material.dart';

class DeleteDriverSupervisorRepo {
  final _dio = NetworkService();

  Future<DeleteDriverSupervisorModels> repo({
    int? id,
  }) async {
    try {
      final _request = await _dio.delete(
        url: "${ConfigBase.deleteSupervisorAndDriver}/$id",
        isAuth: true,
      );

      DeleteDriverSupervisorModels? deleteDriverSupervisorModels;
      if (_request.statusCode == 200) {
        deleteDriverSupervisorModels =
            DeleteDriverSupervisorModels.fromJson(_request.data);
      } else {
        deleteDriverSupervisorModels =
            DeleteDriverSupervisorModels.fromJson(_request.data);
      }
      return deleteDriverSupervisorModels;
    } catch (e, s) {
      debugPrint("catch error $s");
      debugPrint("catch error $e");
      return DeleteDriverSupervisorModels(message: e.toString());
    }
  }
}
