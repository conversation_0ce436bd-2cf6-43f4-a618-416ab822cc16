import 'dart:io';

import 'package:bus/config/config_base.dart';
import 'package:bus/translations/local_keys.g.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:logger/logger.dart';
import 'package:open_file/open_file.dart';
import 'package:path_provider/path_provider.dart';

import '../../helper/network_serviecs.dart';

class DownloadStudentsFileRepo {
  final _dio = NetworkService();

  Future<void> downloadStudentsExampleFile ({
    required String fileNameAndExtension,
  }) async {
    try {
      // Obtain the public Downloads directory
      Directory? directory;
      if (Platform.isAndroid) {
        directory = Directory('/storage/emulated/0/Download');
      } else if (Platform.isIOS) {
        directory = await getApplicationDocumentsDirectory();
      }

      if (directory == null) {
        debugPrint("Could not find the public directory.");
        return;
      }

      // Extract file name and extension
      final fileName = fileNameAndExtension.split('.').first;
      final fileExtension = fileNameAndExtension.split('.').last;

      // Generate a timestamp and sanitize it for use in file names
      final timestamp = DateTime.now().toIso8601String().replaceAll(':', '-');
      final sanitizedTimestamp = timestamp.replaceAll(RegExp(r'[^\w\-]'), '');

      // Construct the save path with the timestamp
      final savePath =
          '${directory.path}/${fileName}_$sanitizedTimestamp.$fileExtension';

      // Log the save path
      Logger().w('Saving file to: $savePath');

      // Download the file
      final response = await _dio.download(
        url: ConfigBase
            .downloadStudentsExampleFile, // Replace with your actual URL
        savePath: savePath,
        isAuth: true, // Add your authentication configuration here if needed
      );

      if (response.statusCode == 200) {
        Logger().w('File downloaded successfully: $savePath');
        OpenFile.open(savePath); // Open the downloaded file
      } else {
        debugPrint("Download failed with status code: ${response.statusCode}");
      }
    } catch (e) {
      debugPrint("Error during download: $e");
    }
  }

  void downloadStudentsPDFFile(
      {String? busName, int? busId, String? lang}) async {
    final directory = await getExternalStorageDirectory();
    final savePath =
        '${directory?.path}/${busName}_${AppStrings.students.tr()}.pdf';
    Logger().e(savePath);
    Logger().e('oooooooooooooooooooooooooooooooooooooooooo$directory');

    try {
      final response = await _dio.download(
        url: '${ConfigBase.downloadStudentsPDFFile}/$busId/$lang',
        savePath: savePath,
        isAuth: true,
      );
      if (response.statusCode == 200) {
        OpenFile.open(savePath);
      } else {
        debugPrint("Download failed with status code: ${response.statusCode}");
      }
    } catch (e) {
      debugPrint("Error during download: $e");
    }
  }
}
