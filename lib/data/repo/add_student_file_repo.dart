import 'dart:developer';
import 'dart:io';
import 'package:bus/config/config_base.dart';
import 'package:bus/data/models/add_student_file_models/add_student_file_error_models.dart';

import 'package:bus/data/models/add_student_file_models/add_student_file_models.dart';
import 'package:bus/helper/network_serviecs.dart';
import 'package:dio/dio.dart';
import 'package:flutter/material.dart';

class AddStudentFileRepo {
  final _dio = NetworkService();

  Future<AddStudentFileModels> repo({
    int? gradeId,
    File? uploadFile,
    String? file,
    int? classId,
  }) async {
    final file__lol = await MultipartFile.fromFile(uploadFile!.path, filename: file);

    Map<String, dynamic> datamap = {};
    datamap.addAll({
      "attachment": file__lol,
      "grade_id": gradeId,
      "classroom_id": classId
    });
    log("datamap:${datamap.toString()}  ");
    FormData formData = FormData.fromMap(datamap);

    try {
      final _request = await _dio.post(
        url: "${ConfigBase.baseUrl}students/import",
        isAuth: true,
        body: formData,
      );
      // debugPrint(_request.data);
      // Logger().wtf(_request.data);
      // debugPrint("student from repo ${_request.statusCode}");
      AddStudentFileModels? _addStudentFileModels;
      if (_request.statusCode == 200) {
        _addStudentFileModels = AddStudentFileModels.fromJson(_request.data);
      } else {
        _addStudentFileModels = AddStudentFileModels(errors: AddStudentFileErrorModels.fromJson(_request.data));
      }
      return _addStudentFileModels;
    } catch (e, s) {
      debugPrint("catch error $s");
      debugPrint("catch error $e");
      return AddStudentFileModels(message: e.toString());
    }
  }
}
