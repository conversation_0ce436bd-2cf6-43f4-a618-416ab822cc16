import 'package:bus/config/config_base.dart';
import 'package:bus/data/models/file_name_download_models/file_name-download_models.dart';
import 'package:bus/helper/network_serviecs.dart';
import 'package:flutter/material.dart';

class FileNameDownloadRepo {
  final _dio = NetworkService();

  Future<FileNameDownloadModels> repo() async {
    try {
      final _request = await _dio.get(
        url: ConfigBase.fileNameAndExtension,
        isAuth: true,
      );
      // debugPrint(_request.data);
      // Logger().wtf(_request.data);
      // debugPrint("student from repo ${_request.statusCode}");
      FileNameDownloadModels? fileNameDownloadModels;
      if (_request.statusCode == 200) {
        fileNameDownloadModels = FileNameDownloadModels.fromJson(_request.data);
      } else {
        fileNameDownloadModels = FileNameDownloadModels.fromJson(_request.data);
      }
      return fileNameDownloadModels;
    } catch (e) {
      debugPrint("catch error $e");
      return FileNameDownloadModels(message: e.toString());
    }
  }

}
