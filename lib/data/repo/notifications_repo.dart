import 'package:dio/dio.dart';
import 'package:flutter/material.dart';

import '../../config/config_base.dart';
import '../../config/global_variable.dart';
import '../../helper/network_serviecs.dart';
import '../models/notifications_models/fcm_token_model.dart';
import '../models/notifications_models/fcm_response.dart';
import '../models/notifications_models/notifications_model.dart';
import '../models/notifications_models/store_notification_model.dart';

class NotificationsRepo {
  final _dio = NetworkService();

  Future<AllNotificationsModel1> notifications({int? page}) async {
    try {
      final request = await _dio.get(
        url:
            "https://test.busatyapp.com/api/schools/notifications?page=1&limit=10",
        isAuth: true,
      );
      // Logger().w(request.data);
      AllNotificationsModel1? notificationsModel;
      if (request.statusCode == 200) {
        notificationsModel = AllNotificationsModel1.from<PERSON>son(request.data);
      } else {
        notificationsModel = AllNotificationsModel1.fromJson(request.data);
      }
      return notificationsModel;
    } catch (e) {
      debugPrint("catch error $e");
      rethrow;
    }
  }

  Future<FCMResponse> sendNotification({
    required List<String> deviceTokens,
    String? title,
    String? body,
  }) async {
    try {
      Dio dio = Dio();

      Map<String, dynamic> requestBody = {
        'registration_ids': deviceTokens,
        'notification': {
          'body': '$body',
          'priority': 'high',
          'subtitle': '',
          'title': '$title',
          'sound': 'default',
        },
        'data': {
          'priority': 'high',
          'urls': '',
        },
      };

      Options options = Options(
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'key=$firebaseServerKey',
        },
      );

      Response response = await dio.post(
        ConfigBase.fcmUrl,
        data: requestBody,
        options: options,
      );

      FCMResponse? notificationsModel;
      if (response.statusCode == 200) {
        notificationsModel = FCMResponse.fromMap(response.data);
        debugPrint('Notification sent successfully');
        debugPrint('${response.data}');
      } else {
        notificationsModel = FCMResponse.fromMap(response.data);
        debugPrint('Failed to send notification: ${response.statusCode}');
        debugPrint(response.data);
      }
      return notificationsModel;
    } catch (e, s) {
      debugPrint("catch error $s");
      debugPrint("catch error at repo $e");
      debugPrint('Error sending notification: $e');
      return FCMResponse();
    }
  }

  Future<FcmTokenModel> allParentsFcmTokens() async {
    try {
      final request = await _dio.get(
        url: ConfigBase.allParentsFcmTokens,
        isAuth: true,
      );
      FcmTokenModel? fcmTokenModel;
      if (request.statusCode == 200) {
        fcmTokenModel = FcmTokenModel.fromMap(request.data);
      } else {
        fcmTokenModel = FcmTokenModel.fromMap(request.data);
      }
      return fcmTokenModel;
    } catch (e, s) {
      debugPrint("catch error $s");
      debugPrint("catch error at repo $e");
      debugPrint("catch error $e");
      return FcmTokenModel(message: e.toString());
    }
  }

  Future<FcmTokenModel> parentFcmTokens({int? studentId}) async {
    try {
      final request = await _dio.post(
        url: '${ConfigBase.parentFcmTokens}/$studentId',
        isAuth: true,
      );
      FcmTokenModel? fcmTokenModel;
      if (request.statusCode == 200) {
        fcmTokenModel = FcmTokenModel.fromMap(request.data);
      } else {
        fcmTokenModel = FcmTokenModel.fromMap(request.data);
      }
      return fcmTokenModel;
    } catch (e, s) {
      debugPrint("catch error $s");
      debugPrint("catch error at repo $e");
      debugPrint("catch error $e");
      return FcmTokenModel(message: e.toString());
    }
  }

  // Future<FcmTokenModel> supervisorFcmToken({String? busId}) async {
  //   try {
  //     final request = await _dio.get(
  //       url: '${ConfigBase.supervisorsFcmTokens}/$busId',
  //       isAuth: true,
  //     );
  //     FcmTokenModel? fcmTokenModel;
  //     if (request.statusCode == 200) {
  //       fcmTokenModel = FcmTokenModel.fromMap(request.data);
  //     } else {
  //       fcmTokenModel = FcmTokenModel.fromMap(request.data);
  //     }
  //     return fcmTokenModel;
  //   } catch (e) {
  //     debugPrint("catch error $e");
  //     return FcmTokenModel(message: e.toString());
  //   }
  // }

  Future<StoreNotificationModel> storeBusNotification(
      {int? busId, String? title, body, route}) async {
    try {
      final request = await _dio.post(
        url: '${ConfigBase.storeBusNotification}/$busId',
        body: {"title": title, "body": body, "route": route},
        isAuth: true,
      );
      StoreNotificationModel? storeBusNotification;
      if (request.statusCode == 200) {
        debugPrint('storeBusNotification Success');
        storeBusNotification = StoreNotificationModel.fromMap(request.data);
      } else {
        debugPrint('storeBusNotification Failed');
        storeBusNotification = StoreNotificationModel.fromMap(request.data);
      }
      return storeBusNotification;
    } on Exception catch (e) {
      debugPrint("error $e");
      return StoreNotificationModel(message: e.toString());
    }
  }
}
