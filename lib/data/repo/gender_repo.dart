import 'package:bus/config/config_base.dart';
import 'package:bus/data/models/gender_models/gender_models.dart';
import 'package:bus/helper/network_serviecs.dart';
import 'package:flutter/cupertino.dart';

class GenderRepo {
  final _dio = NetworkService();

  Future<GenderModels> repo() async {
    try {
      final _request = await _dio.get(
        url: ConfigBase.gender,
        isAuth: true,
      );
      // debugPrint(_request.data);
      // Logger().wtf(_request.data);
      // debugPrint("student from repo ${_request.statusCode}");
      GenderModels? genderModels;
      if (_request.statusCode == 200) {
        genderModels = GenderModels.fromJson(_request.data);
      } else {
        genderModels = GenderModels.fromJson(_request.data);
      }
      return genderModels;
    } catch (e) {
      debugPrint("catch error $e");
      return GenderModels(message: e.toString());
    }
  }
}
