import 'package:bus/config/config_base.dart';
import 'package:bus/config/global_variable.dart';
import 'package:bus/data/models/user_models/user_models.dart';
import 'package:bus/helper/data_state.dart';
import 'package:bus/helper/network_serviecs.dart';
import 'package:flutter/material.dart';
import 'dart:developer';
import 'package:logger/logger.dart';

class UserRepo {
  final _dio = NetworkService();

  Future<DataState<UserModel>> repo() async {
    try {
      final request = await _dio.get(
        url: ConfigBase.profile,
        isAuth: true,
      );
      Logger().w(request.data);
      debugPrint("student from repo ${request.toString()}");
      if (request.statusCode == 200) {
        userEmail = request.data["email"];
        userName = request.data["name"];
        userImageUrl = request.data["logo_path"];
        userPhone = request.data["phone"];
        userAddress = request.data["address"];
        subscriptionStatus = request.data["subscription_status"] ?? false;
        debugPrint('subscriptionStatus: ${request.data["subscription_status"]}');
        Logger().e('subscription - Status: $subscriptionStatus');

        return DataSuccess(
          UserModel.fromJson(request.data),
        );
      } else {
        log("catch error ${request.statusCode}");
        debugPrint("catch error ${request.statusCode}");
        Logger().e('Error in repo(): ${request.statusCode}');
        return const DataFailed();
      }
    } catch (e, s) {
      log("catch error $s");
      debugPrint("catch error $s");
      Logger().e('Error in repo(): $e');
      return const DataFailed();
    }
  }

  Future<UserModel> repoHome() async {
    try {
      final _request = await _dio.get(
        url: ConfigBase.profile,
        isAuth: true,
      );

      Logger().d(_request.data);

      UserModel userModel;
      if (_request.statusCode == 200) {
        userModel = UserModel.fromJson(_request.data);
        userModel.success = true;
      } else {
        userModel = UserModel.fromJson(_request.data);
        userModel.success = false;
      }

      return userModel;
    } catch (e, s) {
      debugPrint("catch error $s");
      Logger().e('Error in repoHome(): $e');
      return UserModel(success: false);
    }
  }
}
