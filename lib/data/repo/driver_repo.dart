import 'package:bus/config/config_base.dart';
import 'package:bus/data/models/driver_models/driver_models.dart';
import 'package:bus/helper/network_serviecs.dart';
import 'package:flutter/material.dart';

class DriverRepo {
  final _dio = NetworkService();

  Future<DriverModels> repo({
    int? pageNumber,
  }) async {
    try {
      final _request = await _dio.get(
        url: "${ConfigBase.driver}?page=$pageNumber&limit=10",
        isAuth: true,
      );
      // debugPrint(_request.data);
      // Logger().wtf(_request.data);
      // debugPrint("student from repo ${_request.statusCode}");
      DriverModels? driverModels;
      if (_request.statusCode == 200) {
        driverModels = DriverModels.fromJson(_request.data);
      } else {
        driverModels = DriverModels.fromJson(_request.data);
      }
      return driverModels;
    } catch (e) {
      debugPrint("catch error $e");
      return DriverModels(message: e.toString());
    }
  }
}
