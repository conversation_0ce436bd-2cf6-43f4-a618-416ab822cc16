import 'package:bus/config/config_base.dart';
import 'package:bus/data/models/attendance_models/absent_student_model.dart';
import 'package:bus/data/models/attendance_models/attendant_student_model.dart';
import 'package:bus/data/models/attendance_models/student_attendance_model.dart';
import 'package:bus/helper/cache_helper.dart';
import 'package:bus/helper/network_serviecs.dart';
import 'package:flutter/material.dart';

class AttendanceRepo {
  final String token = CacheHelper.getString('token') ?? '';
  final _dio = NetworkService();

  Future<TripAttendanceResponse> getTripAttendance({
    required int tripId,
  }) async {
    try {
      // In a real implementation, we would make an API call here
      // For now, return mock data
      return _getMockAttendanceData(tripId);
    } catch (e) {
      // Return empty response on error
      return TripAttendanceResponse(
        status: false,
        message: "Failed to fetch attendance data",
        data: [],
      );
    }
  }

  Future<AbsentStudentsResponse> getTripAbsentStudents({
    required int tripId,
  }) async {
    try {
      // Make API call to get absent students
      final response = await _dio.get(
        url: ConfigBase.tripAbsentStudents + tripId.toString(),
        isAuth: true,
      );

      debugPrint("Trip Absent Students API Response: ${response.data}");

      if (response.statusCode == 200) {
        // Parse the response
        final responseData = response.data;

        return AbsentStudentsResponse.fromJson(responseData);
      } else {
        return AbsentStudentsResponse(
          message:
              "Failed to fetch absent students data. Status code: ${response.statusCode}",
          errors: true,
          absentStudents: [],
        );
      }
    } catch (e) {
      debugPrint("Error fetching absent students: $e");
      // Return empty response on error
      return AbsentStudentsResponse(
        message: "Failed to fetch absent students data: $e",
        errors: true,
        absentStudents: [],
      );
    }
  }

  Future<AttendantStudentsResponse> getTripAttendantStudents({
    required int tripId,
  }) async {
    try {
      // Make API call to get attendant students
      final response = await _dio.get(
        url: ConfigBase.tripAttendantStudents + tripId.toString(),
        isAuth: true,
      );

      debugPrint("Trip Attendant Students API Response: ${response.data}");

      if (response.statusCode == 200) {
        // Parse the response
        final responseData = response.data;

        return AttendantStudentsResponse.fromJson(responseData);
      } else {
        return AttendantStudentsResponse(
          message:
              "Failed to fetch attendant students data. Status code: ${response.statusCode}",
          errors: true,
          attendantStudents: [],
        );
      }
    } catch (e) {
      debugPrint("Error fetching attendant students: $e");
      // Return empty response on error
      return AttendantStudentsResponse(
        message: "Failed to fetch attendant students data: $e",
        errors: true,
        attendantStudents: [],
      );
    }
  }

  // Mock data for UI development until API is available
  TripAttendanceResponse _getMockAttendanceData(int tripId) {
    // Generate different data based on tripId to simulate different trips
    final isEvenTrip = tripId % 2 == 0;

    return TripAttendanceResponse(
      status: true,
      message: "Attendance data fetched successfully",
      data: [
        // Present students
        StudentAttendanceModel(
          id: 1,
          name: "أحمد محمد",
          is_present: true,
          attendance_time: "07:45 AM",
          grade: "الصف الثالث",
          classroom: "3/أ",
          profile_image: null,
        ),
        StudentAttendanceModel(
          id: 2,
          name: "سارة أحمد",
          is_present: true,
          attendance_time: "07:50 AM",
          grade: "الصف الثاني",
          classroom: "2/ب",
          profile_image: null,
        ),
        StudentAttendanceModel(
          id: 3,
          name: "محمد علي",
          is_present: true,
          attendance_time: "07:55 AM",
          grade: "الصف الرابع",
          classroom: "4/أ",
          profile_image: null,
        ),
        StudentAttendanceModel(
          id: 4,
          name: "فاطمة حسن",
          is_present: true,
          attendance_time: "08:00 AM",
          grade: "الصف الأول",
          classroom: "1/ج",
          profile_image: null,
        ),
        StudentAttendanceModel(
          id: 5,
          name: "خالد عبدالله",
          is_present: true,
          attendance_time: "07:40 AM",
          grade: "الصف الخامس",
          classroom: "5/أ",
          profile_image: null,
        ),

        // Absent students - more for even trips, fewer for odd trips
        StudentAttendanceModel(
          id: 6,
          name: "نورا سعيد",
          is_present: false,
          attendance_time: null,
          grade: "الصف الثالث",
          classroom: "3/ب",
          profile_image: null,
        ),
        StudentAttendanceModel(
          id: 7,
          name: "عمر خالد",
          is_present: false,
          attendance_time: null,
          grade: "الصف الرابع",
          classroom: "4/ج",
          profile_image: null,
        ),
        if (isEvenTrip) ...[
          StudentAttendanceModel(
            id: 8,
            name: "ليلى محمود",
            is_present: false,
            attendance_time: null,
            grade: "الصف الثاني",
            classroom: "2/أ",
            profile_image: null,
          ),
          StudentAttendanceModel(
            id: 9,
            name: "يوسف أحمد",
            is_present: false,
            attendance_time: null,
            grade: "الصف الخامس",
            classroom: "5/ب",
            profile_image: null,
          ),
        ],
      ],
    );
  }
}
