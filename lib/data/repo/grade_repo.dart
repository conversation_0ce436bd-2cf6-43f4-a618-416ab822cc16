import 'package:bus/config/config_base.dart';
import 'package:bus/data/models/grade_models/grade_models.dart';
import 'package:bus/helper/network_serviecs.dart';
import 'package:flutter/material.dart';

class GradeRepo {
  final _dio = NetworkService();

  Future<GradeModels> repo() async {
    try {
      final _request = await _dio.get(
        url: ConfigBase.grade,
        isAuth: true,
      );
      // debugPrint(_request.data);
      // Logger().wtf(_request.data);
      // debugPrint("student from repo ${_request.statusCode}");
      GradeModels? gradeModels;
      if (_request.statusCode == 200) {
        gradeModels = GradeModels.fromJson(_request.data);
      } else {
        gradeModels = GradeModels.fromJson(_request.data);
      }
      return gradeModels;
    } catch (e) {
      debugPrint("catch error $e");
      return GradeModels(message: e.toString());
    }
  }
}
