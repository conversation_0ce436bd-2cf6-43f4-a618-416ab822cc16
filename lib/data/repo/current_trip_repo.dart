import 'package:bus/config/config_base.dart';
import 'package:bus/data/models/current_trip_models/current_trip_models.dart';
import 'package:bus/helper/network_serviecs.dart';
import 'package:flutter/material.dart';

class CurrentTripRepo {
  final _dio = NetworkService();

  Future<CurrentTripModels> repo() async {
    try {
      final request = await _dio.get(
        url: ConfigBase.schoolCurrentTrips,
        isAuth: true,
      );

      // Logger().w(request.data);
      if (request.statusCode == 200) {
        var data = CurrentTripModels.fromJson(request.data);

        return data;
      } else {
        // Optionally handle non-200 status codes if needed
        return CurrentTripModels.fromJson(request.data);
      }
    } catch (e, s) {
      // Handle exceptions such as network errors or JSON parsing errors
      debugPrint("Error occurred: $e");
      debugPrint("Error occurred: $s");
      throw Exception("Failed to fetch current trip models: $e");
    }
  }
}
