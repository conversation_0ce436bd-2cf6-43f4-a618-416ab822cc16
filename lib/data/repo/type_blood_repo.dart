import 'package:bus/config/config_base.dart';
import 'package:bus/data/models/type_blood_models/type_blood_models.dart';
import 'package:bus/helper/network_serviecs.dart';
import 'package:flutter/material.dart';

class TypeBloodRepo {
  final _dio = NetworkService();

  Future<TypeBloodModels> repo() async {
    try {
      final _request = await _dio.get(
        url: ConfigBase.typeBlood,
        isAuth: true,
      );
      // debugPrint(_request.data);
      // Logger().wtf(_request.data);
      // debugPrint("student from repo ${_request.statusCode}");
      TypeBloodModels? typeBloodModels;
      if (_request.statusCode == 200) {
        typeBloodModels = TypeBloodModels.fromJson(_request.data);
      } else {
        typeBloodModels = TypeBloodModels.fromJson(_request.data);
      }
      return typeBloodModels;
    } catch (e,stackTrace) {
      debugPrint(stackTrace.toString());
      debugPrint("catch error at student repo $e");
      return TypeBloodModels(message: e.toString());
    }
  }
}
