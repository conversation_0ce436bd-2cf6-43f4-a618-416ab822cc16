import 'package:bus/data/models/trip_models/trip_route_model.dart';
import 'package:bus/helper/network_serviecs.dart';
import 'package:flutter/material.dart';

class TripRouteRepo {
  final _dio = NetworkService();

  /// Fetches the previous route for a trip
  /// 
  /// [tripId] is the ID of the trip to get the route for
  Future<TripRouteResponse> getTripRoute({
    required String tripId,
  }) async {
    try {
      final request = await _dio.get(
        url: "general/trips/routes/$tripId",
        isAuth: true,
      );

      debugPrint("Trip route API response: ${request.statusCode}");

      if (request.statusCode == 200) {
        return TripRouteResponse.fromJson(request.data);
      } else {
        // Handle non-200 status codes
        return TripRouteResponse(
          status: false,
          message: request.data['message'] ?? "Failed to fetch trip route",
          data: null,
        );
      }
    } catch (e, s) {
      // Log the error for debugging
      debugPrint("Error fetching trip route: $e");
      debugPrint("Stack trace: $s");

      // Return error response
      return TripRouteResponse(
        status: false,
        message: "Failed to fetch trip route: $e",
        data: null,
      );
    }
  }
}
