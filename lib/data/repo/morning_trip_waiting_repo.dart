import 'package:bus/data/models/trip_models/morning_trip_waiting_model.dart';
import 'package:bus/helper/network_serviecs.dart';
import 'package:flutter/material.dart';

class MorningTripWaitingRepo {
  final _dio = NetworkService();

  /// Fetches students waiting for the morning trip
  /// 
  /// [busId] is the ID of the bus to get waiting students for
  Future<MorningTripWaitingResponse> getWaitingStudents({
    required String busId,
  }) async {
    try {
      final request = await _dio.get(
        url: "trips/morning/waiting/$busId",
        isAuth: true,
      );

      debugPrint("Morning trip waiting students API response: ${request.statusCode}");

      if (request.statusCode == 200) {
        return MorningTripWaitingResponse.fromJson(request.data);
      } else {
        // Handle non-200 status codes
        return MorningTripWaitingResponse(
          status: false,
          message: request.data['message'] ?? "Failed to fetch waiting students",
          waiting: [],
        );
      }
    } catch (e, s) {
      // Log the error for debugging
      debugPrint("Error fetching morning trip waiting students: $e");
      debugPrint("Stack trace: $s");

      // Return error response
      return MorningTripWaitingResponse(
        status: false,
        message: "Failed to fetch waiting students: $e",
        waiting: [],
      );
    }
  }
}
