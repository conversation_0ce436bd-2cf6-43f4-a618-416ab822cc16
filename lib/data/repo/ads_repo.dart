import 'package:flutter/material.dart';

import '../../config/config_base.dart';
import '../../helper/network_serviecs.dart';
import '../models/ads_model.dart';

class AdsRepo {
  final _dio = NetworkService();

  Future<AdsModel> ads() async {
    try {
      final request = await _dio.get(
        url: ConfigBase.ads,
        isAuth: true,
      );
      // Logger().w(request.data);
      AdsModel? adsModel;
      if (request.statusCode == 200) {
        adsModel = AdsModel.fromMap(request.data);
      } else {
        adsModel = AdsModel.fromMap(request.data);
      }
      return adsModel;
    } catch (e) {
      debugPrint("catch error $e");
      return AdsModel(message: e.toString());
    }
  }
}
