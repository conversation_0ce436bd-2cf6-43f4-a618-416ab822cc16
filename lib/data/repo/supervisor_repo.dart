import 'package:bus/config/config_base.dart';
import 'package:bus/data/models/supervisor_mdodels/supervisor_models.dart';
import 'package:bus/helper/network_serviecs.dart';
import 'package:flutter/material.dart';

class SupervisorRepo {
  final _dio = NetworkService();

  Future<SupervisorModels> repo({
    int? pageNumber,
  }) async {
    try {
      final _request = await _dio.get(
        url: "${ConfigBase.supervisor}?page=$pageNumber&limit=10",
        isAuth: true,
      );
      // Logger().wtf(_request.data);
      SupervisorModels? supervisorModels;
      if (_request.statusCode == 200) {
        // (_request.data["data"]['data'] as List<dynamic>).forEach((element) {
        //   var data =
        //       SupervisorInfoModels.fromJson(element as Map<String, dynamic>);
        //   Logger().e(data);
        //   debugPrint(data.toString());
        // });

        supervisorModels = SupervisorModels.fromJson(_request.data);
      } else {
        supervisorModels = SupervisorModels.fromJson(_request.data);
      }
      return supervisorModels;
    } catch (e, stackTrace) {
      debugPrint(stackTrace.toString());
      debugPrint("catch error at student repo $e");
      return SupervisorModels(message: e.toString());
    }
  }
}
