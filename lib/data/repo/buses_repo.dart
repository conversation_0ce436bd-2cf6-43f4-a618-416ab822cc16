import 'package:bus/config/config_base.dart';
import 'package:bus/data/models/buses_models/buses_models.dart';
import 'package:bus/helper/network_serviecs.dart';
import 'package:dio/dio.dart';
import 'package:flutter/material.dart';

import '../../config/global_variable.dart';
import '../models/buses_models/buses_data_models.dart';

class BusesRepo {
  final _dio = NetworkService();

  Future<BusesModel> repo({
    int? pageNumber,
  }) async {
    try {
      final _request = await _dio.get(
        url: "${ConfigBase.buses}?page=$pageNumber&limit=10",
        isAuth: true,
      );
      debugPrint(_request.data["data"].toString());
      BusesModel? busesModels = BusesModel.fromJson(_request.data);
      return busesModels;
    } catch (e, stackTrace) {
      debugPrint("stackTrace $stackTrace");
      debugPrint("catch error $e");
      rethrow;
    }
  }

  Future<BusesDataModels> allBusesRepo() async {
    try {
      final _request = await _dio.get(
        url: "${ConfigBase.buses}/get",
        isAuth: true,
      );
      BusesDataModels? busesDataModels;
      if (_request.statusCode == 200) {
        busesDataModels = BusesDataModels.fromJson(_request.data);
      } else {
        busesDataModels = BusesDataModels.fromJson(_request.data);
      }
      return busesDataModels;
    } catch (e, stackTrace) {
      debugPrint("stackTrace $stackTrace");
      debugPrint("catch error $e");
      return BusesDataModels(message: e.toString());
    }
  }

  Future<bool> removeStudentFromBus({
    required List<String> studentIds,
    String? busId,
  }) async {
    var dio = Dio();
    var formData = FormData();

    // Add student_ids as separate fields
    for (var id in studentIds) {
      formData.fields.add(MapEntry('student_id[]', id.toString()));
    }
    formData.fields.add(MapEntry('bus_id', '$busId'));

    final authHeaders = {'Authorization': "Bearer $token"};
    try {
      final _request = await dio.post(
        "${ConfigBase.baseUrl}buses/remove/students",
        data: formData,
        options: Options(
          headers: authHeaders,
        ),
      );
      debugPrint('Response status: ${_request.statusCode}');
      debugPrint('Response data: ${_request.data}');
      debugPrint(_request.data.toString());
      return !_request.data["errors"];
    } catch (e, stackTrace) {
      debugPrint("stackTrace $stackTrace");
      debugPrint("catch error $e");
      return false;
    }
  }
}
