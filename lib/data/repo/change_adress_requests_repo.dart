import 'package:bus/bloc/change_address_cubit/change_address_states.dart';
import 'package:bus/config/config_base.dart';
import 'package:bus/helper/network_serviecs.dart';
import 'package:flutter/material.dart';
import 'package:logger/logger.dart';

class CAdreessRepo {
  final _dio = NetworkService();

  Future<bool> acceptChangeAddressRequest({
    int? changeAddressRequestId,
  }) async {
    try {
      final _request = await _dio.get(
        url: "${ConfigBase.acceptChangeAddressRequests}$changeAddressRequestId",
        isAuth: true,
      );
      if (_request.statusCode == 200) {
        return true;
      } else {
        return false;
      }
    } catch (e) {
      debugPrint("catch error $e");
      return false;
    }
  }

  Future<bool> refuseChangeAddressRequest(
      {int? changeAddressRequestId, required String text}) async {
    try {
      final _request = await _dio.get(
        url: "${ConfigBase.refuseChangeAddressRequests}$changeAddressRequestId",
        isAuth: true,
      );
      if (_request.statusCode == 200) {
        Logger().e(_request.data);
        return true;
      } else {
        return false;
      }
    } catch (e) {
        Logger().e(e);

      debugPrint("catch error $e");
      return false;
    }
  }

  Future<List<dynamic>> repo({
    int? pageNumber,
    String? status,
  }) async {
    try {
      int lastPage = 1, currentPage = 1;
      final _request = await _dio.get(
        url: status == null
            ? "${ConfigBase.change_address_requests}?page=$pageNumber"
            : "${ConfigBase.change_address_requests}?page=$pageNumber&status=$status",
        isAuth: true,
      );
      Logger().e(status);
      Logger().w(_request.data);
      List<CAdreessModels?> cAdreessModels1 = [];
      if (_request.statusCode == 200) {
        Logger().wtf(
            _request.data["data"]["request_addresses"]["data"]);
        for (var element in (_request.data["data"]["request_addresses"]["data"]
            as List<dynamic>)) {
          cAdreessModels1.add(CAdreessModels.fromMap(element));
        }
        lastPage = _request.data["data"]["request_addresses"]["last_page"];
        currentPage =
            _request.data["data"]["request_addresses"]["current_page"];
      } else {}
      return [cAdreessModels1, currentPage, lastPage];
    } catch (e) {
      debugPrint("catch error $e");
      return [];
    }
  }
}
