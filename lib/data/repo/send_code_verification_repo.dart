import 'package:bus/config/config_base.dart';
import 'package:bus/config/global_variable.dart';
import 'package:bus/data/models/send_verificiation_code_models/send_verification_code_models.dart';
import 'package:bus/helper/network_serviecs.dart';
import 'package:logger/logger.dart';

class SendCodeVerificationRepo {
  final _dio = NetworkService();

  Future<SendCodeVerificationModels> repo({
    String? smsCode,
  }) async {
    Logger().w(smsCode);
      Logger().w(tempToken);

    final  _request = await _dio.post(
      url: ConfigBase.verificationCode,
      body: {
        "token": smsCode,
        "firebase_token": fCMToken
      },
      headers: {'Authorization': "Bearer $tempToken"}
    );
    Logger().w(_request.data);
    SendCodeVerificationModels? sendCodeVerificationModels;
    if (_request.statusCode == 200) {
      sendCodeVerificationModels =
          SendCodeVerificationModels.fromJson(_request.data);
      sendCodeVerificationModels.status = true;
    } else {
      sendCodeVerificationModels =
          SendCodeVerificationModels.fromJson(_request.data);
      sendCodeVerificationModels.status = false;
    }
    return sendCodeVerificationModels;
  }
}
