import 'package:bus/config/config_base.dart';
import 'package:bus/data/models/trip_models/previous_trip_model.dart';
import 'package:bus/helper/network_serviecs.dart';
import 'package:flutter/material.dart';

class PreviousTripsRepo {
  final _dio = NetworkService();

  Future<PreviousTripsResponse> getPreviousTrips({
    int? pageNumber,
    String? busId,
    String? date,
    String? tripId,
    String? sort,
    String? search,
  }) async {
    try {
      // Build query parameters
      Map<String, dynamic> queryParams = {};

      if (pageNumber != null) {
        queryParams['page'] = pageNumber.toString();
      }

      if (busId != null && busId.isNotEmpty && busId != "0") {
        queryParams['bus_id'] = busId;
      }

      if (date != null && date.isNotEmpty) {
        queryParams['date'] = date;
      }

      if (tripId != null && tripId.isNotEmpty) {
        queryParams['trip_id'] = tripId;
      }

      if (sort != null && sort.isNotEmpty) {
        queryParams['sort'] = sort;
      }

      if (search != null && search.isNotEmpty) {
        queryParams['search'] = search;
      }

      // Make the API call
      final request = await _dio.get(
        url: ConfigBase.schoolPreviousTrips,
        queryParameters: queryParams,
        isAuth: true,
      );

      debugPrint("Previous trips API response: ${request.statusCode}");

      if (request.statusCode == 200) {
        debugPrint("Response data: ${request.data}");

        // Check for the new API response format with 'previous_trips'
        if (request.data is Map<String, dynamic> &&
            request.data.containsKey('previous_trips')) {
          return PreviousTripsResponse.fromJson(request.data);
        }
        // If the response has a data field that is a list (old format)
        else if (request.data is Map<String, dynamic> &&
            request.data.containsKey('data') &&
            request.data['data'] is List) {
          return PreviousTripsResponse(
            status: true,
            message: request.data['message'] ??
                "Previous trips fetched successfully",
            data: (request.data['data'] as List)
                .map((item) => PreviousTripModel.fromJson(item))
                .toList(),
          );
        }
        // If the response is a list (multiple trips)
        else if (request.data is List) {
          return PreviousTripsResponse(
            status: true,
            message: "Previous trips fetched successfully",
            data: (request.data as List)
                .map((item) => PreviousTripModel.fromJson(item))
                .toList(),
          );
        }
        // If the response is a single trip object
        else if (request.data is Map<String, dynamic>) {
          // Check if this is a response wrapper or a direct trip object
          if (request.data.containsKey('status') &&
              request.data.containsKey('message')) {
            // This is a response wrapper
            return PreviousTripsResponse.fromJson(request.data);
          } else {
            // This is a direct trip object
            return PreviousTripsResponse(
              status: true,
              message: "Previous trip fetched successfully",
              data: [PreviousTripModel.fromJson(request.data)],
            );
          }
        }
        // Unexpected response format
        else {
          return PreviousTripsResponse(
            status: false,
            message: "Unexpected response format",
            data: [],
          );
        }
      } else {
        // Handle non-200 status codes
        return PreviousTripsResponse(
          status: false,
          message: request.data['error'] ?? "Failed to fetch previous trips",
          data: [],
        );
      }
    } catch (e, s) {
      // Log the error for debugging
      debugPrint("Error fetching previous trips: $e");
      debugPrint("Stack trace: $s");

      // Return error response
      return PreviousTripsResponse(
        status: false,
        message: "Failed to fetch previous trips: $e",
        data: [],
      );
    }
  }
}
