import 'dart:io';

import 'package:bus/config/config_base.dart';
import 'package:bus/data/models/update_profile_models/update_profile_models.dart';
import 'package:bus/helper/data_state.dart';
import 'package:bus/helper/network_serviecs.dart';
import 'package:dio/dio.dart';
import 'package:open_street_map_search_and_pick/open_street_map_search_and_pick.dart';

class UpdateProfileRepo {
  final _dio = NetworkService();

  Future<DataState<UpdateProfileModels>> repo({
    String? email,
    String? address,
    String? cityName,
    String? currentPassword,
    String? phone,
    String? name,
    LatLong? position,
    File? image,
  }) async {
    FormData formData = FormData.fromMap({
      "email": email,
      "address": address,
      "city_name": cityName,
      "current_password": currentPassword,
      "phone": phone,
      "name": name,
      "latitude": '${position?.latitude}',
      "longitude": '${position?.longitude}',
      "logo": image != null ? await MultipartFile.fromFile(image.path) : "",
    });
    final _request = await _dio.post(
      url: ConfigBase.updateProfile,
      body: formData,
      isAuth: true,
    );
    // Logger().w(_request.data);
    if (_request.statusCode == 200) {
      return DataSuccess(
        UpdateProfileModels.fromJson(_request.data),
      );
    } else {
      return const DataFailed();
    }
  }
}
