import 'dart:developer';
import 'package:bus/config/config_base.dart';
import 'package:bus/data/models/add_bus_models/add_bus_models.dart';
import 'package:bus/helper/network_serviecs.dart';
import 'package:dio/dio.dart';
import 'package:flutter/material.dart';


class AddBusRepo {
  final _dio = NetworkService();

  Future<AddBusModels> repo({
    String? name,
    String? notes,
    String? car_number,
  }) async {
    Map<String, dynamic> datamap = {
      "name": name,
      "notes": notes,
      "car_number": car_number,
    };

    log("datamap:${datamap.toString()}  ");
    FormData formData = FormData.fromMap(datamap);

    try {
      final _request = await _dio.post(
        url: "${ConfigBase.baseUrl}buses/store",
        body: formData,
        isAuth: true,
      );
      // debugPrint(_request.data);
      // Logger().wtf(_request.data);
      // debugPrint("student from repo ${_request.statusCode}");
      AddBusModels? addBusModels;
      if (_request.statusCode == 200) {
        addBusModels = AddBusModels.fromJson(_request.data);
      } else {
        addBusModels = AddBusModels.fromJson(_request.data);
      }
      return addBusModels;
    }catch (e, s) {
      debugPrint("catch error $s");
      debugPrint("catch error $e");
      return AddBusModels(message: e.toString());
    }
  }
}
