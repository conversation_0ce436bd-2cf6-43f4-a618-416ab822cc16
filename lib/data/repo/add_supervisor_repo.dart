import 'dart:developer';
import 'dart:io';
import 'package:bus/config/config_base.dart';
import 'package:bus/data/models/add_driver_models/add_driver_models.dart';
import 'package:bus/helper/network_serviecs.dart';
import 'package:dio/dio.dart';
import 'package:flutter/material.dart';

class AddSupervisorRepo {
  final _dio = NetworkService();

  Future<AddDriverModels> repo({
    String? name,
    String? fileName,
    String? username,
    int? genderId,
    int? religionId,
    int? typeBloodId,
    int? busId,
    String? birthDate,
    String? password,
    String? password_confirmation,
    String? address,
    String? cityName,
    String? phone,
    File? image,
  }) async {
    MultipartFile? file;
    if (image != null) {
      try {
        fileName = image.path.split('/').last;
        file = await MultipartFile.fromFile(image.path, filename: fileName);
      } catch (e, s) {
        debugPrint("catch error $s");
        debugPrint(e.toString());
      }
    }

    Map<String, dynamic> datamap = {
      "name": name,
      "phone": phone,
      "username": username,
      "Date_Birth": birthDate,
      "city_name": cityName,
      "type__blood_id": typeBloodId == 0 ? null : typeBloodId,
      "bus_id": busId == 0 ? null : busId.toString(),
      "address": address,
      "gender_id": genderId == 0 ? null : genderId.toString(),
      "religion_id": religionId == 0 ? null : religionId.toString(),
    }..removeWhere((key, value) => value == null || value == "" || value == 0);

    if (file != null) {
      datamap["logo"] = file;
    }

    if (password != null && password.isNotEmpty) {
      datamap["password"] = password;
    }

    if (password_confirmation != null && password_confirmation.isNotEmpty) {
      datamap["password_confirmation"] = password_confirmation;
    }

    log("datamap: ${datamap.toString()}");
    FormData formData = FormData.fromMap(datamap);

    try {
      final _request = await _dio.post(
        url: "${ConfigBase.baseUrl}attendants/store/admins",
        body: formData,
        isAuth: true,
      );

      AddDriverModels? addDriverModels;
      if (_request.statusCode == 200) {
        addDriverModels = AddDriverModels.fromJson(_request.data);
      } else {
        addDriverModels =
            AddDriverModels(messages: _request.data['messages']['username'][0]);
      }
      return addDriverModels;
    } catch (e, s) {
      debugPrint("catch error $s");
      debugPrint("catch error $e");
      return AddDriverModels(message: e.toString());
    }
  }
}
