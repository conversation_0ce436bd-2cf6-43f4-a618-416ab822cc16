import 'package:bus/config/config_base.dart';
import 'package:bus/config/global_variable.dart';
import 'package:bus/data/models/create_password/create_password_model.dart';
import 'package:bus/helper/cache_helper.dart';
import 'package:bus/helper/data_state.dart';
import 'package:bus/helper/network_serviecs.dart';
import 'package:dio/dio.dart';
import 'package:flutter/material.dart';

class CreatePasswordRepo {
  final _dio = NetworkService();
  final _customDio = Dio();

  Future<DataState<CreatePasswordModel>> createPassword({
    String? newPassword,
    String? confirmedPassword,
  }) async {
    try {
      // Use a custom Dio instance with a different baseUrl
      _customDio.options.baseUrl = ConfigBase.baseUrl; // Use base without "schools"

      // Add logging interceptor
      _customDio.interceptors.add(LogInterceptor(
        request: true,
        requestHeader: true,
        requestBody: true,
        responseHeader: true,
        responseBody: true,
        logPrint: (log) => debugPrint(log.toString()),
      ));

      // Add auth token to headers
      final headers = {
        'Authorization': "Bearer ${_dio.dio.options.headers['Authorization'] ?? token}",
        'ln': CacheHelper.getString('lang') == 'ar' ? 'ar' : 'en',
      };

      // Make the request to the correct endpoint
      final response = await _customDio.post(
        '/new-password', // API endpoint for creating password without "schools"
        data: {
          "password": newPassword,
          "password_confirmation": confirmedPassword,
        },
        options: Options(headers: headers),
      );

      if (response.statusCode == 200) {
        return DataSuccess(
          CreatePasswordModel.fromJson(response.data),
        );
      } else {
        return DataFailed(
          message: response.data["status"]?["messages"] ?? "حدث خطأ ما، يرجى المحاولة مرة أخرى"
        );
      }
    } catch (e) {
      debugPrint("CreatePasswordRepo error: $e");
      return const DataFailed(
        message: "حدث خطأ ما، يرجى المحاولة مرة أخرى"
      );
    }
  }
}
