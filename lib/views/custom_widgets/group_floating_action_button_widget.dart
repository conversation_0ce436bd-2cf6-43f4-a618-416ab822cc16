import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import '../../config/theme_colors.dart';
import 'custom_text.dart';

class GroupFloatingActionButtonWidget extends StatelessWidget {
  const GroupFloatingActionButtonWidget({
    super.key,
    required this.btnTitle,
    required this.iconBtn,
    required this.onTap,
    required this.heroTag,
  });

  final void Function()? onTap;
  final String btnTitle;
  final IconData? iconBtn;
  final String heroTag;

  @override
  Widget build(BuildContext context) {
    return Row(
      // mainAxisAlignment: MainAxisAlignment.end,
      children: [
        FloatingActionButton(
          heroTag: heroTag,
          onPressed: onTap,
          backgroundColor: TColor.mainColor,
          child: Icon(iconBtn),
        ),
        5.horizontalSpace,
        GestureDetector(
          onTap: onTap,
          child: Container(
            padding: const EdgeInsets.all(5.0),
            margin: const EdgeInsets.all(5.0),
            decoration: BoxDecoration(
              color: Colors.white24,
              borderRadius: BorderRadius.circular(5.0),
              boxShadow: [
                BoxShadow(
                  color: Colors.grey.withOpacity(0.5),
                  spreadRadius: 5,
                  blurRadius: 7,
                  offset: const Offset(0, 3),
                ),
              ],
            ),
            child: CustomText(
              text: btnTitle,
              color: TColor.mainColor,
              fontW: FontWeight.w700,
              fontSize: 16,
            ),
          ),
        ),
      ],
    );
  }
}
