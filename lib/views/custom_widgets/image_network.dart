import 'package:bus/config/theme_colors.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';


class ImageNetwork extends StatelessWidget {
  final String? url;
  final double? height;
  final double? width;
  final Widget? errorWidget;
  final bool cover;
  final bool fill;
  final BoxFit boxFitStatic;

  const ImageNetwork({
    super.key,
    required this.url,
    this.width,
    this.height,
    this.errorWidget,
    this.cover = false,
    this.fill = false,
    this.boxFitStatic = BoxFit.fill,
  });

  @override
  Widget build(BuildContext context) {
    // return Image.asset(
    //   assetsImages(url!),
    //   fit: boxFitStatic,
    //   height: height,
    //   width: width,
    // );
    return CachedNetworkImage(
      height: height,
      width: width,
      imageUrl: url ?? " ",
      fit: boxFitStatic,
      errorWidget: (context, url, error) => errorWidget != null
          ? errorWidget!
          : const Center(
              child: Icon(
                Icons.error,
                color: TColor.error,
              ),
            ),
    );
  }
}
