 import 'package:bus/config/theme_colors.dart';
import 'package:bus/translations/local_keys.g.dart';
import 'package:bus/views/custom_widgets/custom_text.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class CustomFormFieldWithBorder extends StatelessWidget {
  final String? hintText;
  final bool security;
  final TextInputType? inputType;
  final String? validation;
  final Function(dynamic)? saved;
  final int maxLine;
  final Widget? prefix;
  final Widget? suffix;
  final double? radiusNumber;
  final Function(String)? onChanged;
  final void Function(String)? onFieldSubmitted;

  final Function()? onComplete;
  final int? requierdNumber;
  final bool isTitled;
  final double? paddingLeft;
  final double? paddingRight;
  final double? formFieldWidth;
  final double? contentPaddingVertical;
  final double? contentPaddingHorizontal;
  final Color? fillColor;
  final Color? borderColor;
  final bool? height;
  final double? heightA;
  final String? title;
  final TextEditingController? controller;
  final String? Function(String?)? validatorFunc;
  final bool checkValidatorFunc;
  final bool readOnly;
  final Color? titleColor;
  final TextCapitalization textCapitalization;

  const CustomFormFieldWithBorder({
    this.isTitled = false,
    this.controller,
    this.onComplete,
    super.key,
    this.hintText,
    this.validatorFunc,
    this.checkValidatorFunc = false,
    this.inputType = TextInputType.text,
    this.saved,
    this.validation,
    this.security = false,
    this.maxLine = 1,
    this.prefix,
    this.suffix,
    this.radiusNumber = 5,
    this.onChanged,
    this.fillColor = TColor.fillFormField,
    this.paddingLeft = 20,
    this.paddingRight = 20,
    this.formFieldWidth = 328,
    this.contentPaddingVertical = 0,
    this.contentPaddingHorizontal = 10,
    this.borderColor = TColor.black,
    this.height = false,
    this.heightA = 48,
    this.requierdNumber = 0,
    this.title,
    this.onFieldSubmitted,
    this.readOnly = false,
    this.titleColor = TColor.black,
    this.textCapitalization = TextCapitalization.none,
  });
  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        isTitled
            ? Padding(
                padding: context.locale.toString() == "ar"
                    ? EdgeInsets.only(
                        left: paddingLeft!.w,
                        right: paddingRight!.w,
                        bottom: 10.h)
                    : EdgeInsets.only(
                        left: paddingLeft!.w,
                        right: paddingRight!.w,
                        bottom: 10.h),
                child: CustomText(
                  text: title,
                  fontSize: 15,
                  textAlign: TextAlign.center,
                  fontW: FontWeight.bold,
                  color: titleColor ?? TColor.black,
                ),
              )
            : Container(),
        Padding(
          padding: context.locale.toString() == "ar"
              ? EdgeInsets.only(left: paddingLeft!.w, right: paddingRight!.w)
              : EdgeInsets.only(left: paddingLeft!.w, right: paddingRight!.w),
          child: SizedBox(
            width: formFieldWidth!.w,
            // height: height == true ? 46.21.w : heightA!.w,
            child: TextFormField(
              readOnly: readOnly,
              onFieldSubmitted: onFieldSubmitted,
              controller: controller,
              onEditingComplete: onComplete,
              onChanged: onChanged,
              textCapitalization: textCapitalization,
              decoration: InputDecoration(
                prefixIcon: prefix,
                suffixIcon: suffix,
                filled: true,
                fillColor: fillColor,
                contentPadding: EdgeInsets.symmetric(
                    vertical: contentPaddingVertical!,
                    horizontal: contentPaddingHorizontal!),
                hintText: hintText,
                hintStyle: const TextStyle(
                  color: TColor.tabColors,
                ),
                enabledBorder: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(radiusNumber!),
                    borderSide: BorderSide(color: borderColor!, width: 1)),
                focusedBorder: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(radiusNumber!),
                    borderSide: BorderSide(color: borderColor!, width: 1)),
                border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(radiusNumber!),
                    borderSide: BorderSide(color: borderColor!, width: 1)),
                errorBorder: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(radiusNumber!),
                    borderSide: BorderSide(color: borderColor!, width: 1)),
              ),
              validator: checkValidatorFunc
                  ? validatorFunc
                  : (value) {
                      try {
                        if (value!.isEmpty) {
                          return validation;
                        } else if (value.length < requierdNumber!) {
                          return AppStrings.fieldMinLength.tr(namedArgs: {'count': requierdNumber.toString()});
                        }
                        return null;
                      } catch (e) {
                        return AppStrings.notValid.tr();
                      }
                    },
              onSaved: saved,
              obscureText: security,
              maxLines: maxLine,
              keyboardType: inputType,
            ),
          ),
        ),
      ],
    );
  }
}
