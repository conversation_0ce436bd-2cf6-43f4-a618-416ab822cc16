import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class CustomDropDownButton<T> extends StatelessWidget {
  const CustomDropDownButton({
    Key? key,
    required this.items,
    required this.onChanged,
    required this.value,
    this.hint,
  }) : super(key: key);

  final T value;
  final Widget? hint;
  final List<DropdownMenuItem<T>>? items;
  final Function(T?) onChanged;

  @override
  Widget build(BuildContext context) {
    return Container(
      width: double.infinity,
      padding: EdgeInsets.symmetric(horizontal: 15.w),
      margin: EdgeInsets.symmetric(horizontal: 37.w),
      decoration: BoxDecoration(
        border: Border.all(
            color: const Color(0xFF3F51B5).withAlpha(100), width: 1.5),
        borderRadius: BorderRadius.circular(12.r),
      ),
      child: DropdownButton<T>(
        isExpanded: true,
        hint: hint,
        underline: const SizedBox(),
        value: value,
        items: items,
        onChanged: onChanged,
      ),
    );
  }
}
