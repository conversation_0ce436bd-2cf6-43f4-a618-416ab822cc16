import 'package:flutter/material.dart';

import '../../config/theme_colors.dart';

class StudentFloatingActionWidget extends StatelessWidget {
  const StudentFloatingActionWidget({
    super.key,
    required this.onPressed,
    required this.opacity,
    required this.children,
  });

  final Animation<double> opacity;
  final List<Widget> children;
  final void Function()? onPressed;

  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisAlignment: MainAxisAlignment.end,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        FadeTransition(
          opacity: opacity,
          child: SizeTransition(
            sizeFactor: opacity,
            child: Column(children: children),
          ),
        ),
        FloatingActionButton(
          heroTag: '0',
          onPressed: onPressed,
          backgroundColor: TColor.mainColor,
          child: const Icon(Icons.menu),
        ),
      ],
    );
  }
}