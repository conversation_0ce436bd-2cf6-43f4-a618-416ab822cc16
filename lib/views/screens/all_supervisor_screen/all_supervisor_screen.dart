import 'package:bus/bloc/delete_driver_supervisor_cubit/delete_driver_supervisor_cubit.dart';
import 'package:bus/bloc/supervisor_cubit/supervisor_cubit.dart';
import 'package:bus/bloc/supervisor_cubit/supervisor_states.dart';
import 'package:bus/constant/path_route_name.dart';
import 'package:bus/utils/helpers.dart';
import 'package:bus/views/custom_widgets/custom_text.dart';
import 'package:bus/views/screens/add_supervisor_screen/add_supervisor_screen.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:url_launcher/url_launcher.dart';

import '../../../config/theme_colors.dart';
import 'package:bus/translations/local_keys.g.dart';
import '../../../utils/assets_utils.dart';
import '../../../utils/sized_box.dart';
import '../../../widgets/custom_appbar.dart';
import '../../../widgets/student_widgets/custom_search_w.dart';
import '../../custom_widgets/build_table_row_widget.dart';
import '../show_bus_on_map_screen/show_bus_on_map_screen.dart';

class AllSupervisorScreen extends StatefulWidget {
  static const String routeName = PathRouteName.allSupervisor;

  const AllSupervisorScreen({Key? key}) : super(key: key);

  @override
  State<AllSupervisorScreen> createState() => _AllSupervisorScreenState();
}

class _AllSupervisorScreenState extends State<AllSupervisorScreen> {
  final ScrollController? scrollController = ScrollController();

  @override
  void initState() {
    context.read<SupervisorCubit>().initScrollController(
        isFirst: false,
        scrollController: scrollController,
        setStates: () {
          setState(() {});
        });
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: CustomAppBar(
        titleWidget: CustomText(
          text: AppStrings.allSupervisors.tr(),
          fontSize: 18,
          textAlign: TextAlign.center,
          fontW: FontWeight.w600,
          color: TColor.white,
        ),
        leftWidget: context.locale.toString() == "ar"
            ? InkWell(
                onTap: () {
                  Navigator.pop(context);
                },
                child: SvgPicture.asset(AppAssets.arrowBack),
              )
            : InkWell(
                onTap: () {
                  Navigator.pop(context);
                },
                child: SvgPicture.asset(
                  AppAssets.forwardArrow,
                  colorFilter:
                      const ColorFilter.mode(TColor.white, BlendMode.srcIn),
                  width: 25.w,
                  height: 25.w,
                ),
              ),
      ),
      body: SingleChildScrollView(
        controller: scrollController,
        child: Column(
          children: [
            CustomSearchW(
              hintText: AppStrings.searchSupervisorHint.tr(),
              type: "supervisor",
            ),
            BlocBuilder<SupervisorCubit, SupervisorStates>(
              buildWhen: (previous, current) =>
                  current is SupervisorSuccessStates,
              builder: (context, states) {
                if (states is SupervisorLoadingStates) {
                  return const Center(
                    child: CircularProgressIndicator(
                      color: TColor.mainColor,
                    ),
                  );
                } else if (states is SupervisorSuccessStates) {
                  if (states.supervisorModels!.data!.data!.isEmpty) {
                    return SizedBox(
                      width: 300.w,
                      height: 300.w,
                      child: Center(
                        child: CustomText(
                          text: AppStrings.supervisorNotFound.tr(),
                          fontSize: 17,
                          fontW: FontWeight.w600,
                        ),
                      ),
                    );
                  } else {
                    return Column(
                      children: [
                        Padding(
                          padding: const EdgeInsets.all(8.0),
                          child: Container(
                            margin: const EdgeInsets.symmetric(horizontal: 8.0),
                            clipBehavior: Clip.antiAlias,
                            decoration: BoxDecoration(
                              borderRadius: BorderRadius.circular(15.0),
                            ),
                            child: Table(
                              columnWidths: const {
                                0: FlexColumnWidth(2.5),
                                1: FlexColumnWidth(2),
                                2: FlexColumnWidth(1),
                                3: FlexColumnWidth(1),
                              },
                              border: TableBorder.all(
                                color: TColor.tabColors,
                                borderRadius: BorderRadius.circular(15.0),
                              ),
                              children: List.generate(
                                1 +
                                    context
                                        .watch<SupervisorCubit>()
                                        .data
                                        .length,
                                (index) {
                                  // Logger().wtf(states.supervisorModels!.data!.data![index].address);
                                  if (index == 0) {
                                    return BuildTableRowWidget(
                                      cell: [
                                        AppStrings.name.tr(),
                                        AppStrings.busName.tr(),
                                        AppStrings.call.tr(),
                                        AppStrings.options.tr(),
                                      ],
                                      header: true,
                                    ).build(context);
                                  } else {
                                    final newSupervisor = context
                                        .watch<SupervisorCubit>()
                                        .data[index - 1];
                                    debugPrint(
                                        "***type: ${newSupervisor.type}");

                                    return BuildTableRowWidget(
                                        is2Icon: true,
                                        isTabDown: true,
                                        cell: [
                                          newSupervisor.name,
                                          newSupervisor.bus?.name ??
                                              AppStrings.notFound.tr(),
                                          Icons.call,
                                          Icons.more_horiz,
                                        ],
                                        onTapDown: (position) {
                                          Helpers.customShowDialog(context,
                                              position: position.globalPosition,
                                              onTapShow: () {
                                            Navigator.of(context)
                                              ..pop()
                                              ..pushNamed(
                                                PathRouteName.supervisorData,
                                                arguments: newSupervisor,
                                              );
                                          }, onTapEdit: () {
                                            Navigator.of(context)
                                              ..pop()
                                              ..push(MaterialPageRoute(
                                                  builder: (ctx) {
                                                return AddSupervisorScreen(
                                                  isEdit: true,
                                                  supervisor: newSupervisor,
                                                );
                                              }));
                                          }, onTapDelete: () {
                                            context
                                                .read<
                                                    DeleteDriverSupervisorCubit>()
                                                .deleteDriverSupervisor(
                                                    id: newSupervisor.id)
                                                .whenComplete(() {
                                              Navigator.pop(context);
                                              context
                                                  .read<SupervisorCubit>()
                                                  .getSupervisor(
                                                      pageNumber: 1,
                                                      isFirst: true);
                                              // BlocProvider.of<DriverCubit>(context).getDriver();
                                            });
                                          }, onTapLocation: () {
                                            Navigator.of(context)
                                              ..pop()
                                              ..push(MaterialPageRoute(
                                                  builder: (ctx) {
                                                return ShowBusOnMapScreen(
                                                  name: newSupervisor.name!,
                                                  busName:
                                                      newSupervisor.bus!.name!,
                                                  busId: newSupervisor.bus!.id
                                                      .toString(),
                                                  type: newSupervisor.type!,
                                                );
                                              }));
                                          });
                                        },
                                        onTapBeforeLastCell: () async {
                                          //Call
                                          if (!await launchUrl(Uri.parse(
                                              "tel:${newSupervisor.phone}"))) {
                                            throw Exception(
                                                'Could not launch url');
                                          }
                                        }).build(context);
                                  }
                                },
                              ),
                            ),
                          ),
                        ),
                        const SBox(h: 10.0),
                        context.watch<SupervisorCubit>().currentPage! <
                                context.watch<SupervisorCubit>().last_pages!
                            ? Icon(
                                Icons.arrow_downward_rounded,
                                size: 40.sp,
                              )
                            : const SizedBox.shrink(),
                        // PageNumberWidget(
                        //   lastPage: states.supervisorModels!.data!.last_page,
                        //   currentPage:
                        //       states.supervisorModels!.data!.current_page,
                        //   type: "supervisor",
                        // ),
                      ],
                    );
                  }
                } else if (states is SupervisorErrorStates) {
                  if (states.error == "admins not found") {
                    return SizedBox(
                      width: 300.w,
                      height: 300.w,
                      child: Center(
                        child: CustomText(
                          text: AppStrings.supervisorNotFound.tr(),
                          fontSize: 17,
                          fontW: FontWeight.w600,
                        ),
                      ),
                    );
                  } else {
                    return SizedBox(
                      width: 300.w,
                      height: 300.w,
                      child: Center(
                        child: CustomText(
                          text: AppStrings.supervisorNotFound.tr(),
                          fontSize: 17,
                          fontW: FontWeight.w600,
                        ),
                      ),
                    );
                  }
                } else {
                  return const SizedBox();
                }
              },
            ),
            const SBox(h: 50),
          ],
        ),
      ),
    );
  }
}
