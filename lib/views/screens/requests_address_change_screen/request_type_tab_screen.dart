import 'package:bus/translations/local_keys.g.dart';
import 'package:bus/views/custom_widgets/custom_text.dart';
import 'package:bus/views/screens/requests_address_change_screen/requests_address_change_screen.dart';
import 'package:bus/views/screens/requests_address_change_screen/temporary_address_screen.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

/// شاشة تعرض نوع معين من الطلبات مع تاب بار سفلي للتبديل بين الدائم والمؤقت
class RequestTypeTabScreen extends StatefulWidget {
  final String? status;
  final String? appBarTitle;
  final Color tabColor;

  const RequestTypeTabScreen({
    super.key,
    required this.status,
    required this.appBarTitle,
    required this.tabColor,
  });

  @override
  State<RequestTypeTabScreen> createState() => _RequestTypeTabScreenState();
}

class _RequestTypeTabScreenState extends State<RequestTypeTabScreen> with TickerProviderStateMixin {
  late TabController _innerTabController;

  @override
  void initState() {
    super.initState();
    _innerTabController = TabController(length: 2, vsync: this);
  }

  @override
  void dispose() {
    _innerTabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        // تاب بار سفلي للتبديل بين الدائم والمؤقت
        Container(
          margin: EdgeInsets.only(top: 8.h, bottom: 8.h, left: 16.w, right: 16.w),
          decoration: BoxDecoration(
            color: widget.tabColor.withOpacity(0.1),
            borderRadius: BorderRadius.circular(20.r),
            boxShadow: [
              const BoxShadow(
                color: Color.fromRGBO(158, 158, 158, 0.1),
                blurRadius: 4,
                offset: Offset(0, 2),
              ),
            ],
          ),
          child: TabBar(
            controller: _innerTabController,
            labelStyle: TextStyle(
              fontSize: 14.sp,
              fontWeight: FontWeight.w600,
            ),
            // ignore: deprecated_member_use
            labelColor: Colors.black,
            unselectedLabelColor: Colors.grey[700],
            unselectedLabelStyle: const TextStyle(
              color: Colors.black
            ),
            indicator: BoxDecoration(
              borderRadius: BorderRadius.circular(15.r),
               // ignore: deprecated_member_use
               color: widget.tabColor.withOpacity(0.8),
            ),
            indicatorSize: TabBarIndicatorSize.tab,
            dividerColor: Colors.transparent,
            indicatorColor: Colors.transparent,
            labelPadding: EdgeInsets.symmetric(horizontal: 4.w),
            tabs: [
              _buildInnerTab(AppStrings.permanent.tr(), 0),
              _buildInnerTab(AppStrings.temporary.tr(), 1),
            ],
          ),
        ),

        // محتوى التاب
        Expanded(
          child: TabBarView(
            controller: _innerTabController,
            children: [
              // محتوى الدائم - يعرض شاشة الطلبات الحالية
              RequestsAddressChangeScreen(
                status: widget.status,
                appBarTitle: widget.appBarTitle,
                hideAppBar: true,
              ),

              // محتوى المؤقت - عرض طلبات العنوان المؤقت
              TemporaryAddressScreen(
                status: widget.status,
                appBarTitle: widget.appBarTitle,
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildInnerTab(String title, int index) {
    return Tab(
      height: 36.h,
      child: Container(
        padding: EdgeInsets.symmetric(horizontal: 4.w),
        alignment: Alignment.center,
        child: FittedBox(
          fit: BoxFit.scaleDown,
          child: CustomText(
            text: title,
            fontSize: 12,
            fontW: FontWeight.w600,
            color:  Colors.black ,
            textAlign: TextAlign.center,
            maxLine: 1,
          ),
        ),
      ),
    );
  }
}
