import 'package:bus/config/theme_colors.dart';
import 'package:bus/constant/path_route_name.dart';
import 'package:bus/translations/local_keys.g.dart';
import 'package:bus/utils/sized_box.dart';
import 'package:bus/views/custom_widgets/custom_form_field_border.dart';
import 'package:bus/views/custom_widgets/custom_text.dart';
import 'package:bus/widgets/custom_appbar.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class SearchScreen extends StatelessWidget {
  static const String routeName = PathRouteName.search;
  const SearchScreen({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      child: Column(
        children: [
          SizedBox(
            width: 1.sh,
            height: 120.w,
            child: CustomAppBar(
              titleWidget: CustomText(
                text: AppStrings.search.tr(),
                fontSize: 18,
                textAlign: TextAlign.center,
                fontW: FontWeight.w600,
                color: TColor.white,
              ),
            ),
          ),
          const SBox(h: 20),
          CustomFormFieldWithBorder(
            paddingLeft: 30.w,
            paddingRight: 30.w,
            formFieldWidth: 1.sw,
            fillColor: TColor.white,
            hintText: AppStrings.search.tr(),
            borderColor: Colors.grey.withOpacity(0.2),
            suffix: InkWell(
              onTap: () {},
              child: const  Icon(
                Icons.search,
                color: TColor.mainColor,
              ),
            ),
          ),
        ],
      ),
    );
  }
}
