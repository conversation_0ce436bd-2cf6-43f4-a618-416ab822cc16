import 'package:bus/utils/helpers.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';

import '../../../config/theme_colors.dart';
import '../../../constant/path_route_name.dart';
import 'package:bus/translations/local_keys.g.dart';import '../../../utils/assets_utils.dart';
import '../../../utils/sized_box.dart';
import '../../../widgets/custom_appbar.dart';
import '../../../widgets/page_number_widget.dart';
import '../../custom_widgets/build_table_row_widget.dart';
import '../../custom_widgets/custom_text.dart';

class TableRequestChangeAddressScreen extends StatefulWidget {
  static const String routeName = PathRouteName.tableRequestChangeAddress;

  const TableRequestChangeAddressScreen({Key? key}) : super(key: key);

  @override
  State<TableRequestChangeAddressScreen> createState() =>
      _TableRequestChangeAddressScreenState();
}

class _TableRequestChangeAddressScreenState extends State<TableRequestChangeAddressScreen> {

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: CustomAppBar(
        rightWidget: Container(
          padding: EdgeInsets.symmetric(horizontal: 6.0.w, vertical: 3.0.w),
          decoration: BoxDecoration(
            color: TColor.darkRed,
            borderRadius: BorderRadius.circular(5.0.r),
          ),
          child:  Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    const CustomText(
                      text: '5823',
                      color: TColor.white,
                      padding: EdgeInsets.symmetric(vertical: 5.0),
                    ),
                    const SizedBox(width: 5.0),
                    SvgPicture.asset(AppAssets.locationIcon, height: 15.0.w),
                  ],
                ),
        ),
        titleWidget: Center(
          child: CustomText(
            text: AppStrings.requestChangeAddress.tr(),
            fontSize: 18,
            textAlign: TextAlign.center,
            fontW: FontWeight.w600,
            color: TColor.white,
          ),
        ),
        leftWidget: context.locale.toString() == "ar"
            ? InkWell(
                onTap: () {
                  Navigator.pop(context);
                },
                child: SvgPicture.asset(AppAssets.arrowBack),
              )
            : InkWell(
                onTap: () {
                  Navigator.pop(context);
                },
                child: SvgPicture.asset(
                  AppAssets.forwardArrow,
                  colorFilter: const ColorFilter.mode(TColor.white, BlendMode.srcIn),
                  width: 25.w,
                  height: 25.w,
                ),
              ),
      ),
      body: SingleChildScrollView(
        child: Column(
          children: [
            Padding(
              padding: const EdgeInsets.all(8.0),
              child: Container(
                margin: const EdgeInsets.symmetric(horizontal: 8.0),
                clipBehavior: Clip.antiAlias,
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(15.0),
                ),
                child: Table(
                  columnWidths: const {
                    0: FlexColumnWidth(2),
                    1: FlexColumnWidth(2),
                    2: FlexColumnWidth(2),
                    3: FlexColumnWidth(2),
                  },
                  border: TableBorder.all(
                      color: TColor.tabColors,
                      borderRadius: BorderRadius.circular(15.0)),
                  children: [
                    const BuildTableRowWidget(
                      cell: [
                        'Student Name',
                        'Parint Name',
                        'New Address',
                        'Options'
                      ],
                      header: true,
                    ).build(context),
                    ...List.generate(8, (index) {
                      return BuildTableRowWidget(
                        cell: const [
                          'Ahmed',
                          'Zaki Ahmed',
                          'Nasr City',
                          Icons.more_horiz,
                        ],
                        onTapDown: (position) {
                          Helpers.customShowDialog(
                            context,
                            position: position.globalPosition,
                            onTapShow: () {
                              if(index == 0) {
                                Navigator.pushNamed(
                                  context,
                                  PathRouteName.requestChangeAddress,
                                );
                              } else {
                                Navigator.pushNamed(
                                  context,
                                  PathRouteName.reasonRefusal,
                                );
                              }
                            },
                          );
                        },
                      ).build(context);
                    }),
                  ],
                ),
              ),
            ),
            const SBox(h: 50.0),
            const PageNumberWidget(),
          ],
        ),
      ),
    );
  }
}
