import 'dart:async';

import 'package:bus/bloc/profile_cubit/profile_cubit.dart';
import 'package:bus/bloc/send_code_verification_cubit/send_code_verification_cubit.dart';
import 'package:bus/bloc/send_code_verification_cubit/send_code_verification_states.dart';
import 'package:bus/config/global_variable.dart';
import 'package:bus/constant/path_route_name.dart';
import 'package:bus/deep_link.dart';
import 'package:bus/helper/cache_helper.dart';
import 'package:bus/translations/local_keys.g.dart';
import 'package:bus/utils/assets_utils.dart';
import 'package:bus/utils/sized_box.dart';
import 'package:bus/views/custom_widgets/custom_button.dart';
import 'package:bus/views/custom_widgets/custom_text.dart';
import 'package:bus/views/screens/layout_screen/layout_screen.dart';
import 'package:bus/widgets/custom_background_image.dart';
import 'package:dio/dio.dart';
import 'package:easy_localization/easy_localization.dart' as localization;
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:pin_code_fields/pin_code_fields.dart';
import '../../../bloc/forgot_password_cubit/forgot_password_cubit.dart';
import '../../../config/config_base.dart';
import '../../../config/theme_colors.dart';
import '../../custom_widgets/custom_form_field_border.dart';
import '../coupon_screen/coupon_screen.dart';

class SendCodeScreen extends StatefulWidget {
  static const String routeName = PathRouteName.sendCode;
  final bool isForgotPassword;
  final String? email;

  const SendCodeScreen({
    Key? key,
    this.isForgotPassword = false,
    this.email,
  }) : super(key: key);

  @override
  State<SendCodeScreen> createState() => _SendCodeScreenState();
}

class _SendCodeScreenState extends State<SendCodeScreen> {
  String? smsCode;
  String? password;
  String? confirmedPassword;
  bool securityCheck = true;
  bool securityCheck1 = true;
  final _formKey = GlobalKey<FormState>();
  int seonds = 60;

  startMinutes() {
    // tempTime = minutes * 60 + seonds;
    // if (mounted) {
    Timer.periodic(const Duration(seconds: 1), (e) {
      debugPrint(seonds.toString());

      if (e.isActive) {
        if (mounted) {
          if (seonds != 0) {
            setState(() {
              seonds--;
            });
          } else {
            setState(() {
              seonds = 0;
            });
          }
        }
      }
    });
    // }
  }

  @override
  void initState() {
    _initializeAndValidate();
    startMinutes();
    super.initState();
  }

  Future<void> _initializeAndValidate() async {
    await initializeFCM();

    if (fCMToken == null && mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text(
            'Error: Failed to initialize notifications. Please check your internet connection and try again.',
          ),
          duration: Duration(seconds: 5),
        ),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    debugPrint("#####tempToken: $tempToken");
    debugPrint("#####token: $token");
    return Scaffold(
      body: SingleChildScrollView(
        child: CustomBackgroundImage(
          child: Column(
            children: [
              const SBox(h: 60),
              Image.asset(
                assetsImages("logo.png"),
                height: 121.w,
                width: 124.w,
              ),
              const SBox(h: 60),
              CustomText(
                text: AppStrings.sendCode.tr(),
                color: TColor.white,
                fontW: FontWeight.w700,
                fontSize: 20,
              ),
              const SBox(h: 20),
              Container(
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(30.r),
                  color: TColor.white,
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withOpacity(0.2),
                      blurRadius: 3,
                      offset: const Offset(0, 4), // changes position of shadow
                    ),
                  ],
                ),
                width: 354.w,
                child: Form(
                  key: _formKey,
                  child: Column(
                    children: [
                      Row(children: [
                        TextButton(
                          child: CustomText(
                            color: TColor.redAccent,
                            text: AppStrings.wrongEmail.tr(),
                          ),
                          onPressed: () async {
                            Navigator.of(context).pop();
                          },
                        ),
                        const SizedBox(width: 5),
                      ]),
                      CustomText(
                        text: 'تم ارسال الكود ${widget.email ?? ''}',
                        color: TColor.backgroundContainer,
                        fontW: FontWeight.w700,
                        fontSize: 15,
                      ),
                      // const SBox(h: 30),
                      widget.isForgotPassword
                          ? CustomFormFieldWithBorder(
                              prefix: const Icon(
                                Icons.lock_outline,
                                color: TColor.iconInputColor,
                              ),
                              formFieldWidth: 307,
                              contentPaddingVertical: 0,
                              borderColor: TColor.fillFormFieldB,
                              fillColor: TColor.fillFormFieldB,
                              radiusNumber: 15.0,
                              hintText: AppStrings.password.tr(),
                              requierdNumber: 6,
                              saved: (value) {
                                password = value;
                              },
                              validation: AppStrings.validPassword.tr(),
                              security: securityCheck,
                              suffix: InkWell(
                                onTap: () {
                                  setState(() {
                                    securityCheck = !securityCheck;
                                  });
                                },
                                child: securityCheck
                                    ? const Icon(
                                        Icons.visibility_off,
                                        color: TColor.iconInputColor,
                                      )
                                    : const Icon(
                                        Icons.visibility_outlined,
                                        color: TColor.iconInputColor,
                                      ),
                              ),
                            )
                          : const SizedBox(),
                      const SBox(h: 15),
                      widget.isForgotPassword
                          ? CustomFormFieldWithBorder(
                              prefix: const Icon(
                                Icons.lock_outline,
                                color: TColor.iconInputColor,
                              ),
                              formFieldWidth: 307,
                              contentPaddingVertical: 0,
                              borderColor: TColor.fillFormFieldB,
                              fillColor: TColor.fillFormFieldB,
                              radiusNumber: 15.0,
                              requierdNumber: 6,
                              hintText: AppStrings.confirmPassword.tr(),
                              saved: (value) {
                                confirmedPassword = value;
                              },
                              validation: AppStrings.validConfirmPassword.tr(),
                              security: securityCheck1,
                              suffix: InkWell(
                                onTap: () {
                                  setState(() {
                                    securityCheck1 = !securityCheck1;
                                  });
                                },
                                child: securityCheck1
                                    ? const Icon(
                                        Icons.visibility_off,
                                        color: TColor.iconInputColor,
                                      )
                                    : const Icon(
                                        Icons.visibility_outlined,
                                        color: TColor.iconInputColor,
                                      ),
                              ),
                            )
                          : const SizedBox(),
                      const SBox(h: 15),
                      Padding(
                        padding: EdgeInsets.symmetric(
                            vertical: 8.0, horizontal: 10.w),
                        child: Directionality(
                          textDirection: TextDirection.ltr,
                          child: PinCodeTextField(
                            appContext: context,
                            autoFocus: true,
                            cursorColor: TColor.mainColor,
                            keyboardType: TextInputType.number,
                            length: 6,
                            obscureText: false,
                            animationType: AnimationType.scale,
                            pinTheme: PinTheme(
                              shape: PinCodeFieldShape.box,
                              borderRadius: BorderRadius.circular(5),
                              fieldHeight: 50.w,
                              fieldWidth: 50.w,
                              borderWidth: 1,
                              activeColor: TColor.mainColor,
                              inactiveColor: TColor.mainColor,
                              inactiveFillColor: Colors.white,
                              activeFillColor: Colors.white,
                              selectedColor: TColor.mainColor,
                              selectedFillColor: Colors.white,
                            ),
                            animationDuration:
                                const Duration(milliseconds: 300),
                            backgroundColor: Colors.white,
                            enableActiveFill: true,
                            onCompleted: (submitCode) {
                              smsCode = submitCode;
                              debugPrint("Completed");
                            },
                            onChanged: (value) {
                              debugPrint(value);
                            },
                            beforeTextPaste: (text) {
                              debugPrint("Allowing to paste $text");
                              //if you return true then it will show the paste confirmation dialog. Otherwise if false, then nothing will happen.
                              //but you can show anything you want here, like your pop up saying wrong paste format or etc
                              return true;
                            },
                          ),
                        ),
                      ),
                      const SBox(h: 10),

                      (seonds != 0)
                          ? Center(
                              child: CustomText(
                                text: 'Resend Code After ${seonds}s',
                                color: TColor.mainColor,
                                fontW: FontWeight.w500,
                                fontSize: 16,
                              ),
                            )
                          : CustomButton(
                              text: "Resend",
                              onTap: () async {
                                setState(() {
                                  setState(() {
                                    seonds = 60;
                                  });
                                });

                                final authHeaders = {
                                  'Authorization': "Bearer $tempToken"
                                };
                                try {
                                  var response = await Dio().post(
                                    "${ConfigBase.baseUrl}resendCode",
                                    data: {"token": smsCode,"firebase_token": fCMToken,},
                                    options: Options(
                                      headers: authHeaders,
                                    ),
                                  );
                                  if (response.statusCode == 200) {
                                    try {
                                      var response = await Dio().post(
                                        "${ConfigBase.baseUrl}verify",
                                        data: {"token": smsCode,"firebase_token": fCMToken,},
                                        options: Options(
                                          headers: authHeaders,
                                        ),
                                      );
                                      if (response.statusCode == 200) {
                                        CacheHelper.putString(
                                            "token", tempToken!);
                                        token = tempToken;
                                        // ignore: use_build_context_synchronously
                                        // Navigator.popAndPushNamed(
                                        //     context, HomeScreen.routeName);
                                        // ignore: use_build_context_synchronously
                                        ScaffoldMessenger.of(context)
                                            .showSnackBar(
                                          const SnackBar(
                                            backgroundColor:
                                                TColor.greenSuccess,
                                            content: CustomText(
                                              text: '"تم التحقق بنجاح"',
                                              color: TColor.white,
                                            ),
                                          ),
                                        );
                                      } else {
                                        // Handle other status codes here, if needed
                                        debugPrint(
                                            "******************Request failed with status: ${response.statusCode}");
                                      }
                                    } catch (e) {
                                      // debugPrint("******************An error occurred: $e");
                                      // ScaffoldMessenger.of(context).showSnackBar(
                                      //   SnackBar(
                                      //     backgroundColor: TColor.redAccent,
                                      //     content: CustomText(
                                      //       text: AppStrings.wrongCode.tr(),
                                      //       color: TColor.white,
                                      //     ),
                                      //   ),
                                      // );
                                    }
                                    // CacheHelper.putString("token", tempToken!);
                                    // token = tempToken;
                                    // ignore: use_build_context_synchronously
                                    // Navigator.popAndPushNamed(
                                    //     context, HomeScreen.routeName);
                                    ScaffoldMessenger.of(context).showSnackBar(
                                      const SnackBar(
                                        backgroundColor: TColor.greenSuccess,
                                        content: CustomText(
                                          text: 'تم ارسال الكود مره اخري',
                                          color: TColor.white,
                                        ),
                                      ),
                                    );
                                  } else {
                                    // Handle other status codes here, if needed
                                    debugPrint(
                                        "******************Request failed with status: ${response.statusCode}");
                                  }
                                } catch (e) {
                                  debugPrint(
                                      "******************An error occurred: $e");
                                  ScaffoldMessenger.of(context).showSnackBar(
                                    const SnackBar(
                                      backgroundColor: TColor.redAccent,
                                      content: CustomText(
                                        text: "حدث خطأ",
                                        color: TColor.white,
                                      ),
                                    ),
                                  );
                                }
                                // context
                                //     .read<SendCodeVerificationCubit>()
                                //     .sendCodeVerification(
                                //       smsCode: smsCode,
                                //     );
                              },
                              width: 120,
                              height: 48,
                              radius: 15,
                              borderColor: TColor.mainColor,
                              bgColor: TColor.mainColor,
                            ),
                      const SBox(h: 30),

                      BlocConsumer<ForgotPasswordCubit, ForgotPasswordState>(
                        listener: (context, state) {
                          if (state is ForgotPasswordSuccess) {
                            Navigator.of(context)
                                .popUntil((route) => route.isFirst);
                            Navigator.of(context).pushReplacement(
                                MaterialPageRoute(builder: (context) {
                              return const LayoutScreen();
                            }));
                          }
                        },
                        builder: (context, state) {
                          if (state is ForgotPasswordLoading) {
                            return const CircularProgressIndicator(
                              color: TColor.mainColor,
                            );
                          } else {
                            return widget.isForgotPassword
                                ? BlocBuilder<ForgotPasswordCubit,
                                        ForgotPasswordState>(
                                    builder: (context, states) {
                                    if (states is! ForgotPasswordLoading) {
                                      return CustomButton(
                                        text: AppStrings.next.tr(),
                                        onTap: () async {
                                          if (_formKey.currentState!
                                              .validate()) {
                                            _formKey.currentState!.save();
                                            ForgotPasswordCubit.get(context)
                                                .resetPassword(
                                              email: widget.email,
                                              code: smsCode,
                                              password: password,
                                              passwordConfirmation:
                                                  confirmedPassword,
                                            );
                                          }
                                        },
                                        width: 307,
                                        height: 48,
                                        radius: 15,
                                        borderColor: TColor.mainColor,
                                        bgColor: TColor.mainColor,
                                      );
                                    } else {
                                      return const Center(
                                        child: CircularProgressIndicator(),
                                      );
                                    }
                                  })
                                : BlocConsumer<SendCodeVerificationCubit,
                                        SendCodeVerificationStates>(
                                    listener: (context, states) {
                                    if (states
                                        is SendCodeVerificationSuccessStates) {
                                      CacheHelper.putString(
                                          "token", tempToken!);
                                      token = tempToken;
                                      debugPrint(token);
                                      context.read<ProfileCubit>().getProfile();
                                      Navigator.pushNamed(
                                          context, CouponScreen.routeName);
                                      ScaffoldMessenger.of(context)
                                          .showSnackBar(
                                        SnackBar(
                                          backgroundColor: TColor.greenSuccess,
                                          content: CustomText(
                                            text:
                                                AppStrings.email_verified.tr(),
                                            color: TColor.white,
                                          ),
                                        ),
                                      );
                                    } else if (states
                                        is SendCodeVerificationErrorStates) {
                                      ScaffoldMessenger.of(context)
                                          .showSnackBar(
                                        SnackBar(
                                          backgroundColor: TColor.redAccent,
                                          content: CustomText(
                                            text: AppStrings.wrongCode.tr(),
                                            color: TColor.white,
                                          ),
                                        ),
                                      );
                                    }
                                  }, builder: (context, states) {
                                    if (states
                                        is! SendCodeVerificationLoadingStates) {
                                      return CustomButton(
                                        text: AppStrings.next.tr(),
                                        onTap: () async {
                                          context
                                              .read<SendCodeVerificationCubit>()
                                              .sendCodeVerification(
                                                smsCode: smsCode,
                                              );
                                        },
                                        width: 290,
                                        height: 48,
                                        radius: 15,
                                        borderColor: TColor.mainColor,
                                        bgColor: TColor.mainColor,
                                      );
                                    } else {
                                      return const Center(
                                        child: CircularProgressIndicator(),
                                      );
                                    }
                                  });
                          }
                        },
                      ),
                      const SBox(h: 30),
                      SizedBox(width: 15.w),
                    ],
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
