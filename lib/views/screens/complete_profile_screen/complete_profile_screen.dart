import 'dart:io';
import 'package:bus/bloc/complete_profile_cubit/complete_profile_cubit.dart';
import 'package:bus/bloc/complete_profile_cubit/complete_profile_states.dart';
import 'package:bus/data/models/pickup_location_local_models/pickup_location_local_models.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:image_picker/image_picker.dart';
import 'package:open_street_map_search_and_pick/open_street_map_search_and_pick.dart';

import '../../../config/theme_colors.dart';
import '../../../constant/path_route_name.dart';
import '../../../helper/response_state.dart';
import '../../../translations/local_keys.g.dart';
import '../../custom_widgets/custom_button.dart';
import '../../custom_widgets/custom_form_field_border.dart';
import '../../custom_widgets/custom_text.dart';
import '../../../widgets/custom_appbar.dart';
import '../../../widgets/pick_location_widget.dart';

class CompleteProfileScreen extends StatefulWidget {
  static const String routeName = PathRouteName.completeProfile;

  const CompleteProfileScreen({super.key});

  @override
  State<CompleteProfileScreen> createState() => _CompleteProfileScreenState();
}

class _CompleteProfileScreenState extends State<CompleteProfileScreen> {
  final TextEditingController nameController = TextEditingController();
  final TextEditingController phoneController = TextEditingController();
  final TextEditingController addressController = TextEditingController();
  final TextEditingController cityNameController = TextEditingController();
  final _formKey = GlobalKey<FormState>();
  File? selectedImage;
  PickupLocationLocalModels? pickupLocationLocalModels;
  LatLong? position;

  Future<void> pickImage() async {
    final ImagePicker picker = ImagePicker();
    final XFile? image = await picker.pickImage(source: ImageSource.gallery);
    if (image != null) {
      setState(() {
        selectedImage = File(image.path);
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: CustomAppBar(
        titleWidget: CustomText(
          text: AppStrings.completeProfile.tr(),
          fontSize: 18,
          textAlign: TextAlign.center,
          fontW: FontWeight.w600,
          color: TColor.white,
        ),
      ),
      body: SingleChildScrollView(
        child: Padding(
          padding: EdgeInsets.symmetric(horizontal: 37.w),
          child: Form(
            key: _formKey,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                30.verticalSpace,
                Center(
                  child: Column(
                    children: [
                      InkWell(
                        onTap: pickImage,
                        child: selectedImage != null
                            ? CircleAvatar(
                                radius: 50.r,
                                backgroundImage: FileImage(selectedImage!),
                              )
                            : CircleAvatar(
                                radius: 50.r,
                                backgroundColor: TColor.fillFormFieldB,
                                child: Column(
                                  mainAxisAlignment: MainAxisAlignment.center,
                                  children: [
                                    Icon(
                                      Icons.camera_alt,
                                      size: 30.sp,
                                      color: TColor.grey5,
                                    ),
                                    CustomText(
                                      text: AppStrings.uploadPhoto.tr(),
                                      fontSize: 12,
                                      color: TColor.grey5,
                                    ),
                                  ],
                                ),
                              ),
                      ),
                    ],
                  ),
                ),
                30.verticalSpace,
                CustomText(
                  text: AppStrings.personalInformation.tr(),
                  fontSize: 16,
                  fontW: FontWeight.w600,
                ),
                20.verticalSpace,
                CustomFormFieldWithBorder(
                  isTitled: true,
                  title: AppStrings.schoolName.tr(),
                  controller: nameController,
                  validation: AppStrings.pleaseEnterValidName.tr(),
                  requierdNumber: 3,
                  inputType: TextInputType.text,
                  formFieldWidth: 428,
                  heightA: 53,
                  hintText: AppStrings.name.tr(),
                  borderColor: TColor.fillFormFieldB,
                  fillColor: TColor.fillFormFieldB,
                  radiusNumber: 15.0,
                  contentPaddingVertical: 15,
                  contentPaddingHorizontal: 15,
                ),
                15.verticalSpace,
                CustomFormFieldWithBorder(
                  isTitled: true,
                  title: AppStrings.phoneNumber.tr(),
                  controller: phoneController,
                  validation: AppStrings.pleaseEnterValidPhone.tr(),
                  requierdNumber: 10,
                  inputType: TextInputType.phone,
                  formFieldWidth: 428,
                  heightA: 53,
                  hintText: AppStrings.phoneNumber.tr(),
                  borderColor: TColor.fillFormFieldB,
                  fillColor: TColor.fillFormFieldB,
                  radiusNumber: 15.0,
                  contentPaddingVertical: 15,
                  contentPaddingHorizontal: 15,
                ),
                15.verticalSpace,
                CustomFormFieldWithBorder(
                  isTitled: true,
                  title: AppStrings.address.tr(),
                  controller: addressController,
                  validation: AppStrings.pleaseEnterValidAddress.tr(),
                  requierdNumber: 5,
                  inputType: TextInputType.streetAddress,
                  formFieldWidth: 428,
                  heightA: 53,
                  hintText: AppStrings.address.tr(),
                  borderColor: TColor.fillFormFieldB,
                  fillColor: TColor.fillFormFieldB,
                  radiusNumber: 15.0,
                  contentPaddingVertical: 15,
                  contentPaddingHorizontal: 15,
                ),
                15.verticalSpace,
              
                _buildLocationPicker(),
                30.verticalSpace,
                BlocConsumer<CompleteProfileCubit, CompleteProfileStates>(
                  listener: (context, state) {
                    if (state.rStates == ResponseState.success) {
                      Navigator.pop(context);
                      ScaffoldMessenger.of(context).showSnackBar(
                        SnackBar(
                          backgroundColor: TColor.greenSuccess,
                          content: CustomText(
                            text: AppStrings.profileCompleted.tr(),
                            color: TColor.white,
                          ),
                        ),
                      );
                    } else if (state.rStates == ResponseState.failure) {
                      ScaffoldMessenger.of(context).showSnackBar(
                        SnackBar(
                          backgroundColor: TColor.redAccent,
                          content: CustomText(
                            text: state.error ??
                                AppStrings.somethingWentWrong.tr(),
                            color: TColor.white,
                          ),
                        ),
                      );
                    }
                  },
                  builder: (context, state) {
                    if (state.rStates == ResponseState.loading) {
                      return const Center(
                        child: CircularProgressIndicator(
                          color: TColor.mainColor,
                        ),
                      );
                    }
                    return CustomButton(
                      text: AppStrings.submit.tr(),
                      onTap: () {
                        if (position == null) {
                          ScaffoldMessenger.of(context).showSnackBar(
                            SnackBar(
                              content: CustomText(
                                text: AppStrings.setLocation.tr(),
                                color: TColor.white,
                              ),
                            ),
                          );
                          return;
                        }
                        if (_formKey.currentState!.validate()) {
                          context.read<CompleteProfileCubit>().completeProfile(
                                name: nameController.text,
                                phone: phoneController.text,
                                address: addressController.text,
                                cityName: cityNameController.text.isNotEmpty
                                    ? cityNameController.text
                                    : null,
                                latitude: position?.latitude,
                                longitude: position?.longitude,
                                imageFile: selectedImage?.path,
                                context: context,
                              );
                        }
                      },
                      width: 428,
                      height: 53,
                      radius: 15,
                      borderColor: TColor.mainColor,
                      bgColor: TColor.mainColor,
                    );
                  },
                ),
                30.verticalSpace,
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildLocationPicker() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: EdgeInsets.only(bottom: 10.h),
          child: CustomText(
            text: AppStrings.getLocations.tr(),
            fontSize: 15,
            fontW: FontWeight.bold,
            color: TColor.black,
          ),
        ),
        InkWell(
          onTap: () async {
            try {
              pickupLocationLocalModels = await Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (ctx) => PickLocationWidget(
                    latitude: position?.latitude ?? 0.0,
                    longitude: position?.longitude ?? 0.0,
                  ),
                ),
              );
              setState(() {
                position = LatLong(
                  pickupLocationLocalModels?.lat ?? 0.0,
                  pickupLocationLocalModels?.long ?? 0.0,
                );
              });
            } catch (e) {
              debugPrint("Error picking location: $e");
            }
          },
          child: Container(
            width: 428.w,
            height: 53.w,
            decoration: BoxDecoration(
              color: TColor.fillFormFieldB,
              borderRadius: BorderRadius.circular(15.r),
            ),
            child: Padding(
              padding: context.locale.toString() == "ar"
                  ? EdgeInsets.only(top: 15.w, right: 15.w)
                  : EdgeInsets.only(top: 15.w, left: 15.w),
              child: CustomText(
                text: pickupLocationLocalModels?.address ??
                    AppStrings.getLocations.tr(),
                color: TColor.tabColors,
                fontSize: 15,
                fontW: FontWeight.w500,
              ),
            ),
          ),
        ),
      ],
    );
  }

  @override
  void dispose() {
    nameController.dispose();
    phoneController.dispose();
    addressController.dispose();
    cityNameController.dispose();
    super.dispose();
  }
}
