import 'dart:io';
import 'package:bus/bloc/bus_supervisor_available_cubit/bus_supervisor_available_cubit.dart';
import 'package:bus/bloc/bus_supervisor_available_cubit/bus_supervisor_available_states.dart';
import 'package:bus/bloc/gender_cubit/gender_cubit.dart';
import 'package:bus/bloc/gender_cubit/gender_states.dart';
import 'package:bus/bloc/supervisor_cubit/supervisor_cubit.dart';
import 'package:bus/bloc/update_suporvisor_cubit/update_supervisor_states.dart';
import 'package:bus/bloc/update_suporvisor_cubit/update_suporvisor_cubit.dart';
import 'package:bus/config/global_variable.dart';
import 'package:bus/config/theme_colors.dart';
import 'package:bus/constant/path_route_name.dart';
import 'package:bus/data/models/bus_avaiable_models/bus_available_data_models.dart';
import 'package:bus/data/models/student_models/student_grade_models.dart';
import 'package:bus/data/models/supervisor_mdodels/supervisor_info_models.dart';
import 'package:bus/translations/local_keys.g.dart';
import 'package:bus/utils/assets_utils.dart';
import 'package:bus/utils/sized_box.dart';
import 'package:bus/views/custom_widgets/custom_button.dart';
import 'package:bus/views/custom_widgets/custom_drop_down_button.dart';
import 'package:bus/views/custom_widgets/custom_form_field_border.dart';
import 'package:bus/views/custom_widgets/custom_text.dart';
import 'package:bus/widgets/custom_appbar.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:image_picker/image_picker.dart';
import 'package:logger/logger.dart';
import '../../../bloc/add_suporvisor_cubit/add_supervisor_cubit.dart';
import '../../../bloc/add_suporvisor_cubit/add_supervisor_states.dart';

class AddSupervisorScreen extends StatefulWidget {
  static const String routeName = PathRouteName.addSupervisor;

  const AddSupervisorScreen({Key? key, required this.isEdit, this.supervisor})
      : super(key: key);

  final SupervisorInfoModels? supervisor;
  final bool isEdit;

  @override
  State<AddSupervisorScreen> createState() => _AddSupervisorScreenState();
}

class _AddSupervisorScreenState extends State<AddSupervisorScreen> {
  File? image;
  final _formKey = GlobalKey<FormState>();
  String? name;
  int? busId;
  DateTime? dateBirth;
  String phoneNumber = "";
  TextEditingController? txcName;
  TextEditingController? txcUsername;
  TextEditingController? txcCity;
  TextEditingController? txcAddress;
  TextEditingController? txcPhoneNumber;
  int? genderId;
  int? religionId;
  int? typeBloodId;
  String? address;
  String? cityName;
  String? username;
  TextEditingController password = TextEditingController();
  TextEditingController passwordConfirmation = TextEditingController();

  @override
  void initState() {
    super.initState();
    context.read<BusSupervisorAvailableCubit>().busSupervisorAvailable();
    context.read<GenderCubit>().getGender();
    _initializeFields();
  }

  void _initializeFields() {
    try {
      if (widget.isEdit) {
        _setEditModeFields();
      } else {
        _setAddModeFields();
      }
    } catch (e) {
      if (kDebugMode) {
        print(e);
      }
    }
  }

  void _setEditModeFields() {
    genderId = widget.supervisor?.gender_id ?? 0;
    religionId = widget.supervisor?.religion_id ?? 0;
    busId = int.tryParse(widget.supervisor?.bus?.id.toString() ?? "0");
    name = widget.supervisor?.name;
    address = widget.supervisor?.address;
    typeBloodId = widget.supervisor?.type__blood_id ?? 0;
    username = widget.supervisor?.username;
    dateBirth = _parseDate(widget.supervisor?.birth_date);
    phoneNumber = widget.supervisor?.phone ?? "";
    cityName = widget.supervisor?.city_name ?? "";

    txcName = TextEditingController(text: name);
    txcUsername = TextEditingController(text: username);
    txcCity = TextEditingController(text: cityName);
    txcAddress = TextEditingController(text: address);
    txcPhoneNumber = TextEditingController(text: phoneNumber);
  }

  void _setAddModeFields() {
    genderId = 0;
    religionId = 0;
    typeBloodId = 0;
    busId = 0;
  }

  DateTime? _parseDate(String? dateString) {
    if (dateString == null) return null;
    var listOf = dateString.split('-');
    return DateTime(
      int.parse(listOf[0]),
      int.parse(listOf[1]),
      int.parse(listOf[2]),
    );
  }

  Future<void> _getImage() async {
    try {
      final images = await ImagePicker().pickImage(source: ImageSource.gallery);
      if (images != null) {
        final imageFile = File(images.path);
        setState(() {
          image = imageFile;
        });
      }
    } on PlatformException catch (e) {
      if (kDebugMode) {
        Logger().d(e);
      }
    }
  }

  void _showSnackBar(String message) {
    final snackBar = SnackBar(
      backgroundColor: TColor.redAccent,
      content: CustomText(
        text: message,
        color: TColor.white,
        fontSize: 18,
        maxLine: 5,
      ),
    );
    snackBarKey.currentState?.showSnackBar(snackBar);
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: _buildAppBar(),
      body: _buildBody(),
    );
  }

  PreferredSizeWidget _buildAppBar() {
    return CustomAppBar(
      titleWidget: CustomText(
        text: widget.isEdit
            ? AppStrings.editSupervisor.tr()
            : AppStrings.addSupervisor.tr(),
        fontSize: 18,
        textAlign: TextAlign.center,
        fontW: FontWeight.w600,
        color: TColor.white,
      ),
      leftWidget: _buildBackButton(),
    );
  }

  Widget _buildBackButton() {
    final isArabic = context.locale.toString() == "ar";
    return InkWell(
      onTap: () => Navigator.pop(context),
      child: SvgPicture.asset(
        isArabic ? AppAssets.arrowBack : AppAssets.forwardArrow,
        colorFilter: isArabic
            ? null
            : const ColorFilter.mode(TColor.white, BlendMode.srcIn),
        width: 25.w,
        height: 25.w,
      ),
    );
  }

  Widget _buildBody() {
    return SingleChildScrollView(
      child: Form(
        key: _formKey,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildProfileImage(),
            const SBox(h: 40),
            _buildNameField(),
            const SBox(h: 15),
            _buildUsernameField(),
            const SBox(h: 15),
            _buildGenderDropdown(),
            const SBox(h: 15),
            _buildBusDropdown(),
            const SBox(h: 15),
            _buildAddressField(),
            const SBox(h: 15),
            _buildPhoneNumberField(),
            const SBox(h: 15),
            _buildPasswordField(),
            const SBox(h: 15),
            widget.isEdit ? const SizedBox() : _buildConfirmPasswordField(),
            _buildSubmitButton(),
          ],
        ),
      ),
    );
  }

  Widget _buildProfileImage() {
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: 37.w),
      child: Center(
        child: InkWell(
          onTap: _getImage,
          child: _buildImageWidget(),
        ),
      ),
    );
  }

  Widget _buildImageWidget() {
    if (image != null) {
      return CircleAvatar(
        radius: 34.r,
        backgroundColor: TColor.white,
        child: CircleAvatar(
          backgroundImage: FileImage(image!),
        ),
      );
    }

    return Stack(
      children: [
        _buildExistingImageWidget(),
        _buildImagePickerIcon(),
      ],
    );
  }

  Widget _buildExistingImageWidget() {
    if (widget.supervisor?.logo_path != null) {
      return CircleAvatar(
        radius: 35.r,
        backgroundColor: TColor.borderContainer,
        child: CircleAvatar(
          radius: 34.r,
          backgroundColor: TColor.white,
          backgroundImage: NetworkImage(widget.supervisor!.logo_path ?? ''),
        ),
      );
    }

    return CircleAvatar(
      radius: 35.r,
      backgroundColor: TColor.borderContainer,
      child: CircleAvatar(
        radius: 34.r,
        backgroundColor: TColor.white,
        child: Image.asset(
          assetsImages("pc.png"),
          width: 25.w,
          height: 25.w,
        ),
      ),
    );
  }

  Widget _buildImagePickerIcon() {
    final isArabic = context.locale.toString() == "ar";
    return Positioned(
      left: isArabic ? 0 : null,
      right: isArabic ? null : 0,
      bottom: 5.w,
      child: Container(
        width: 20.w,
        height: 20.w,
        decoration: const BoxDecoration(
          color: TColor.borderContainer,
          shape: BoxShape.circle,
        ),
        child: Icon(
          Icons.folder_copy,
          color: TColor.white,
          size: 10.sp,
        ),
      ),
    );
  }

  Widget _buildNameField() {
    return CustomFormFieldWithBorder(
      isTitled: true,
      title: AppStrings.name.tr(),
      controller: txcName,
      onChanged: (value) => name = value,
      validation: '${AppStrings.name.tr()} ${AppStrings.isRequired.tr()}',
      formFieldWidth: 428,
      heightA: 53,
      hintText: AppStrings.name.tr(),
      borderColor: TColor.fillFormFieldB,
      fillColor: TColor.fillFormFieldB,
      radiusNumber: 15.0,
      paddingRight: 37.w,
      paddingLeft: 37.w,
      contentPaddingVertical: 15,
      contentPaddingHorizontal: 15,
    );
  }

  Widget _buildUsernameField() {
    return CustomFormFieldWithBorder(
      isTitled: true,
      title: AppStrings.username.tr(),
      controller: txcUsername,
      onChanged: (value) => username = value,
      validation: '${AppStrings.username.tr()} ${AppStrings.isRequired.tr()}',
      formFieldWidth: 428,
      heightA: 53,
      hintText: AppStrings.username.tr(),
      borderColor: TColor.fillFormFieldB,
      fillColor: TColor.fillFormFieldB,
      radiusNumber: 15.0,
      paddingRight: 37.w,
      paddingLeft: 37.w,
      contentPaddingVertical: 15,
      contentPaddingHorizontal: 15,
    );
  }

  Widget _buildGenderDropdown() {
    return BlocBuilder<GenderCubit, GenderStates>(
      builder: (context, state) {
        if (state is GenderLoadingStates) {
          return const Center(
            child: CircularProgressIndicator(
              color: TColor.mainColor,
            ),
          );
        } else if (state is GenderSuccessStates) {
          final listOfGradeIds = _buildGenderList(state);
          return CustomDropDownButton(
            items: listOfGradeIds.map<DropdownMenuItem<int>>((value) {
              return DropdownMenuItem<int>(
                value: value.id,
                child: Padding(
                  padding: EdgeInsets.symmetric(horizontal: 20.w),
                  child: Text(_translateGender(value.name ?? '')),
                ),
              );
            }).toList(),
            onChanged: (value) {
              setState(() {
                genderId = value;
              });
            },
            value: genderId,
          );
        } else {
          return const SizedBox();
        }
      },
    );
  }

  List<StudentGradeModels> _buildGenderList(GenderSuccessStates state) {
    final listOfGradeIds = [
      StudentGradeModels(id: 0, name: AppStrings.selectGender.tr()),
      ...?state.genderModels?.data?.map((element) => StudentGradeModels(
            id: element.id,
            name: element.name,
          )),
    ];
    return listOfGradeIds;
  }

  String _translateGender(String name) {
    if (name == 'ذكر' && context.locale.toString() == "en") {
      return 'Male';
    } else if (name == 'انثي' && context.locale.toString() == "en") {
      return 'Female';
    }
    return name;
  }

  Widget _buildBusDropdown() {
    return BlocBuilder<BusSupervisorAvailableCubit,
        BusSupervisorAvailableStates>(
      builder: (context, state) {
        if (state is BusSupervisorAvailableLoadingStates) {
          return const Center(
            child: CircularProgressIndicator(
              color: TColor.mainColor,
            ),
          );
        } else if (state is BusSupervisorAvailableSuccessStates) {
          final listOfGradeIds = _buildBusList(state);
          return CustomDropDownButton(
            items: [
              // DropdownMenuItem<int>(
              //   value: null,
              //   child: Padding(
              //     padding: EdgeInsets.symmetric(horizontal: 20.w),
              //     child: const Text('No Bus'),
              //   ),
              // ),
              ...listOfGradeIds.map<DropdownMenuItem<int>>((value) {
                return DropdownMenuItem<int>(
                  value: value.id,
                  child: Padding(
                    padding: EdgeInsets.symmetric(horizontal: 20.w),
                    child: Text(value.name ?? ''),
                  ),
                );
              }).toList(),
            ],
            onChanged: (value) {
              setState(() {
                busId = value;
              });
            },
            value: busId,
          );
        } else {
          return Center(
            child: CustomText(
              text: AppStrings.busesNotFound.tr(),
            ),
          );
        }
      },
    );
  }

  List<BusAvailableDataModels> _buildBusList(
      BusSupervisorAvailableSuccessStates state) {
    final Set<int> addedIds = {};
    final uniqueList = <BusAvailableDataModels>[];

    // Add edited bus if exists
    if (widget.isEdit && widget.supervisor?.bus_id != null) {
      uniqueList.add(BusAvailableDataModels(
        id: widget.supervisor!.bus_id!,
        name: widget.supervisor!.bus?.name!,
      ));
      addedIds.add(widget.supervisor!.bus_id!);
    }

    // Add "no Bus" option
    uniqueList.add(const BusAvailableDataModels(
      id: 0,
      name: "no Bus",
    ));
    addedIds.add(0);

    // Add remaining unique buses
    state.busAvailableModels?.data?.forEach((element) {
      if (element.id != null && !addedIds.contains(element.id)) {
        uniqueList.add(BusAvailableDataModels(
          id: element.id!,
          name: element.name!,
        ));
        addedIds.add(element.id!);
      }
    });

    return uniqueList;
  }

  Widget _buildAddressField() {
    return CustomFormFieldWithBorder(
      isTitled: true,
      title: AppStrings.address.tr(),
      controller: txcAddress,
      validation: '${AppStrings.address.tr()} ${AppStrings.isRequired.tr()}',
      onChanged: (value) => address = value,
      formFieldWidth: 428,
      requierdNumber: 5,
      heightA: 53,
      hintText: AppStrings.address.tr(),
      borderColor: TColor.fillFormFieldB,
      fillColor: TColor.fillFormFieldB,
      radiusNumber: 15.0,
      paddingRight: 37.w,
      paddingLeft: 37.w,
      contentPaddingVertical: 15,
      contentPaddingHorizontal: 15,
    );
  }

  Widget _buildPhoneNumberField() {
    return CustomFormFieldWithBorder(
      isTitled: true,
      title: AppStrings.phoneNumber.tr(),
      requierdNumber: 9,
      controller: txcPhoneNumber,
      validation:
          '${AppStrings.phoneNumber.tr()} ${AppStrings.isRequired.tr()}',
      onChanged: (value) => phoneNumber = value,
      formFieldWidth: 428,
      heightA: 53,
      hintText: AppStrings.phoneNumber.tr(),
      borderColor: TColor.fillFormFieldB,
      fillColor: TColor.fillFormFieldB,
      radiusNumber: 15.0,
      paddingRight: 37.w,
      paddingLeft: 37.w,
      contentPaddingVertical: 15,
      contentPaddingHorizontal: 15,
      inputType: TextInputType.number,
    );
  }

  Widget _buildPasswordField() {
    return CustomFormFieldWithBorder(
      isTitled: true,
      title: widget.isEdit
          ? AppStrings.newPassword.tr()
          : AppStrings.password.tr(),
      onChanged: (value) => password.text = value.trim(),
      validation: '${AppStrings.password.tr()} ${AppStrings.isRequired.tr()}',
      formFieldWidth: 428,
      heightA: 53,
      hintText: AppStrings.password.tr(),
      borderColor: TColor.fillFormFieldB,
      fillColor: TColor.fillFormFieldB,
      radiusNumber: 15.0,
      paddingRight: 37.w,
      paddingLeft: 37.w,
      contentPaddingVertical: 15,
      contentPaddingHorizontal: 15,
    );
  }

  Widget _buildConfirmPasswordField() {
    return CustomFormFieldWithBorder(
      isTitled: true,
      title: AppStrings.confirmPassword.tr(),
      onChanged: (value) => passwordConfirmation.text = value,
      checkValidatorFunc: true,
      validatorFunc: (value) {
        if (password.text != value) {
          return 'password not matching';
        }
        return null;
      },
      formFieldWidth: 428,
      heightA: 53,
      hintText: AppStrings.confirmPassword.tr(),
      borderColor: TColor.fillFormFieldB,
      fillColor: TColor.fillFormFieldB,
      radiusNumber: 15.0,
      paddingRight: 37.w,
      paddingLeft: 37.w,
      contentPaddingVertical: 15,
      contentPaddingHorizontal: 15,
    );
  }

  Widget _buildSubmitButton() {
    return widget.isEdit ? _buildUpdateButton() : _buildAddButton();
  }

  Widget _buildUpdateButton() {
    return BlocConsumer<UpdateSupervisorCubit, UpdateSupervisorStates>(
      listener: (context, state) {
        if (state is UpdateSupervisorSuccessStates) {
          _handleSuccess(context);
        } else if (state is UpdateSupervisorErrorStates) {
          _showSnackBar(state.error ?? 'An unknown error occurred');
        }
      },
      builder: (context, state) {
        if (state is! UpdateSupervisorLoadingStates) {
          return _buildSubmitButtonWidget(
            text: AppStrings.save.tr(),
            onTap: _updateSupervisor,
          );
        } else {
          return const Center(
            child: CircularProgressIndicator(),
          );
        }
      },
    );
  }

  Widget _buildAddButton() {
    return BlocConsumer<AddSupervisorCubit, AddSupervisorStates>(
      listener: (context, state) {
        if (state is AddSupervisorSuccessStates) {
          _handleSuccess(context);
        } else if (state is AddSupervisorErrorStates) {
          _showSnackBar(state.error ?? 'An unknown error occurred');
        }
      },
      builder: (context, state) {
        if (state is! AddSupervisorLoadingStates) {
          return _buildSubmitButtonWidget(
            text: AppStrings.add.tr(),
            onTap: _addSupervisor,
          );
        } else {
          return const Center(
            child: CircularProgressIndicator(),
          );
        }
      },
    );
  }

  Widget _buildSubmitButtonWidget({
    required String text,
    required VoidCallback onTap,
  }) {
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: 37.w, vertical: 30.h),
      child: CustomButton(
        text: text,
        onTap: onTap,
        width: 428,
        height: 53,
        radius: 15,
        borderColor: TColor.mainColor,
        bgColor: TColor.mainColor,
      ),
    );
  }

  void _updateSupervisor() {
    context.read<UpdateSupervisorCubit>().updateSupervisor(
          name: name,
          cityName: cityName,
          genderId: genderId,
          typeBloodId: typeBloodId,
          religionId: religionId,
          password_confirmation: passwordConfirmation.text.trim(),
          password: password.text.trim(),
          address: address,
          username: username,
          phone: phoneNumber,
          birthDate: dateBirthS(dateBirth),
          image: image,
          busId: busId,
          supervisorId: widget.supervisor!.id,
        );
  }

  void _addSupervisor() {
    if (_formKey.currentState!.validate()) {
      _formKey.currentState!.save();
      context.read<AddSupervisorCubit>().addSupervisor(
            name: name,
            cityName: cityName,
            genderId: genderId,
            typeBloodId: typeBloodId,
            religionId: religionId,
            password_confirmation: passwordConfirmation.text,
            password: password.text,
            address: address,
            username: username,
            phone: phoneNumber,
            birthDate: dateBirthS(dateBirth),
            image: image,
            busId: busId,
          );
    }
  }

  void _handleSuccess(BuildContext context) {
    BlocProvider.of<SupervisorCubit>(context)
        .getSupervisor(isFirst: true, pageNumber: 1);
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        backgroundColor: TColor.greenSuccess,
        content: CustomText(
          text: widget.isEdit
              ? AppStrings.supervisorEdited.tr()
              : AppStrings.supervisorAdded.tr(),
          fontSize: 18,
          maxLine: 5,
          color: TColor.white,
        ),
      ),
    );
    Navigator.pop(context);
  }

  String dateBirthS(DateTime? datetime) {
    if (datetime != null) {
      String year = datetime.year.toString();
      int month = datetime.month;
      int day = datetime.day;
      String dayString = day >= 10 ? day.toString() : "0$day";
      String monthString = month >= 10 ? month.toString() : "0$month";
      String dateFormat = "$year-$monthString-$dayString";
      return dateFormat;
    } else {
      return "";
    }
  }
}
