import 'dart:developer';
import 'dart:io';

import 'package:bus/bloc/buses_cubit/buses_cubit.dart';
import 'package:bus/bloc/buses_cubit/buses_states.dart';
import 'package:bus/bloc/classroom__cubit/class_room_cubit.dart';
import 'package:bus/bloc/gender_cubit/gender_cubit.dart';
import 'package:bus/bloc/gender_cubit/gender_states.dart';
import 'package:bus/bloc/grade_cubit/grade_cubit.dart';
import 'package:bus/bloc/grade_cubit/grade_states.dart';
import 'package:bus/bloc/student_cubit/student_cubit.dart';
import 'package:bus/config/global_variable.dart';
import 'package:bus/config/theme_colors.dart';
import 'package:bus/constant/path_route_name.dart';
import 'package:bus/data/models/buses_models/buses_info_models.dart';
import 'package:bus/data/models/pickup_location_local_models/pickup_location_local_models.dart';
import 'package:bus/data/models/student_models/student_grade_models.dart';
import 'package:bus/data/models/student_models/student_model.dart';
import 'package:bus/helper/network_serviecs.dart';
import 'package:bus/translations/local_keys.g.dart';
import 'package:bus/utils/assets_utils.dart';
import 'package:bus/views/custom_widgets/custom_button.dart';
import 'package:bus/views/custom_widgets/custom_drop_down_button.dart';
import 'package:bus/views/custom_widgets/custom_form_field_border.dart';
import 'package:bus/views/custom_widgets/custom_text.dart';
import 'package:bus/widgets/custom_appbar.dart';
import 'package:dio/dio.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:image_picker/image_picker.dart';
import 'package:open_street_map_search_and_pick/open_street_map_search_and_pick.dart';
import '../../../config/config_base.dart';
import '../../../widgets/pick_location_widget.dart';

class AddStudentScreen extends StatefulWidget {
  static const String routeName = PathRouteName.addStudent;
  final bool isEdit;
  final bool isFromBus;
  final int? previousBusId;
  final StudentModel? studentModel;

  AddStudentScreen({
    Key? key,
    required this.isEdit,
    this.studentModel,
    this.isFromBus = false,
    this.previousBusId,
  }) : super(key: key);

  @override
  State<AddStudentScreen> createState() => _AddStudentScreenState();
}

class _AddStudentScreenState extends State<AddStudentScreen> {
  File? image;
  int? gradeId;
  String name = "";
  String address = '';
  int? genderId;
  int? classroomId;
  int? busId;
  LatLong? position;
  PickedData? pickedData;
  PickupLocationLocalModels? pickupLocationLocalModels;
  DateTime? dateBirth;
  TextEditingController? txc_phoneNumber;
  TextEditingController? txc_name;
  TextEditingController? txc_city;
  TextEditingController? txc_Address;
  bool isLoading = false;
  String dropdownValue = AppStrings.fullDay.tr();
  final List<String> items = [
    AppStrings.fullDay.tr(),
    AppStrings.morningTrip.tr(),
    AppStrings.eveningTrip.tr(),
  ];
  String phone_number = "";
  bool isGradeChanged = false;
  String? city_name;
  final _formKey = GlobalKey<FormState>();

  @override
  void initState() {
    try {
      super.initState();
      _initializeControllers();
      _fetchInitialData();
    } catch (e, stackTrace) {
      _logError("Error in initState", e, stackTrace);
    }
  }

  void _initializeControllers() {
    try {
      txc_name = TextEditingController(text: widget.studentModel?.name);
      txc_city = TextEditingController(text: widget.studentModel?.city_name);
      txc_Address = TextEditingController(text: widget.studentModel?.address);
      txc_phoneNumber = TextEditingController(text: widget.studentModel?.phone);
    } catch (e, stackTrace) {
      _logError("Error initializing controllers", e, stackTrace);
    }
  }

  void _fetchInitialData() {
    try {
      BusesCubit.get(context).getAllBuses();
      context.read<GenderCubit>().getGender();

      if (widget.isEdit && widget.studentModel != null) {
        _setEditModeData();
        // Fetch classroom data based on the selected grade
        context
            .read<ClassRoomCubit>()
            .getClassRoom(widget.studentModel!.grade_id!);
      } else {
        gradeId = 0;
        address = '';
      }
    } catch (e, stackTrace) {
      _logError("Error fetching initial data", e, stackTrace);
    }
  }

  void _setEditModeData() {
    try {
      gradeId = int.parse(widget.studentModel?.grade_id.toString() ?? "0");
      genderId = widget.studentModel?.gender_id ?? 0;
      busId = widget.studentModel?.bus_id ?? 0;
      name = widget.studentModel?.name ?? '';
      dropdownValue = widget.studentModel?.trip_type == "end_day"
          ? items[2]
          : widget.studentModel?.trip_type == "start_day"
              ? items[1]
              : items[0];

      if (widget.studentModel?.classroom_id.toString().isNotEmpty ?? false) {
        classroomId = int.parse(widget.studentModel!.classroom_id.toString());
      }

      phone_number = widget.studentModel?.phone ?? "";
      position = widget.studentModel?.latitude != null ||
              widget.studentModel?.longitude != null
          ? LatLong(double.parse(widget.studentModel!.latitude!),
              double.parse(widget.studentModel!.longitude!))
          : null;
      address = widget.studentModel?.address ?? '';
      city_name = widget.studentModel?.city_name ?? "";
    } catch (e, stackTrace) {
      _logError("Error setting edit mode data", e, stackTrace);
    }
  }

  Future<void> getImage() async {
    try {
      final imageTest =
          await ImagePicker().pickImage(source: ImageSource.gallery);
      if (imageTest != null) {
        setState(() {
          image = File(imageTest.path);
        });
      }
    } on PlatformException catch (e, stackTrace) {
      _logError("Error picking image", e, stackTrace);
    } catch (e, stackTrace) {
      _logError("Unexpected error picking image", e, stackTrace);
    }
  }

  Future<void> uploadStudent() async {
    if (!_formKey.currentState!.validate()) {
      _formKey.currentState!.save();
      return;
    }

    setState(() {
      isLoading = true;
    });

    try {
      if (gradeId != 0 && classroomId != 0) {
        await _performUpload();
      } else {
        _showSnackBar('يجب اختيار المرحلة الدراسية والصف', TColor.redAccent);
      }
    } catch (e, stackTrace) {
      _logError("Error uploading student data", e, stackTrace);
      _showSnackBar(
          'An error occurred while uploading student data', TColor.redAccent);
    } finally {
      setState(() {
        isLoading = false;
      });
    }
  }

  Response? performUploadResponse;
  Future<void> _performUpload() async {
    try {
      // Validate required fields
      if (name.isEmpty) {
        _showSnackBar('Please fill all required fields', TColor.redAccent);
        return;
      }

      MultipartFile? file__lol;
      if (image != null) {
        file__lol = await MultipartFile.fromFile(image!.path,
            filename: image!.path.split('/').last);
      }

      Map<String, dynamic> datamap = {
        "trip_type": dropdownValue == items[2]
            ? "end_day"
            : dropdownValue == items[1]
                ? "start_day"
                : "full_day",
        "name": name,
        "phone": phone_number,
        "Date_Birth": _formatDate(dateBirth),
        "grade_id": gradeId,
        "city_name": city_name,
        "bus_id": busId,
        "address": address,
        "gender_id": genderId,
        "classroom_id": classroomId,
        if (position != null) "latitude": position!.latitude,
        if (position != null) "longitude": position!.longitude,
      }..removeWhere(
          (key, value) => value == null || value == "" || value == 0);

      if (file__lol != null) {
        datamap.addAll({"logo": file__lol});
      }

      FormData formData = FormData.fromMap(datamap);
      final authHeaders = {'Authorization': "Bearer $token"};

      // Log the request details
      debugPrint(
          "Sending POST request to: ${ConfigBase.baseUrl}students/${widget.isEdit ? "update/${widget.studentModel?.id}" : "store"}");
      debugPrint("Request Headers: $authHeaders");
      debugPrint("Request Data: $datamap");

      performUploadResponse = await NetworkService().dio.post(
            "${ConfigBase.baseUrl}students/${widget.isEdit ? "update/${widget.studentModel?.id}" : "store"}",
            options: Options(headers: authHeaders),
            data: formData,
          );
      context.read<StudentCubit>().getStudent(page: 1, isFirst: true);
      debugPrint("Response Status Code: ${performUploadResponse!.statusCode}");
      debugPrint("Response Data: ${performUploadResponse!.data}");
      _showSnackBar(
        widget.isEdit
            ? AppStrings.studentEdited.tr()
            : AppStrings.studentAdded.tr(),
        TColor.greenSuccess,
      );
      Navigator.pop(context);
    } on DioException catch (e, stackTrace) {
      debugPrint(
          "DioException during upload" ", error: $e, stackTrace: $stackTrace");
      _logError("DioException during upload", e, stackTrace);
      _showSnackBar(e.response?.data["messages"]?.toString() ?? "error",
          TColor.redAccent);
    } catch (e, stackTrace) {
      debugPrint("Unexpected error during upload"
          ", error: $e, stackTrace: $stackTrace");
      _logError("Unexpected error during upload", e, stackTrace);
      _showSnackBar(
          performUploadResponse?.data["messages"]?.toString() ??
              "Unknown error",
          TColor.redAccent);
    }
  }

  String _formatDate(DateTime? datetime) {
    try {
      if (datetime == null) return "";
      return "${datetime.year}-${datetime.month.toString().padLeft(2, '0')}-${datetime.day.toString().padLeft(2, '0')}";
    } catch (e, stackTrace) {
      _logError("Error formatting date", e, stackTrace);
      return "";
    }
  }

  void _logError(String message, dynamic error, StackTrace stackTrace) {
    debugPrint("$message: $error");
    log("$message: $error", stackTrace: stackTrace);
  }

  void _showSnackBar(String message, Color backgroundColor) {
    try {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          backgroundColor: backgroundColor,
          content: CustomText(
            text: message,
            fontSize: 18,
            maxLine: 5,
            color: TColor.white,
          ),
        ),
      );
    } catch (e, stackTrace) {
      _logError("Error showing SnackBar", e, stackTrace);
    }
  }

  @override
  Widget build(BuildContext context) {
    try {
      return Scaffold(
        appBar: CustomAppBar(
          titleWidget: CustomText(
            text: widget.isEdit
                ? AppStrings.editStudent.tr()
                : AppStrings.addStudent.tr(),
            fontSize: 18,
            textAlign: TextAlign.center,
            fontW: FontWeight.w600,
            color: TColor.white,
          ),
          leftWidget: InkWell(
            onTap: () => Navigator.pop(context),
            child: SvgPicture.asset(
              context.locale.toString() == "ar"
                  ? AppAssets.arrowBack
                  : AppAssets.forwardArrow,
              colorFilter:
                  const ColorFilter.mode(TColor.white, BlendMode.srcIn),
              width: 25.w,
              height: 25.w,
            ),
          ),
        ),
        body: SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              _buildProfileImage(),
              _buildForm(),
              _buildActionButton(),
            ],
          ),
        ),
      );
    } catch (e, stackTrace) {
      _logError("Error in main build method", e, stackTrace);
      return const Scaffold(
        body: Center(
          child: CustomText(
            text: 'An error occurred. Please try again later.',
            fontSize: 18,
            color: TColor.redAccent,
          ),
        ),
      );
    }
  }

  Widget _buildProfileImage() {
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: 37.w),
      child: Center(
        child: InkWell(
          onTap: getImage,
          child: CircleAvatar(
            radius: 35.r,
            backgroundColor: TColor.borderContainer,
            child: CircleAvatar(
              radius: 34.r,
              backgroundColor: TColor.white,
              backgroundImage: image != null
                  ? FileImage(image!)
                  : widget.studentModel?.logo_path != null
                      ? NetworkImage(widget.studentModel!.logo_path!)
                      : null,
              child: image == null && widget.studentModel?.logo_path == null
                  ? Image.asset(
                      assetsImages("pc.png"),
                      width: 25.w,
                      height: 25.w,
                    )
                  : null,
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildForm() {
    return Form(
      key: _formKey,
      child: Column(
        children: [
          _buildTextField(
              AppStrings.studentName.tr(), txc_name, (value) => name = value),
          _buildTextField(
              AppStrings.address.tr(), txc_Address, (value) => address = value),
          _buildTextField(AppStrings.phoneNumber.tr(), txc_phoneNumber,
              (value) => phone_number = value,
              inputType: TextInputType.phone),
          _buildDropdown(AppStrings.grade.tr(), _buildGradeDropdown()),
          _buildDropdown(AppStrings.classroom.tr(), _buildClassroomDropdown()),
          _buildDropdown(AppStrings.gender.tr(), _buildGenderDropdown()),
          _buildDropdown(AppStrings.busName.tr(), _buildBusDropdown()),
          _buildTripTypeDropdown(),
          _buildLocationPicker(),
          SizedBox(height: 25.h),
        ],
      ),
    );
  }

  Widget _buildTextField(String label, TextEditingController? controller,
      Function(String) onChanged,
      {TextInputType? inputType}) {
    try {
      return CustomFormFieldWithBorder(
        inputType: inputType,
        isTitled: true,
        title: label,
        controller: controller,
        onChanged: onChanged,
        formFieldWidth: 428,
        heightA: 53,
        hintText: label,
        borderColor: TColor.fillFormFieldB,
        fillColor: TColor.fillFormFieldB,
        radiusNumber: 12.0,
        paddingRight: 37.w,
        paddingLeft: 37.w,
        contentPaddingVertical: 15,
        contentPaddingHorizontal: 15,
      );
    } catch (e, stackTrace) {
      _logError("Error building text field", e, stackTrace);
      return const SizedBox();
    }
  }

  Widget _buildDropdown(String label, Widget dropdown) {
    try {
      return Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Padding(
            padding: EdgeInsets.only(
                left: 37.w, right: 37.w, bottom: 10.h, top: 15.h),
            child: CustomText(
              text: label,
              fontSize: 15,
              textAlign: TextAlign.center,
              fontW: FontWeight.bold,
              color: TColor.black,
            ),
          ),
          dropdown,
        ],
      );
    } catch (e, stackTrace) {
      _logError("Error building dropdown", e, stackTrace);
      return const SizedBox();
    }
  }

  Widget _buildGradeDropdown() {
    return BlocBuilder<GradeCubit, GradeStates>(
      builder: (context, state) {
        try {
          if (state is GradeLoadingStates) {
            return const Center(
                child: CircularProgressIndicator(color: TColor.mainColor));
          } else if (state is GradeSuccessStates) {
            List<StudentGradeModels> grades = [
              StudentGradeModels(id: 0, name: AppStrings.selectGrade.tr()),
              ...?state.gradeModels?.data,
            ];
            return CustomDropDownButton(
              items: grades
                  .map((value) => DropdownMenuItem<int>(
                        value: value.id,
                        child: Padding(
                          padding: EdgeInsets.symmetric(horizontal: 15.w),
                          child: Text(_translateGrade(value.name)),
                        ),
                      ))
                  .toList(),
              onChanged: (v) {
                setState(() => gradeId = v);
                if (v != 0) {
                  context.read<ClassRoomCubit>().getClassRoom(v!);
                }
                isGradeChanged = true;
              },
              value: gradeId,
            );
          } else {
            return const SizedBox();
          }
        } catch (e, stackTrace) {
          _logError("Error building grade dropdown", e, stackTrace);
          return const SizedBox();
        }
      },
    );
  }

  String _translateGrade(String? grade) {
    try {
      if (context.locale.toString() == "ar") {
        switch (grade) {
          case 'KG':
            return 'الروضة';
          case 'secondary':
            return 'الثانوية';
          case 'Middle':
            return 'الإعدادية';
          case 'Primary':
            return 'الإبتدائية';
          default:
            return grade ?? '';
        }
      }
      return grade ?? '';
    } catch (e, stackTrace) {
      _logError("Error translating grade", e, stackTrace);
      return grade ?? '';
    }
  }

  Widget _buildClassroomDropdown() {
    if (gradeId == 0) {
      return _buildPlaceholder(AppStrings.selectEduLev.tr());
    }
    return BlocBuilder<ClassRoomCubit, ClassRoomState>(
      builder: (context, state) {
        try {
          if (state is ClassRoomLoadingStates) {
            return const Center(
                child: CircularProgressIndicator(color: TColor.mainColor));
          } else if (state is ClassRoomSuccessStates) {
            List<ClassRoomModel?> classrooms =
                state.classRoomModels?.classRooms ?? [];

            // Ensure classroomId is set to a valid value or null
            if (isGradeChanged) {
              classroomId = classrooms.isNotEmpty ? classrooms[0]?.id : null;
              isGradeChanged = false;
            }

            // Check if the current classroomId exists in the classrooms list
            final isValidClassroomId =
                classrooms.any((classroom) => classroom?.id == classroomId);

            // If classroomId is not valid, reset it to null
            if (!isValidClassroomId) {
              classroomId = null;
            }

            return CustomDropDownButton(
              items: classrooms
                  .map((value) => DropdownMenuItem<int>(
                        value: value?.id,
                        child: Padding(
                          padding: EdgeInsets.symmetric(horizontal: 15.w),
                          child: Text(value?.name ?? ''),
                        ),
                      ))
                  .toList(),
              onChanged: (v) => setState(() => classroomId = v),
              value: classroomId,
            );
          } else {
            return _buildPlaceholder(AppStrings.selectEduLev.tr());
          }
        } catch (e, stackTrace) {
          _logError("Error building classroom dropdown", e, stackTrace);
          return _buildPlaceholder(AppStrings.selectEduLev.tr());
        }
      },
    );
  }

  Widget _buildGenderDropdown() {
    return BlocBuilder<GenderCubit, GenderStates>(
      builder: (context, state) {
        try {
          if (state is GenderLoadingStates) {
            return const Center(
                child: CircularProgressIndicator(color: TColor.mainColor));
          } else if (state is GenderSuccessStates) {
            List<StudentGradeModels> genders = [
              StudentGradeModels(id: 0, name: AppStrings.selectGender.tr()),
              ...?state.genderModels?.data,
            ];
            return CustomDropDownButton(
              items: genders
                  .map((value) => DropdownMenuItem<int>(
                        value: value.id,
                        child: Padding(
                          padding: EdgeInsets.symmetric(horizontal: 15.w),
                          child: Text(_translateGender(value.name)),
                        ),
                      ))
                  .toList(),
              onChanged: (v) => setState(() => genderId = v),
              value: genderId,
            );
          } else {
            return const SizedBox();
          }
        } catch (e, stackTrace) {
          _logError("Error building gender dropdown", e, stackTrace);
          return const SizedBox();
        }
      },
    );
  }

  String _translateGender(String? gender) {
    try {
      if (context.locale.toString() == "en") {
        switch (gender) {
          case 'ذكر':
            return 'Male';
          case 'انثي':
            return 'Female';
          default:
            return gender ?? '';
        }
      }
      return gender ?? '';
    } catch (e, stackTrace) {
      _logError("Error translating gender", e, stackTrace);
      return gender ?? '';
    }
  }

  Widget _buildBusDropdown() {
    return BlocBuilder<BusesCubit, BusesState>(
      builder: (context, state) {
        try {
          if (state is BusesLoadingStates) {
            return const Center(
                child: CircularProgressIndicator(color: TColor.mainColor));
          } else if (state is BusesSuccessStates) {
            List<BusesInfoModel> buses = [
              BusesInfoModel(id: 0, name: AppStrings.selectBus.tr()),
              ...?state.busesDataModels?.data,
            ];
            return CustomDropDownButton(
              items: buses
                  .map((value) => DropdownMenuItem<int>(
                        value: value.id,
                        child: Padding(
                          padding: EdgeInsets.symmetric(horizontal: 15.w),
                          child: Text(value.name ?? ''),
                        ),
                      ))
                  .toList(),
              onChanged: (v) => setState(() => busId = v),
              value: busId,
            );
          } else {
            return Center(
                child: CustomText(text: AppStrings.busesNotFound.tr()));
          }
        } catch (e, stackTrace) {
          _logError("Error building bus dropdown", e, stackTrace);
          return Center(child: CustomText(text: AppStrings.busesNotFound.tr()));
        }
      },
    );
  }

  Widget _buildTripTypeDropdown() {
    try {
      return Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Padding(
            padding: EdgeInsets.only(
                left: 37.w, right: 37.w, bottom: 10.h, top: 15.h),
            child: CustomText(
              text: AppStrings.busSubscription.tr(),
              fontSize: 15,
              textAlign: TextAlign.center,
              fontW: FontWeight.bold,
              color: TColor.black,
            ),
          ),
          Container(
            width: double.infinity,
            padding: EdgeInsets.symmetric(horizontal: 15.w),
            margin: EdgeInsets.symmetric(horizontal: 37.w),
            decoration: BoxDecoration(
              border: Border.all(
                  color: TColor.mainColor.withAlpha(100), width: 1.5),
              borderRadius: BorderRadius.circular(12.r),
            ),
            child: DropdownButton(
              isExpanded: true,
              underline: const SizedBox(),
              value: dropdownValue,
              items: items
                  .map((String item) => DropdownMenuItem(
                        value: item,
                        child: Text(item),
                      ))
                  .toList(),
              onChanged: (String? newValue) =>
                  setState(() => dropdownValue = newValue!),
            ),
          ),
        ],
      );
    } catch (e, stackTrace) {
      _logError("Error building trip type dropdown", e, stackTrace);
      return const SizedBox();
    }
  }

  Widget _buildLocationPicker() {
    try {
      return Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Padding(
            padding: EdgeInsets.only(
                left: 37.w, right: 37.w, bottom: 10.h, top: 15.h),
            child: Row(
              children: [
                CustomText(
                  text: AppStrings.getLocations.tr(),
                  fontSize: 15,
                  fontW: FontWeight.bold,
                  color: TColor.black,
                ),
                const Spacer(),
                if (position != null &&
                    position!.latitude != 0.0 &&
                    position!.longitude != 0.0)
                  Container(
                    decoration: BoxDecoration(
                      color: TColor.mainColor.withAlpha(20),
                      borderRadius: BorderRadius.circular(12.r),
                    ),
                    padding:
                        EdgeInsets.symmetric(horizontal: 10.w, vertical: 5.h),
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Icon(
                          Icons.location_on,
                          color: TColor.mainColor,
                          size: 16.w,
                        ),
                        SizedBox(width: 4.w),
                        const CustomText(
                          text: "تم تحديد الموقع",
                          fontSize: 12,
                          color: TColor.mainColor,
                          fontW: FontWeight.w500,
                        ),
                      ],
                    ),
                  ),
              ],
            ),
          ),
          Padding(
            padding: EdgeInsets.symmetric(horizontal: 37.w),
            child: Column(
              children: [
                // Location input field
                InkWell(
                  onTap: () async {
                    try {
                      pickupLocationLocalModels = await Navigator.push(
                        context,
                        MaterialPageRoute(
                          builder: (ctx) => PickLocationWidget(
                            latitude: position?.latitude ?? 0.0,
                            longitude: position?.longitude ?? 0.0,
                          ),
                        ),
                      );
                      setState(() {
                        position = LatLong(
                          pickupLocationLocalModels?.lat ?? 0.0,
                          pickupLocationLocalModels?.long ?? 0.0,
                        );
                      });
                    } catch (e, stackTrace) {
                      _logError("Error picking location", e, stackTrace);
                    }
                  },
                  child: Container(
                    width: 428.w,
                    height: 53.w,
                    decoration: BoxDecoration(
                      color: TColor.fillFormFieldB,
                      borderRadius: BorderRadius.circular(12.r),
                      border: position != null &&
                              position!.latitude != 0.0 &&
                              position!.longitude != 0.0
                          ? Border.all(
                              color: TColor.mainColor.withAlpha(100),
                              width: 1.5)
                          : null,
                    ),
                    child: Padding(
                      padding: context.locale.toString() == "ar"
                          ? EdgeInsets.only(top: 15.w, right: 15.w)
                          : EdgeInsets.only(top: 15.w, left: 15.w),
                      child: Row(
                        children: [
                          Expanded(
                            child: CustomText(
                              text: pickupLocationLocalModels?.address ??
                                  AppStrings.getLocations.tr(),
                              color: pickupLocationLocalModels?.address != null
                                  ? TColor.black
                                  : TColor.tabColors,
                              fontSize: 15,
                              fontW: FontWeight.w500,
                              maxLine: 1,
                              overflow: TextOverflow.ellipsis,
                            ),
                          ),
                          Icon(
                            Icons.location_on,
                            color: TColor.mainColor,
                            size: 20.w,
                          ),
                          SizedBox(width: 10.w),
                        ],
                      ),
                    ),
                  ),
                ),
                // Map preview
                if (position != null &&
                    position!.latitude != 0.0 &&
                    position!.longitude != 0.0)
                  Container(
                    margin: EdgeInsets.only(top: 12.h),
                    height: 180.h,
                    width: 428.w,
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(12.r),
                      border: Border.all(
                          color: TColor.mainColor.withAlpha(100), width: 1.5),
                      color: TColor.white,
                      boxShadow: [
                        BoxShadow(
                          color: Colors.black.withAlpha(25),
                          spreadRadius: 1,
                          blurRadius: 5,
                          offset: const Offset(0, 2),
                        ),
                      ],
                    ),
                    clipBehavior: Clip.antiAlias,
                    child: Stack(
                      children: [
                        GoogleMap(
                          initialCameraPosition: CameraPosition(
                            target:
                                LatLng(position!.latitude, position!.longitude),
                            zoom: 15,
                          ),
                          markers: {
                            Marker(
                              markerId: const MarkerId('0'),
                              position: LatLng(
                                  position!.latitude, position!.longitude),
                              icon: BitmapDescriptor.defaultMarkerWithHue(
                                  BitmapDescriptor.hueRed),
                            ),
                          },
                          zoomControlsEnabled: false,
                          mapToolbarEnabled: false,
                          myLocationButtonEnabled: false,
                          scrollGesturesEnabled: false,
                          zoomGesturesEnabled: false,
                          rotateGesturesEnabled: false,
                          tiltGesturesEnabled: false,
                          compassEnabled: false,
                          liteModeEnabled: false,
                        ),
                        // Address bar at bottom
                        Positioned(
                          bottom: 0,
                          left: 0,
                          right: 0,
                          child: Container(
                            padding: EdgeInsets.symmetric(
                                vertical: 8.h, horizontal: 12.w),
                            decoration: BoxDecoration(
                              color: TColor.white.withAlpha(240),
                              border: Border(
                                  top: BorderSide(
                                      color: TColor.mainColor.withAlpha(50),
                                      width: 1)),
                            ),
                            child: Row(
                              children: [
                                Icon(
                                  Icons.location_on,
                                  color: TColor.mainColor,
                                  size: 16.w,
                                ),
                                SizedBox(width: 6.w),
                                Expanded(
                                  child: CustomText(
                                    text: pickupLocationLocalModels?.address ??
                                        '',
                                    fontSize: 13,
                                    maxLine: 1,
                                    overflow: TextOverflow.ellipsis,
                                    color: TColor.black,
                                    fontW: FontWeight.w500,
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ),
                        // Control buttons
                        Positioned(
                          top: 10,
                          right: 10,
                          child: Column(
                            children: [
                              // Edit location button
                              InkWell(
                                onTap: () async {
                                  try {
                                    pickupLocationLocalModels =
                                        await Navigator.push(
                                      context,
                                      MaterialPageRoute(
                                        builder: (ctx) => PickLocationWidget(
                                          latitude: position?.latitude ?? 0.0,
                                          longitude: position?.longitude ?? 0.0,
                                        ),
                                      ),
                                    );
                                    setState(() {
                                      position = LatLong(
                                        pickupLocationLocalModels?.lat ?? 0.0,
                                        pickupLocationLocalModels?.long ?? 0.0,
                                      );
                                    });
                                  } catch (e, stackTrace) {
                                    _logError("Error picking location", e,
                                        stackTrace);
                                  }
                                },
                                child: Container(
                                  padding: EdgeInsets.all(10.w),
                                  decoration: BoxDecoration(
                                    color: TColor.white,
                                    shape: BoxShape.circle,
                                    boxShadow: [
                                      BoxShadow(
                                        color: Colors.black.withAlpha(25),
                                        spreadRadius: 1,
                                        blurRadius: 3,
                                        offset: const Offset(0, 1),
                                      ),
                                    ],
                                  ),
                                  child: Icon(
                                    Icons.edit_location_alt_outlined,
                                    color: TColor.mainColor,
                                    size: 22.w,
                                  ),
                                ),
                              ),
                              SizedBox(height: 12.h),
                              // View full map button
                              InkWell(
                                onTap: () {
                                  Navigator.push(
                                    context,
                                    MaterialPageRoute(
                                      builder: (ctx) => Scaffold(
                                        appBar: const CustomAppBar(
                                          titleWidget: CustomText(
                                            text: "تفاصيل الموقع",
                                            fontSize: 18,
                                            textAlign: TextAlign.center,
                                            fontW: FontWeight.w600,
                                            color: TColor.white,
                                          ),
                                        ),
                                        body: GoogleMap(
                                          initialCameraPosition: CameraPosition(
                                            target: LatLng(position!.latitude,
                                                position!.longitude),
                                            zoom: 16,
                                          ),
                                          markers: {
                                            Marker(
                                              markerId: const MarkerId('0'),
                                              position: LatLng(
                                                  position!.latitude,
                                                  position!.longitude),
                                              infoWindow: InfoWindow(
                                                title: pickupLocationLocalModels
                                                        ?.address ??
                                                    '',
                                              ),
                                            ),
                                          },
                                          zoomControlsEnabled: true,
                                          mapToolbarEnabled: true,
                                          myLocationButtonEnabled: true,
                                        ),
                                      ),
                                    ),
                                  );
                                },
                                child: Container(
                                  padding: EdgeInsets.all(10.w),
                                  decoration: BoxDecoration(
                                    color: TColor.white,
                                    shape: BoxShape.circle,
                                    boxShadow: [
                                      BoxShadow(
                                        color: Colors.black.withAlpha(25),
                                        spreadRadius: 1,
                                        blurRadius: 3,
                                        offset: const Offset(0, 1),
                                      ),
                                    ],
                                  ),
                                  child: Icon(
                                    Icons.fullscreen,
                                    color: TColor.mainColor,
                                    size: 22.w,
                                  ),
                                ),
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                  ),
              ],
            ),
          ),
        ],
      );
    } catch (e, stackTrace) {
      _logError("Error building location picker", e, stackTrace);
      return const SizedBox();
    }
  }

  Widget _buildPlaceholder(String text) {
    try {
      return Padding(
        padding: EdgeInsets.symmetric(horizontal: 37.w),
        child: Container(
          width: 428.w,
          height: 53.w,
          decoration: BoxDecoration(
            color: TColor.fillFormFieldB,
            borderRadius: BorderRadius.circular(12.r),
          ),
          child: Center(child: Text(text)),
        ),
      );
    } catch (e, stackTrace) {
      _logError("Error building placeholder", e, stackTrace);
      return const SizedBox();
    }
  }

  Widget _buildActionButton() {
    try {
      return Padding(
        padding: EdgeInsets.symmetric(horizontal: 37.w),
        child: CustomButton(
          loading: isLoading,
          text: widget.isEdit ? AppStrings.save.tr() : AppStrings.add.tr(),
          onTap: uploadStudent,
          width: 428,
          height: 53,
          radius: 12,
          borderColor: TColor.mainColor,
          bgColor: TColor.mainColor,
        ),
      );
    } catch (e, stackTrace) {
      _logError("Error building action button", e, stackTrace);
      return const SizedBox();
    }
  }

  @override
  void dispose() {
    try {
      txc_name?.dispose();
      txc_city?.dispose();
      txc_Address?.dispose();
      txc_phoneNumber?.dispose();
      super.dispose();
    } catch (e, stackTrace) {
      _logError("Error in dispose method", e, stackTrace);
    }
  }
}
