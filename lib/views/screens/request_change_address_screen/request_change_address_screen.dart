import 'package:bus/bloc/change_address_cubit/change_address_states.dart';
import 'package:bus/config/theme_colors.dart';
import 'package:bus/constant/path_route_name.dart';
import 'package:bus/data/repo/change_adress_requests_repo.dart';
import 'package:bus/translations/local_keys.g.dart';
import 'package:bus/utils/assets_utils.dart';
import 'package:bus/views/custom_widgets/custom_text.dart';
import 'package:bus/widgets/custom_appbar.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';

import '../../../bloc/notifications_cubit/notifications_cubit.dart';
import '../../../data/repo/notifications_repo.dart';
import '../../../utils/helper.dart';
import '../requests_address_change_screen/requests_address_change_screen.dart';

class RequestChangeAddressScreen extends StatelessWidget {
  static const String routeName = PathRouteName.requestChangeAddress;
  const RequestChangeAddressScreen({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final args = ModalRoute.of(context)!.settings.arguments as CAdreessModels;

    // Determine status color and icon
    Color statusColor;
    String statusText;
    IconData statusIcon;
    bool isNewRequest = args.status == 3;

    switch (args.status) {
      case 0:
        statusColor = Colors.orange;
        statusText = AppStrings.newS.tr();
        statusIcon = Icons.pending_outlined;
        break;
      case 1:
        statusColor = Colors.green;
        statusText = AppStrings.accepted.tr();
        statusIcon = Icons.check_circle_outline;
        break;
      case 2:
        statusColor = Colors.red;
        statusText = AppStrings.refused.tr();
        statusIcon = Icons.cancel_outlined;
        break;
      case 3:
        statusColor = Colors.orange;
        statusText = AppStrings.newS.tr();
        statusIcon = Icons.pending_outlined;
        break;
      default:
        statusColor = Colors.grey;
        statusText = args.statusText?.text ?? '';
        statusIcon = Icons.help_outline;
    }

    return Scaffold(
      appBar: CustomAppBar(
        titleWidget: CustomText(
          text: AppStrings.requestStatus.tr(),
          fontSize: 18,
          textAlign: TextAlign.center,
          fontW: FontWeight.w600,
          color: TColor.white,
        ),
        leftWidget: context.locale.toString() == "ar"
            ? InkWell(
                onTap: () {
                  Navigator.pop(context);
                },
                child: SvgPicture.asset(AppAssets.arrowBack),
              )
            : InkWell(
                onTap: () {
                  Navigator.pop(context);
                },
                child: SvgPicture.asset(
                  AppAssets.forwardArrow,
                  colorFilter:
                      const ColorFilter.mode(TColor.white, BlendMode.srcIn),
                  width: 25.w,
                  height: 25.w,
                ),
              ),
      ),
      body: Stack(
        children: [
          // Status bar at top
          Container(
            height: 40.h,
            width: double.infinity,
            color: statusColor,
          ),

          // Main content
          SingleChildScrollView(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Main info card
                _buildMainInfoCard(args, statusColor, statusText, statusIcon),

                // Student info card
                _buildStudentInfoCard(args),

                // Bus info card
                _buildBusInfoCard(args),

                // School info card
                if (args.schoolName.isNotEmpty || args.gradeName.isNotEmpty)
                  _buildSchoolInfoCard(args),

                // Action buttons (if new request)
                if (isNewRequest)
                  _buildActionButtons(context, args),

                // Bottom spacing
                SizedBox(height: 24.h),
              ],
            ),
          ),
        ],
      ),
    );
  }

  // Main info card
  Widget _buildMainInfoCard(CAdreessModels args, Color statusColor, String statusText, IconData statusIcon) {
    return Card(
      margin: EdgeInsets.only(top: 24.h, left: 16.r, right: 16.r),
      elevation: 4,
      shadowColor: Colors.black38,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(20.r),
        side: BorderSide(
          color: statusColor.withOpacity(0.3),
          width: 1.5,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Status bar at top
          Container(
            width: double.infinity,
            padding: EdgeInsets.symmetric(vertical: 12.h, horizontal: 16.w),
            decoration: BoxDecoration(
              color: statusColor.withOpacity(0.15),
              borderRadius: BorderRadius.only(
                topLeft: Radius.circular(20.r),
                topRight: Radius.circular(20.r),
              ),
            ),
            child: Row(
              children: [
                Icon(
                  statusIcon,
                  size: 22,
                  color: statusColor,
                ),
                SizedBox(width: 8.w),
                CustomText(
                  text: statusText,
                  fontSize: 16,
                  fontW: FontWeight.w600,
                  color: statusColor,
                ),
              ],
            ),
          ),

          // Address content
          Padding(
            padding: EdgeInsets.all(20.r),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Card title
                Row(
                  children: [
                    Container(
                      padding: EdgeInsets.all(8.r),
                      decoration: BoxDecoration(
                        color: TColor.mainColor.withOpacity(0.1),
                        borderRadius: BorderRadius.circular(12.r),
                      ),
                      child: const Icon(
                        Icons.location_on,
                        color: TColor.mainColor,
                        size: 24,
                      ),
                    ),
                    SizedBox(width: 12.w),
                    const Expanded(
                      child: CustomText(
                        text: "معلومات تغيير العنوان",
                        fontSize: 18,
                        fontW: FontWeight.w700,
                        color: TColor.mainColor,
                      ),
                    ),
                  ],
                ),

                SizedBox(height: 20.h),

                // Old address
                if (args.old_address != null && args.old_address!.isNotEmpty) ...[
                  _buildAddressSection(
                    title: "العنوان الحالي",
                    address: args.old_address!,
                    icon: Icons.home,
                    color: Colors.orange.shade700,
                  ),
                  SizedBox(height: 16.h),
                ],

                // New address
                _buildAddressSection(
                  title: "العنوان الجديد",
                  address: args.address ?? "غير محدد",
                  icon: Icons.location_on,
                  color: Colors.green.shade700,
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  // Address section widget
  Widget _buildAddressSection({
    required String title,
    required String address,
    required IconData icon,
    required Color color,
  }) {
    return Container(
      width: double.infinity,
      padding: EdgeInsets.all(16.r),
      decoration: BoxDecoration(
        color: Colors.grey.shade50,
        borderRadius: BorderRadius.circular(16.r),
        border: Border.all(
          color: Colors.grey.shade200,
          width: 1,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                icon,
                size: 18,
                color: color,
              ),
              SizedBox(width: 8.w),
              CustomText(
                text: title,
                fontSize: 14,
                fontW: FontWeight.w500,
                color: Colors.grey.shade600,
              ),
            ],
          ),
          SizedBox(height: 8.h),
          CustomText(
            text: address,
            fontSize: 16,
            fontW: FontWeight.w600,
            color: TColor.black,
            maxLine: 3,
          ),
        ],
      ),
    );
  }

  // Student info card
  Widget _buildStudentInfoCard(CAdreessModels args) {
    return Card(
      margin: EdgeInsets.only(top: 16.h, left: 16.r, right: 16.r),
      elevation: 4,
      shadowColor: Colors.black.withAlpha(40),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(20.r),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Card header
          Container(
            width: double.infinity,
            padding: EdgeInsets.symmetric(vertical: 16.h, horizontal: 20.w),
            decoration: BoxDecoration(
              gradient: LinearGradient(
                colors: [Colors.blue.shade50, Colors.blue.shade100],
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
              ),
              borderRadius: BorderRadius.only(
                topLeft: Radius.circular(20.r),
                topRight: Radius.circular(20.r),
              ),
            ),
            child: Row(
              children: [
                Container(
                  padding: EdgeInsets.all(8.r),
                  decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(12.r),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.black.withAlpha(20),
                        blurRadius: 4,
                        offset: const Offset(0, 2),
                      ),
                    ],
                  ),
                  child: Icon(
                    Icons.person,
                    size: 24,
                    color: Colors.blue.shade700,
                  ),
                ),
                SizedBox(width: 12.w),
                const Expanded(
                  child: CustomText(
                    text: "معلومات الطالب",
                    fontSize: 18,
                    fontW: FontWeight.w700,
                    color: TColor.mainColor,
                  ),
                ),
              ],
            ),
          ),

          // Card content
          Padding(
            padding: EdgeInsets.all(20.r),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Student name
                _buildInfoItem(
                  icon: Icons.badge,
                  iconColor: Colors.indigo.shade700,
                  iconBgColor: Colors.indigo.withAlpha(20),
                  title: "اسم الطالب",
                  value: args.studentName,
                ),

                // Parent name
                if (args.parentName != null && args.parentName!.isNotEmpty) ...[
                  SizedBox(height: 16.h),
                  _buildInfoItem(
                    icon: Icons.family_restroom,
                    iconColor: Colors.purple.shade700,
                    iconBgColor: Colors.purple.withAlpha(20),
                    title: "ولي الأمر",
                    value: args.parentName!,
                  ),
                ],
              ],
            ),
          ),
        ],
      ),
    );
  }

  // Bus info card
  Widget _buildBusInfoCard(CAdreessModels args) {
    return Card(
      margin: EdgeInsets.only(top: 16.h, left: 16.r, right: 16.r),
      elevation: 4,
      shadowColor: Colors.black.withAlpha(40),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(20.r),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Card header
          Container(
            width: double.infinity,
            padding: EdgeInsets.symmetric(vertical: 16.h, horizontal: 20.w),
            decoration: BoxDecoration(
              gradient: LinearGradient(
                colors: [Colors.amber.shade50, Colors.amber.shade100],
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
              ),
              borderRadius: BorderRadius.only(
                topLeft: Radius.circular(20.r),
                topRight: Radius.circular(20.r),
              ),
            ),
            child: Row(
              children: [
                Container(
                  padding: EdgeInsets.all(8.r),
                  decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(12.r),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.black.withAlpha(20),
                        blurRadius: 4,
                        offset: const Offset(0, 2),
                      ),
                    ],
                  ),
                  child: Icon(
                    Icons.directions_bus,
                    size: 24,
                    color: Colors.amber.shade700,
                  ),
                ),
                SizedBox(width: 12.w),
                const Expanded(
                  child: CustomText(
                    text: "معلومات الحافلة",
                    fontSize: 18,
                    fontW: FontWeight.w700,
                    color: TColor.mainColor,
                  ),
                ),
              ],
            ),
          ),

          // Card content
          Padding(
            padding: EdgeInsets.all(20.r),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Bus name
                _buildInfoItem(
                  icon: Icons.directions_bus,
                  iconColor: Colors.red.shade700,
                  iconBgColor: Colors.red.withAlpha(20),
                  title: "اسم الحافلة",
                  value: args.busName,
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  // School info card
  Widget _buildSchoolInfoCard(CAdreessModels args) {
    return Card(
      margin: EdgeInsets.only(top: 16.h, left: 16.r, right: 16.r),
      elevation: 4,
      shadowColor: Colors.black.withAlpha(40),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(20.r),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Card header
          Container(
            width: double.infinity,
            padding: EdgeInsets.symmetric(vertical: 16.h, horizontal: 20.w),
            decoration: BoxDecoration(
              gradient: LinearGradient(
                colors: [Colors.green.shade50, Colors.green.shade100],
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
              ),
              borderRadius: BorderRadius.only(
                topLeft: Radius.circular(20.r),
                topRight: Radius.circular(20.r),
              ),
            ),
            child: Row(
              children: [
                Container(
                  padding: EdgeInsets.all(8.r),
                  decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(12.r),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.black.withAlpha(20),
                        blurRadius: 4,
                        offset: const Offset(0, 2),
                      ),
                    ],
                  ),
                  child: Icon(
                    Icons.school,
                    size: 24,
                    color: Colors.green.shade700,
                  ),
                ),
                SizedBox(width: 12.w),
                const Expanded(
                  child: CustomText(
                    text: "معلومات المدرسة",
                    fontSize: 18,
                    fontW: FontWeight.w700,
                    color: TColor.mainColor,
                  ),
                ),
              ],
            ),
          ),

          // Card content
          Padding(
            padding: EdgeInsets.all(20.r),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // School name
                if (args.schoolName.isNotEmpty)
                  _buildInfoItem(
                    icon: Icons.school,
                    iconColor: Colors.blue.shade700,
                    iconBgColor: Colors.blue.withAlpha(20),
                    title: "اسم المدرسة",
                    value: args.schoolName,
                  ),

                // Grade name
                if (args.gradeName.isNotEmpty) ...[
                  if (args.schoolName.isNotEmpty) SizedBox(height: 16.h),
                  _buildInfoItem(
                    icon: Icons.grade,
                    iconColor: Colors.purple.shade700,
                    iconBgColor: Colors.purple.withAlpha(20),
                    title: "الصف الدراسي",
                    value: args.gradeName,
                  ),
                ],
              ],
            ),
          ),
        ],
      ),
    );
  }

  // Info item widget
  Widget _buildInfoItem({
    required IconData icon,
    required Color iconColor,
    required Color iconBgColor,
    required String title,
    required String value,
  }) {
    return Container(
      padding: EdgeInsets.all(16.r),
      decoration: BoxDecoration(
        color: Colors.grey.shade50,
        borderRadius: BorderRadius.circular(16.r),
        border: Border.all(
          color: Colors.grey.shade200,
          width: 1,
        ),
      ),
      child: Row(
        children: [
          Container(
            padding: EdgeInsets.all(8.r),
            decoration: BoxDecoration(
              color: iconBgColor,
              shape: BoxShape.circle,
            ),
            child: Icon(
              icon,
              size: 20,
              color: iconColor,
            ),
          ),
          SizedBox(width: 12.w),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                CustomText(
                  text: title,
                  fontSize: 14,
                  fontW: FontWeight.w500,
                  color: Colors.grey.shade600,
                ),
                SizedBox(height: 4.h),
                CustomText(
                  text: value,
                  fontSize: 16,
                  fontW: FontWeight.w600,
                  color: TColor.black,
                  maxLine: 2,
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  // Action buttons card
  Widget _buildActionButtons(BuildContext context, CAdreessModels args) {
    return Card(
      margin: EdgeInsets.only(top: 16.h, left: 16.r, right: 16.r, bottom: 24.h),
      elevation: 4,
      shadowColor: Colors.black.withAlpha(40),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(20.r),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Card header
          Container(
            width: double.infinity,
            padding: EdgeInsets.symmetric(vertical: 16.h, horizontal: 20.w),
            decoration: BoxDecoration(
              gradient: LinearGradient(
                colors: [Colors.deepPurple.shade50, Colors.deepPurple.shade100],
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
              ),
              borderRadius: BorderRadius.only(
                topLeft: Radius.circular(20.r),
                topRight: Radius.circular(20.r),
              ),
            ),
            child: Row(
              children: [
                Container(
                  padding: EdgeInsets.all(8.r),
                  decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(12.r),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.black.withAlpha(20),
                        blurRadius: 4,
                        offset: const Offset(0, 2),
                      ),
                    ],
                  ),
                  child: Icon(
                    Icons.admin_panel_settings,
                    size: 24,
                    color: Colors.deepPurple.shade700,
                  ),
                ),
                SizedBox(width: 12.w),
                const Expanded(
                  child: CustomText(
                    text: "إجراءات الطلب",
                    fontSize: 18,
                    fontW: FontWeight.w700,
                    color: TColor.mainColor,
                  ),
                ),
              ],
            ),
          ),

          // Card content
          Padding(
            padding: EdgeInsets.all(20.r),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const CustomText(
                  text: "يرجى اختيار الإجراء المناسب لهذا الطلب:",
                  fontSize: 16,
                  fontW: FontWeight.w500,
                  color: Colors.grey,
                ),

                SizedBox(height: 24.h),

                // Action buttons
                Row(
                  children: [
                    // Reject button
                    Expanded(
                      child: _buildActionButton(
                        onPressed: () => _handleRejectRequest(context, args),
                        icon: Icons.close,
                        label: AppStrings.refusal.tr(),
                        backgroundColor: Colors.red.shade500,
                        gradient: LinearGradient(
                          colors: [Colors.red.shade400, Colors.red.shade600],
                          begin: Alignment.topLeft,
                          end: Alignment.bottomRight,
                        ),
                      ),
                    ),

                    SizedBox(width: 16.w),

                    // Accept button
                    Expanded(
                      child: _buildActionButton(
                        onPressed: () => _handleAcceptRequest(context, args),
                        icon: Icons.check,
                        label: AppStrings.accept.tr(),
                        backgroundColor: Colors.green.shade500,
                        gradient: LinearGradient(
                          colors: [Colors.green.shade400, Colors.green.shade600],
                          begin: Alignment.topLeft,
                          end: Alignment.bottomRight,
                        ),
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  // Action button widget
  Widget _buildActionButton({
    required VoidCallback onPressed,
    required IconData icon,
    required String label,
    required Color backgroundColor,
    required Gradient gradient,
  }) {
    return Container(
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(16.r),
        boxShadow: [
          BoxShadow(
            color: backgroundColor.withAlpha(60),
            blurRadius: 8,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: ElevatedButton(
        onPressed: onPressed,
        style: ElevatedButton.styleFrom(
          padding: EdgeInsets.zero,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(16.r),
          ),
          elevation: 0,
        ),
        child: Ink(
          decoration: BoxDecoration(
            gradient: gradient,
            borderRadius: BorderRadius.circular(16.r),
          ),
          child: Container(
            padding: EdgeInsets.symmetric(vertical: 16.h),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(
                  icon,
                  color: Colors.white,
                  size: 20,
                ),
                SizedBox(width: 8.w),
                CustomText(
                  text: label,
                  fontSize: 16,
                  fontW: FontWeight.w600,
                  color: Colors.white,
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  // Handle accept request
  void _handleAcceptRequest(BuildContext context, CAdreessModels args) async {
    // Show confirmation dialog
    bool? confirmed = await showDialog<bool>(
      context: context,
      barrierDismissible: false,
      builder: (dialogContext) => AlertDialog(
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(16.r),
        ),
        title: Row(
          children: [
            const Icon(
              Icons.check_circle,
              color: Colors.green,
              size: 28,
            ),
            SizedBox(width: 8.w),
            Expanded(
              child: CustomText(
                text: "تأكيد القبول",
                fontSize: 18,
                fontW: FontWeight.w600,
                color: TColor.mainColor,
              ),
            ),
          ],
        ),
        content: const CustomText(
          text: "هل أنت متأكد من قبول طلب تغيير العنوان؟",
          fontSize: 16,
          fontW: FontWeight.w400,
          maxLine: 3,
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(dialogContext, false),
            child: const CustomText(
              text: "إلغاء",
              fontSize: 16,
              fontW: FontWeight.w500,
              color: Colors.grey,
            ),
          ),
          ElevatedButton(
            onPressed: () => Navigator.pop(dialogContext, true),
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.green,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8.r),
              ),
            ),
            child: const CustomText(
              text: "قبول",
              fontSize: 16,
              fontW: FontWeight.w500,
              color: Colors.white,
            ),
          ),
        ],
      ),
    );

    if (confirmed == true) {
      // Show loading
      showDialog(
        context: context,
        barrierDismissible: false,
        builder: (loadingContext) => const Center(
          child: CircularProgressIndicator(
            color: TColor.mainColor,
          ),
        ),
      );

      try {
        bool isValid = await CAdreessRepo()
            .acceptChangeAddressRequest(changeAddressRequestId: args.id);

        // Check if context is still valid
        if (!context.mounted) return;
        Navigator.of(context).pop(); // Close loading

        if (isValid) {
          // Show success message
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              backgroundColor: TColor.greenSuccess,
              content: CustomText(
                text: AppStrings.successfullyDone.tr(),
                fontSize: 18,
                maxLine: 5,
                color: TColor.white,
              ),
            ),
          );

          // Send notifications
          NotificationsRepo().storeBusNotification(
            busId: args.bus_id,
            title: "تم قبول طلب تغيير عنوان",
            body: "الطالب: ${args.studentName}\nالعنوان الجديد: ${args.address}",
            route: "route",
          );

          NotificationsRepo().sendNotification(
            deviceTokens: NotificationsCubit.get(context).supervisorsFcmTokens!.data!,
            title: "تم قبول طلب تغيير عنوان",
            body: "الطالب: ${args.studentName}\nالعنوان الجديد: ${args.address}",
          );

          // Navigate back
          goBack();
          navigateTo(
            RequestsAddressChangeScreen(
              status: 'new',
              appBarTitle: AppStrings.newRequests.tr(),
            ),
            replace: true,
          );
        } else {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              backgroundColor: TColor.redAccent,
              content: CustomText(
                text: "فشل في قبول الطلب",
                fontSize: 18,
                maxLine: 5,
                color: TColor.white,
              ),
            ),
          );
        }
      } catch (e) {
        if (!context.mounted) return;
        Navigator.of(context).pop(); // Close loading
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            backgroundColor: TColor.redAccent,
            content: CustomText(
              text: "حدث خطأ: ${e.toString()}",
              fontSize: 18,
              maxLine: 5,
              color: TColor.white,
            ),
          ),
        );
      }
    }
  }

  // Handle reject request
  void _handleRejectRequest(BuildContext context, CAdreessModels args) async {
    // Show confirmation dialog
    bool? confirmed = await showDialog<bool>(
      context: context,
      barrierDismissible: false,
      builder: (dialogContext) => AlertDialog(
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(16.r),
        ),
        title: Row(
          children: [
            const Icon(
              Icons.cancel,
              color: Colors.red,
              size: 28,
            ),
            SizedBox(width: 8.w),
            Expanded(
              child: CustomText(
                text: "تأكيد الرفض",
                fontSize: 18,
                fontW: FontWeight.w600,
                color: TColor.mainColor,
              ),
            ),
          ],
        ),
        content: const CustomText(
          text: "هل أنت متأكد من رفض طلب تغيير العنوان؟",
          fontSize: 16,
          fontW: FontWeight.w400,
          maxLine: 3,
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(dialogContext, false),
            child: const CustomText(
              text: "إلغاء",
              fontSize: 16,
              fontW: FontWeight.w500,
              color: Colors.grey,
            ),
          ),
          ElevatedButton(
            onPressed: () => Navigator.pop(dialogContext, true),
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.red,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8.r),
              ),
            ),
            child: const CustomText(
              text: "رفض",
              fontSize: 16,
              fontW: FontWeight.w500,
              color: Colors.white,
            ),
          ),
        ],
      ),
    );

    if (confirmed == true) {
      // Show loading
      showDialog(
        context: context,
        barrierDismissible: false,
        builder: (loadingContext) => const Center(
          child: CircularProgressIndicator(
            color: TColor.mainColor,
          ),
        ),
      );

      try {
        bool isValid = await CAdreessRepo()
            .refuseChangeAddressRequest(changeAddressRequestId: args.id, text: '');

        // Check if context is still valid
        if (!context.mounted) return;
        Navigator.of(context).pop(); // Close loading

        if (isValid) {
          // Show success message
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              backgroundColor: TColor.greenSuccess,
              content: CustomText(
                text: AppStrings.successfullyDone.tr(),
                fontSize: 18,
                maxLine: 5,
                color: TColor.white,
              ),
            ),
          );

          // Navigate back
          goBack();
          navigateTo(
            RequestsAddressChangeScreen(
              status: 'new',
              appBarTitle: AppStrings.newRequests.tr(),
            ),
            replace: true,
          );
        } else {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              backgroundColor: TColor.redAccent,
              content: CustomText(
                text: "فشل في رفض الطلب",
                fontSize: 18,
                maxLine: 5,
                color: TColor.white,
              ),
            ),
          );
        }
      } catch (e) {
        if (!context.mounted) return;
        Navigator.of(context).pop(); // Close loading
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            backgroundColor: TColor.redAccent,
            content: CustomText(
              text: "حدث خطأ: ${e.toString()}",
              fontSize: 18,
              maxLine: 5,
              color: TColor.white,
            ),
          ),
        );
      }
    }
  }
}
