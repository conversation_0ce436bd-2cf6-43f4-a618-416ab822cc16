import 'package:bus/config/theme_colors.dart';
import 'package:bus/translations/local_keys.g.dart';
import 'package:bus/utils/assets_utils.dart';
import 'package:bus/views/custom_widgets/custom_text.dart';
import 'package:bus/widgets/custom_appbar.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:url_launcher/url_launcher.dart';

class FullMapScreen extends StatefulWidget {
  final String? latitude;
  final String? longitude;
  final String title;

  const FullMapScreen({
    super.key,
    required this.latitude,
    required this.longitude,
    this.title = '',
  });

  @override
  State<FullMapScreen> createState() => _FullMapScreenState();
}

class _FullMapScreenState extends State<FullMapScreen> {
  GoogleMapController? _mapController;
  final Set<Marker> _markers = {};
  LatLng? _position;

  @override
  void initState() {
    super.initState();
    _initializeMap();
  }

  void _initializeMap() {
    if (widget.latitude != null && widget.longitude != null) {
      try {
        final double lat = double.parse(widget.latitude!);
        final double lng = double.parse(widget.longitude!);
        _position = LatLng(lat, lng);
        
        _markers.add(
          Marker(
            markerId: const MarkerId('location'),
            position: _position!,
          ),
        );
      } catch (e) {
        debugPrint('Error parsing coordinates: $e');
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: CustomAppBar(
        rightWidget: const SizedBox(),
        titleWidget: Row(
          children: [
            CustomText(
              text: widget.title.isNotEmpty 
                  ? widget.title 
                  : AppStrings.studentLocation.tr(),
              fontSize: 18,
              textAlign: TextAlign.center,
              fontW: FontWeight.w600,
              color: TColor.white,
            ),
            const SizedBox(width: 10.0),
          ],
        ),
        leftWidget: context.locale.toString() == "ar"
            ? InkWell(
                onTap: () {
                  Navigator.pop(context);
                },
                child: SvgPicture.asset(AppAssets.arrowBack),
              )
            : InkWell(
                onTap: () {
                  Navigator.pop(context);
                },
                child: SvgPicture.asset(
                  AppAssets.forwardArrow,
                  colorFilter: const ColorFilter.mode(TColor.white, BlendMode.srcIn),
                  width: 25.w,
                  height: 25.w,
                ),
              ),
      ),
      body: _position == null
          ? Center(
              child: CustomText(
                text: AppStrings.locationNotAvailable.tr(),
                fontSize: 16,
                color: TColor.grey5,
              ),
            )
          : Stack(
              children: [
                GoogleMap(
                  initialCameraPosition: CameraPosition(
                    target: _position!,
                    zoom: 15,
                  ),
                  markers: _markers,
                  zoomControlsEnabled: true,
                  myLocationButtonEnabled: false,
                  onMapCreated: (GoogleMapController controller) {
                    _mapController = controller;
                  },
                ),
                Positioned(
                  bottom: 20,
                  left: 20,
                  right: 20,
                  child: Container(
                    padding: EdgeInsets.all(10.r),
                    decoration: BoxDecoration(
                      color: Colors.white,
                      borderRadius: BorderRadius.circular(10.r),
                      boxShadow: [
                        BoxShadow(
                          color: Colors.black.withOpacity(0.1),
                          blurRadius: 10,
                          offset: const Offset(0, 5),
                        ),
                      ],
                    ),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Expanded(
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  CustomText(
                                    text: "${AppStrings.latitude.tr()}: ${widget.latitude}",
                                    fontSize: 14,
                                    color: TColor.grey5,
                                  ),
                                  SizedBox(height: 5.h),
                                  CustomText(
                                    text: "${AppStrings.longitude.tr()}: ${widget.longitude}",
                                    fontSize: 14,
                                    color: TColor.grey5,
                                  ),
                                ],
                              ),
                            ),
                            InkWell(
                              onTap: () async {
                                final url = 'https://www.google.com/maps/search/?api=1&query=${widget.latitude},${widget.longitude}';
                                final Uri uri = Uri.parse(url);
                                if (await canLaunchUrl(uri)) {
                                  await launchUrl(uri, mode: LaunchMode.externalApplication);
                                }
                              },
                              child: Container(
                                padding: EdgeInsets.symmetric(horizontal: 12.w, vertical: 8.h),
                                decoration: BoxDecoration(
                                  color: TColor.mainColor,
                                  borderRadius: BorderRadius.circular(5.r),
                                ),
                                child: CustomText(
                                  text: AppStrings.openInMaps.tr(),
                                  fontSize: 14,
                                  color: TColor.white,
                                ),
                              ),
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),
                ),
              ],
            ),
    );
  }

  @override
  void dispose() {
    _mapController?.dispose();
    super.dispose();
  }
}
