import 'package:bus/config/theme_colors.dart';
import 'package:bus/constant/path_route_name.dart';
import 'package:bus/translations/local_keys.g.dart';
import 'package:bus/utils/assets_utils.dart';
import 'package:bus/utils/sized_box.dart';
import 'package:bus/views/custom_widgets/custom_text.dart';
import 'package:bus/widgets/custom_appbar.dart';
import 'package:bus/widgets/student_data_widget/custom_student_c_w.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';

import '../../../data/models/driver_models/driver_info_models.dart';

class DriverDataScreen extends StatelessWidget {
  static const String routeName = PathRouteName.driverData;

  const DriverDataScreen({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final args = ModalRoute.of(context)!.settings.arguments as DriverInfoModels;
    return Scaffold(
      appBar: CustomAppBar(
        titleWidget: CustomText(
          text: AppStrings.driveData.tr(),
          fontSize: 18,
          textAlign: TextAlign.center,
          fontW: FontWeight.w600,
          color: TColor.white,
        ),
        leftWidget: context.locale.toString() == "ar"
            ? InkWell(
                onTap: () {
                  Navigator.pop(context);
                },
                child: SvgPicture.asset(AppAssets.arrowBack),
              )
            : InkWell(
                onTap: () {
                  Navigator.pop(context);
                },
                child: SvgPicture.asset(
                  AppAssets.forwardArrow,
                  colorFilter: const ColorFilter.mode(TColor.white, BlendMode.srcIn),
                  width: 25.w,
                  height: 25.w,
                ),
              ),
      ),
      body: SingleChildScrollView(
        child: SafeArea(
          child: SizedBox(
            height: 1.sh,
            width: 1.sw,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                Padding(
                  padding: EdgeInsets.symmetric(horizontal: 37.w),
                  child: CircleAvatar(
                    radius: 50.r,
                    backgroundColor: TColor.borderContainer,
                    child: CircleAvatar(
                      radius: 49.r,
                      backgroundColor: TColor.white,
                      backgroundImage: const NetworkImage(
                          "https://www.nj.com/resizer/iqV2J-QFgh0227ybHBor4exTVBk=/800x0/smart/cloudfront-us-east-1.images.arcpublishing.com/advancelocal/SJGKVE5UNVESVCW7BBOHKQCZVE.jpg"),
                    ),
                  ),
                ),
                const SBox(h: 30),
                Padding(
                  padding: EdgeInsets.symmetric(horizontal: 37.w),
                  child: Column(
                    children: [
                      CustomStudentCW(
                        name: args.name,
                        label: AppStrings.name.tr(),
                        isLabel: true,
                      ),
                      const SBox(h: 20),
                      CustomStudentCW(
                        name: args.username,
                        label: AppStrings.username.tr(),
                        isLabel: true,
                      ),
                      const SBox(h: 20),
                      CustomStudentCW(
                        name: args.bus?.name ?? AppStrings.notFound.tr(),
                        label: AppStrings.busName.tr(),
                        isLabel: true,
                      ),
                      const SBox(h: 20),
                      // CustomStudentCW(
                      //   name: "${args.birth_date}",
                      //   label: LocaleKeys.birthDate.tr(),
                      //   isLabel: true,
                      // ),
                      // const SBox(h: 20),
                      // CustomStudentCW(
                      //   name: "${args.city_name}",
                      //   label: LocaleKeys.city.tr(),
                      //   isLabel: true,
                      // ),
                      // const SBox(h: 20),
                      // CustomStudentCW(
                      //   isLabel: true,
                      //   label: LocaleKeys.religion.tr(),
                      //   name: "${args.religion_id}",
                      // ),
                      // const SBox(h: 20),
                      CustomStudentCW(
                        name: args.address,
                        label: AppStrings.address.tr(),
                        isLabel: true,
                      ),
                      const SBox(h: 20),
                      CustomStudentCW(
                        name: args.phone,
                        label: AppStrings.phone.tr(),
                        isLabel: true,
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
