import 'package:bus/bloc/classroom__cubit/class_room_cubit.dart';
import 'package:bus/config/theme_colors.dart';
import 'package:bus/translations/local_keys.g.dart';
import 'package:bus/utils/helpers.dart';
import 'package:bus/views/custom_widgets/build_table_row_widget.dart';
import 'package:bus/views/screens/add_new_classroom_screen/add_new_classroom_screen.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class ClassroomCustomTableW extends StatefulWidget {
  final List<ClassRoomModel?>? studentInfoModels;

  const ClassroomCustomTableW({
    Key? key,
    this.studentInfoModels,
  }) : super(key: key);

  @override
  State<ClassroomCustomTableW> createState() => _ClassroomCustomTableWState();
}

class _ClassroomCustomTableWState extends State<ClassroomCustomTableW> {
  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: 10.w),
      child: ClipRRect(
        borderRadius: BorderRadius.all(Radius.circular(10.r)),
        child: Table(
          columnWidths: const {
            0: FlexColumnWidth(4),
            1: FlexColumnWidth(2),
            2: FlexColumnWidth(2),
            3: FlexColumnWidth(2),
          },
          border: TableBorder.all(
            color: TColor.tabColors,
            borderRadius: BorderRadius.circular(15.r),
          ),
          children:
              List.generate(1 + widget.studentInfoModels!.length, (index) {
            if (index == 0) {
              return BuildTableRowWidget(cell: [
                AppStrings.name.tr(),
                AppStrings.show.tr(),
              ], header: true)
                  .build(context);
            } else {
              final newStudentInfoModels = widget.studentInfoModels![index - 1];
              return BuildTableRowWidget(
                isTabDown: true,
                cell: [
                  newStudentInfoModels?.name,
                  Icons.more_horiz,
                ],
                onTapDown: (TapDownDetails details) {
                  Helpers.customClassroomsShowDialog(context,
                      position: details.globalPosition, onTapEdit: () {
                    debugPrint(newStudentInfoModels.toString());
                    Navigator.of(context)
                      ..pop()
                      ..push(MaterialPageRoute(builder: (ctx) {
                        return AddNewClassroomScreen(
                          isEdit: true,
                          editBus: newStudentInfoModels,
                        );
                      }));
                  }, onTapDelete: () {
                    Navigator.of(context).pop();
                    context
                        .read<ClassRoomCubit>()
                        .deleteClassRoom(
                          newStudentInfoModels?.id,
                        )
                        .whenComplete(() {
                      context
                          .read<ClassRoomCubit>()
                          .getAllClassRooms(pagenumber: 1);
                    });
                  });
                },
              ).build(context);
            }
          }),
        ),
      ),
    );
  }
}
