import 'package:bus/bloc/classroom__cubit/class_room_cubit.dart';
import 'package:bus/config/theme_colors.dart';
import 'package:bus/constant/path_route_name.dart';
import 'package:bus/utils/assets_utils.dart';
import 'package:bus/utils/sized_box.dart';
import 'package:bus/views/custom_widgets/custom_button.dart';
import 'package:bus/views/custom_widgets/custom_text.dart';
import 'package:bus/views/screens/add_new_classroom_screen/add_new_classroom_screen.dart';
import 'package:bus/views/screens/classrooms_screen/classroom_custom_table_w.dart';
import 'package:bus/widgets/custom_appbar.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:bus/translations/local_keys.g.dart';

class ClassroomsScreen extends StatefulWidget {
  static const String routeName = PathRouteName.student;

  const ClassroomsScreen({Key? key}) : super(key: key);

  @override
  State<ClassroomsScreen> createState() => _ClassroomsScreenState();
}

class _ClassroomsScreenState extends State<ClassroomsScreen> {
  @override
  void initState() {
    BlocProvider.of<ClassRoomCubit>(context).getAllClassRooms();

    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: CustomAppBar(
        titleWidget: CustomText(
          text: AppStrings.classrooms.tr(),
          fontSize: 18,
          textAlign: TextAlign.center,
          fontW: FontWeight.w600,
          color: TColor.white,
        ),
        leftWidget: context.locale.toString() == "ar"
            ? InkWell(
                onTap: () {
                  Navigator.pop(context);
                },
                child: SvgPicture.asset(AppAssets.arrowBack),
              )
            : InkWell(
                onTap: () {
                  Navigator.pop(context);
                },
                child: SvgPicture.asset(
                  AppAssets.forwardArrow,
                  colorFilter:
                      const ColorFilter.mode(TColor.white, BlendMode.srcIn),
                  width: 25.w,
                  height: 25.w,
                ),
              ),
      ),
      body: SingleChildScrollView(
        child: SafeArea(
          child: Column(
            children: [
              const SBox(h: 20),
              Padding(
                padding: EdgeInsets.symmetric(horizontal: 37.w),
                child: CustomButton(
                  text: AppStrings.addClassroom.tr(),
                  onTap: () {
                    Navigator.push(context, MaterialPageRoute(builder: (ctx) {
                      return AddNewClassroomScreen(isEdit: false);
                    }));
                  },
                  width: 428,
                  height: 53,
                  radius: 15,
                  borderColor: TColor.mainColor,
                  bgColor: TColor.mainColor,
                ),
              ),
              const SBox(h: 20),
              BlocBuilder<ClassRoomCubit, ClassRoomState>(
                builder: (context, states) {
                  if (states is AllClassRoomLoadingStates) {
                    debugPrint("Students is loading");
                    return const Center(
                      child: CircularProgressIndicator(),
                    );
                  } else if (states is AllClassRoomInitialStates) {
                    return const Center(
                      child: CustomText(text: "No Classrooms found"),
                    );
                  } else if (states is AllClassRoomSuccessStates) {
                    // debugPrint(states.classrooms.toString());
                    // debugPrint(states.classrooms.length);
                    return Column(
                      children: [
                        ClassroomCustomTableW(
                          studentInfoModels: states.classrooms,
                        ),
                        // const SBox(h: 20),
                        // PageNumberWidget(
                        //   lastPage: states.lastPage,
                        //   currentPage: states.currentPage,
                        //   type: "classroom",
                        // ),
                      ],
                    );
                  } else if (states is ClassRoomErrorStates) {
                    return const Center(
                      child: CustomText(
                        color: TColor.mainColor,
                        text: "No Classrooms to show",
                        fontSize: 17,
                        fontW: FontWeight.w600,
                      ),
                    );
                  } else {
                    return const SizedBox();
                  }
                },
              ),
              const SBox(h: 80),
            ],
          ),
        ),
      ),
      // // floatingActionButtonLocation: FloatingActionButtonLocation.centerFloat,
      // // floatingActionButton: Padding(
      // //   padding: const EdgeInsets.symmetric(horizontal: 18.0),
      // //   child: StudentFloatingActionWidget(
      // //     onPressed: () {
      // //       toggleAnimation();
      // //       toggleIcon();
      // //     },
      // //     opacity: opacity!,
      // //     children: [
      // //       GroupFloatingActionButtonWidget(
      // //         btnTitle: 'مراسلة ولي الأمر',
      // //         iconBtn: Icons.send,
      // //         onTap: () {},
      // //         heroTag: '1',
      // //       ),
      // //       const SizedBox(height: 10),
      // //       GroupFloatingActionButtonWidget(
      // //         btnTitle: 'نقل لخط آخر',
      // //         iconBtn: Icons.arrow_drop_down_circle_outlined,
      // //         onTap: () {},
      // //         heroTag: '2',
      // //       ),
      // //       const SizedBox(height: 10),
      // //       GroupFloatingActionButtonWidget(
      // //         btnTitle: 'طباعة الأكواد',
      // //         iconBtn: Icons.debugPrint,
      // //         onTap: () {},
      // //         heroTag: '3',
      // //       ),
      // //       const SizedBox(height: 10),
      // //       GroupFloatingActionButtonWidget(
      // //         btnTitle: 'مسح',
      // //         iconBtn: Icons.delete,
      // //         onTap: () {},
      // //         heroTag: '4',
      // //       ),
      // //       const SizedBox(height: 10),
      // //     ],
      // //   ),
      // ),
    );
  }
}
