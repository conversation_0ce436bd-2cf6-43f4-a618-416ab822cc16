import 'package:bus/bloc/change_password_cubit/change_password_cubit.dart';
import 'package:bus/bloc/change_password_cubit/change_password_states.dart';
import 'package:bus/config/theme_colors.dart';
import 'package:bus/constant/path_route_name.dart';
import 'package:bus/helper/response_state.dart';
import 'package:bus/translations/local_keys.g.dart';
import 'package:bus/utils/assets_utils.dart';
import 'package:bus/utils/sized_box.dart';
import 'package:bus/views/custom_widgets/custom_button.dart';
import 'package:bus/views/custom_widgets/custom_form_field_border.dart';
import 'package:bus/views/custom_widgets/custom_text.dart';
import 'package:bus/widgets/change_password_widgets/custom_change_password_c_w.dart';
import 'package:bus/widgets/custom_appbar.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';

class ChangePasswordScreen extends StatefulWidget {
  static const String routeName = PathRouteName.changePassword;
  const ChangePasswordScreen({Key? key}) : super(key: key);

  @override
  State<ChangePasswordScreen> createState() => _ChangePasswordScreenState();
}

class _ChangePasswordScreenState extends State<ChangePasswordScreen> {
  bool securityCheckNew = true;
  bool securityCheckConfirm = true;
  bool securityCheckOld = true;
  String? currentPassword;
  String? newPassword;
  String? confirmedPassword;
  final _formKey = GlobalKey<FormState>();

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: CustomAppBar(
        rightWidget: const SizedBox(),
        titleWidget: Row(
          children: [
            CustomText(
              text: AppStrings.changePassword.tr(),
              fontSize: 18,
              textAlign: TextAlign.center,
              fontW: FontWeight.w600,
              color: TColor.white,
            ),
            const SizedBox(width: 10.0),
          ],
        ),
        leftWidget: context.locale.toString() == "ar"
            ? InkWell(
                onTap: () {
                  Navigator.pop(context);
                },
                child: SvgPicture.asset(AppAssets.arrowBack),
              )
            : InkWell(
                onTap: () {
                  Navigator.pop(context);
                },
                child: SvgPicture.asset(
                  AppAssets.forwardArrow,
                  colorFilter: const ColorFilter.mode(TColor.white, BlendMode.srcIn),
                  width: 25.w,
                  height: 25.w,
                ),
              ),
      ),
      body: SingleChildScrollView(
        child: SizedBox(
          width: 1.sw,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              const SBox(h: 120),
              CustomText(
                text: AppStrings.newPassword.tr(),
                color: TColor.dialogName,
                fontSize: 20,
                fontW: FontWeight.w600,
              ),
              const SBox(h: 20),
              CustomChangePasswordCW(
                child: Form(
                  key: _formKey,
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      CustomFormFieldWithBorder(
                        security: securityCheckNew,
                        prefix: const Icon(
                          Icons.lock_outline,
                          color: TColor.iconInputColor,
                        ),
                        formFieldWidth: 307,
                        borderColor: TColor.fillFormFieldB,
                        fillColor: TColor.fillFormFieldB,
                        radiusNumber: 15.0,
                        hintText: AppStrings.newPassword.tr(),
                        requierdNumber: 6,
                        validation: AppStrings.validNewPassword.tr(),
                        onChanged: (value) {
                          setState(() {
                            newPassword = value;
                          });
                        },
                        suffix: InkWell(
                          onTap: () {
                            setState(() {
                              securityCheckNew = !securityCheckNew;
                            });
                          },
                          child: securityCheckNew
                              ? const Icon(Icons.visibility_off)
                              : const Icon(Icons.visibility_outlined),
                        ),
                      ),
                      const SBox(h: 20),
                      CustomFormFieldWithBorder(
                        security: securityCheckConfirm,
                        prefix: const Icon(
                          Icons.lock_outline,
                          color: TColor.iconInputColor,
                        ),
                        formFieldWidth: 307,
                        requierdNumber: 6,
                        validation: AppStrings.validConfirmedNewPassword.tr(),
                        borderColor: TColor.fillFormFieldB,
                        fillColor: TColor.fillFormFieldB,
                        radiusNumber: 15.0,
                        hintText: AppStrings.confirmPassword.tr(),
                        onChanged: (value) {
                          setState(() {
                            confirmedPassword = value;
                          });
                        },
                        suffix: InkWell(
                          onTap: () {
                            setState(() {
                              securityCheckConfirm = !securityCheckConfirm;
                            });
                          },
                          child: securityCheckConfirm
                              ? const Icon(Icons.visibility_off)
                              : const Icon(Icons.visibility_outlined),
                        ),
                      ),
                      const SBox(h: 20),
                      CustomFormFieldWithBorder(
                        security: securityCheckOld,
                        prefix: const Icon(
                          Icons.lock_outline,
                          color: TColor.iconInputColor,
                        ),
                        formFieldWidth: 307,
                        borderColor: TColor.fillFormFieldB,
                        fillColor: TColor.fillFormFieldB,
                        radiusNumber: 15.0,
                        requierdNumber: 6,
                        validation: AppStrings.validOldPassword.tr(),
                        hintText: AppStrings.oldPassword.tr(),
                        onChanged: (value) {
                          setState(() {
                            currentPassword = value;
                          });
                        },
                        suffix: InkWell(
                          onTap: () {
                            setState(() {
                              securityCheckOld = !securityCheckOld;
                            });
                          },
                          child: securityCheckOld
                              ? const Icon(Icons.visibility_off)
                              : const Icon(Icons.visibility_outlined),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
              const SBox(h: 180),
              Padding(
                padding: EdgeInsets.symmetric(horizontal: 37.w),
                child: BlocConsumer<ChangePasswordCubit, ChangePasswordStates>(
                  listener: (context, states) {
                    if (states.rStates == ResponseState.success) {
                      ScaffoldMessenger.of(context).showSnackBar(
                        SnackBar(
                          backgroundColor: TColor.greenSuccess,
                          content: CustomText(
                            text: states.changePasswordModels?.status?.messages ??'',
                            color: TColor.white,
                          ),
                        ),
                      );
                      Navigator.pop(context);
                    } else if (states.rStates == ResponseState.failure) {
                      ScaffoldMessenger.of(context).showSnackBar(
                        SnackBar(
                          backgroundColor: TColor.redAccent,
                          content: CustomText(
                            text: states.message?? '',
                            color: TColor.white,
                          ),
                        ),
                      );
                    }
                  },
                  builder: (context, states) {
                    if (states.rStates != ResponseState.loading) {
                      return CustomButton(
                        text: AppStrings.save.tr(),
                        onTap: () {
                          if (_formKey.currentState!.validate()) {
                            _formKey.currentState!.save();
                            if (newPassword != confirmedPassword) {
                              ScaffoldMessenger.of(context).showSnackBar(
                                SnackBar(
                                  backgroundColor: TColor.redAccent,
                                  content: CustomText(
                                    text: '${AppStrings.newPassword.tr()} ${AppStrings.and.tr()} ${AppStrings.confirmPassword.tr()} ${AppStrings.notMatch.tr()}',
                                    color: TColor.white,
                                  ),
                                ),
                              );
                            } else {
                              context.read<ChangePasswordCubit>().changePassword(
                                  newPassword: newPassword,
                                  confirmedPassword: confirmedPassword,
                                  currentPassword: currentPassword);
                            }
                          }
                        },
                        width: 428,
                        height: 53,
                        radius: 15,
                        borderColor: TColor.mainColor,
                        bgColor: TColor.mainColor,
                      );
                    } else {
                      return const CircularProgressIndicator(
                        color: TColor.mainColor,
                      );
                    }
                  },
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
