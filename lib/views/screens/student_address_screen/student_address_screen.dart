import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';

import '../../../config/theme_colors.dart';
import '../../../constant/path_route_name.dart';
import 'package:bus/translations/local_keys.g.dart';import '../../../utils/assets_utils.dart';
import '../../../widgets/custom_appbar.dart';
import '../../custom_widgets/custom_text.dart';

class StudentAddressScreen extends StatefulWidget {
  final double? lat;
  final double? long;
  static const String routeName = PathRouteName.studentAddressScreen;

  const StudentAddressScreen({
    super.key,
    this.long,
    this.lat,
  });

  @override
  State<StudentAddressScreen> createState() => _StudentAddressScreenState();
}

class _StudentAddressScreenState extends State<StudentAddressScreen> {
  GoogleMapController? googleMapController;
  Set<Marker> markerList = {};

  @override
  void initState() {
    markerList.add(
      Marker(
        markerId: const MarkerId("0"),
        position: LatLng(widget.lat!, widget.long!),
      ),
    );

    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: CustomAppBar(
        titleWidget: CustomText(
          text: AppStrings.studentAddress.tr(),
          fontSize: 18,
          textAlign: TextAlign.center,
          fontW: FontWeight.w600,
          color: TColor.white,
        ),
        leftWidget: context.locale.toString() == "ar"
            ? InkWell(
                onTap: () {
                  Navigator.pop(context);
                },
                child: SvgPicture.asset(AppAssets.arrowBack),
              )
            : InkWell(
                onTap: () {
                  Navigator.pop(context);
                },
                child: SvgPicture.asset(
                  AppAssets.forwardArrow,
                  colorFilter:
                      const ColorFilter.mode(TColor.white, BlendMode.srcIn),
                  width: 25.w,
                  height: 25.w,
                ),
              ),
      ),
      body: widget.lat == null || widget.long == null
          ? const Center(
              child: CustomText(
                text: 'قم بتعديل بيانات الطالب وإضافة العنوان أولاً',
                fontSize: 18,
                textAlign: TextAlign.center,
                fontW: FontWeight.w600,
                color: TColor.black,
              ),
            )
          : GoogleMap(
              initialCameraPosition: CameraPosition(
                target: LatLng(widget.lat!, widget.long!),
                zoom: 14,
              ),
              markers: markerList,
              zoomControlsEnabled: true,
              onMapCreated: (GoogleMapController controller) {
                googleMapController = controller;
              },
            ),
    );
  }
}
