import 'package:bus/constant/path_route_name.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';

import '../../../bloc/parent_cubit/parent_cubit.dart';
import '../../../bloc/parent_cubit/parent_states.dart';
import '../../../config/theme_colors.dart';
import 'package:bus/translations/local_keys.g.dart';import '../../../utils/assets_utils.dart';
import '../../../widgets/custom_appbar.dart';
import '../../../widgets/student_data_widget/custom_student_c_w.dart';
import '../../custom_widgets/custom_text.dart';

class ParentDataScreen extends StatefulWidget {
  static const String routeName = PathRouteName.parentData;

  const ParentDataScreen({Key? key}) : super(key: key);

  @override
  State<ParentDataScreen> createState() => _ParentDataScreenState();
}

class _ParentDataScreenState extends State<ParentDataScreen> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: CustomAppBar(
        titleWidget: CustomText(
          text: AppStrings.parentData.tr(),
          fontSize: 18,
          textAlign: TextAlign.center,
          fontW: FontWeight.w600,
          color: TColor.white,
        ),
        leftWidget: context.locale.toString() == "ar"
            ? InkWell(
          onTap: () {
            Navigator.pop(context);
          },
          child: SvgPicture.asset(AppAssets.arrowBack),
        )
            : InkWell(
          onTap: () {
            Navigator.pop(context);
          },
          child: SvgPicture.asset(
            AppAssets.forwardArrow,
            colorFilter: const ColorFilter.mode(TColor.white, BlendMode.srcIn),
            width: 25.w,
            height: 25.w,
          ),
        ),
      ),
      body: SingleChildScrollView(
        padding: EdgeInsets.symmetric(horizontal: 37.w),
        child: BlocBuilder<ParentCubit, ParentState>(
          builder: (context, state) {
            if (state is ParentDataLoadingStates) {
              return const Center(
                child: CircularProgressIndicator(
                  color: TColor.mainColor,
                ),
              );
            } else {
              return Column(
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  CircleAvatar(
                    radius: 50.r,
                    backgroundColor: TColor.borderContainer,
                    child: CircleAvatar(
                      radius: 49.r,
                      backgroundColor: TColor.white,
                      backgroundImage: NetworkImage(
                        ParentCubit.get(context).parentDataModel?.data?.parent?.logoPath ?? ''
                      ),
                    ),
                  ),
                  30.verticalSpace,
                  CustomStudentCW(
                    isLabel: true,
                    label: AppStrings.name.tr(),
                    name: ParentCubit.get(context).parentDataModel?.data?.parent?.name ?? '',
                  ),
                  20.verticalSpace,
                  CustomStudentCW(
                    isLabel: true,
                    label: AppStrings.address.tr(),
                    name: ParentCubit.get(context).parentDataModel?.data?.parent?.address ?? '',
                  ),
                  20.verticalSpace,
                  CustomStudentCW(
                    isLabel: true,
                    label: AppStrings.sonsNumber.tr(),
                    name: ParentCubit.get(context).parentDataModel?.data?.children?.length.toString() ?? '',
                  ),
                  20.verticalSpace,
                  CustomStudentCW(
                    isLabel: true,
                    label: AppStrings.phone.tr(),
                    name: ParentCubit.get(context).parentDataModel?.data?.parent?.phone ?? '',
                  ),
                  20.verticalSpace,
                  CustomStudentCW(
                    isLabel: true,
                    label: AppStrings.email.tr(),
                    name: ParentCubit.get(context).parentDataModel?.data?.parent?.email ?? '',
                  ),
                  20.verticalSpace,
                ],
              );
            }
          },
        ),
      ),
    );
  }
}
