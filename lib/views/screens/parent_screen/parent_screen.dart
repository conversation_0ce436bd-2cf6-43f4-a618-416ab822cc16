import 'package:bus/bloc/parent_cubit/parent_cubit.dart';
import 'package:bus/bloc/parent_cubit/parent_filter_cubit.dart';
import 'package:bus/bloc/parent_cubit/parent_filter_states.dart';
import 'package:bus/data/models/parent_models/parent_model.dart';
import 'package:bus/bloc/parent_cubit/parent_states.dart';
import 'package:bus/views/custom_widgets/custom_form_field_border.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:url_launcher/url_launcher.dart';

import '../../../bloc/student_cubit/student_cubit.dart';
import '../../../config/theme_colors.dart';
import '../../../constant/path_route_name.dart';
import '../../../translations/local_keys.g.dart';
import '../../../utils/assets_utils.dart';
import '../../../utils/helpers.dart';
import '../../../utils/sized_box.dart';
import '../../../widgets/custom_appbar.dart';
import '../../../widgets/student_widgets/custom_container_dialog_w.dart';
import '../../custom_widgets/build_table_row_widget.dart';
import '../../custom_widgets/custom_text.dart';
import '../bus_students_screen/bus_students_screen.dart';
import '../parent_data_screen/parent_data_screen.dart';

class ParentScreen extends StatefulWidget {
  final bool isFromStudents;
  final List<DataInfo>? dataInfo;
  static const String routeName = PathRouteName.parent;

  const ParentScreen({
    Key? key,
    this.isFromStudents = false,
    this.dataInfo,
  }) : super(key: key);

  @override
  State<ParentScreen> createState() => _ParentScreenState();
}

class _ParentScreenState extends State<ParentScreen> {
  final ScrollController _scrollController = ScrollController();
  final TextEditingController _searchController = TextEditingController();
  bool _isSearching = false;

  @override
  void initState() {
    super.initState();
    _initializeScreen();
    // Call the API once when the screen is initialized.
    ParentCubit.get(context).getParent(pageNumber: 1);
  }

  void _initializeScreen() {
    try {
      // Initialize the scroll controller with a listener to check for pagination.
      _scrollController.addListener(() {
        // Check if the user scrolled to the bottom.
        if (_scrollController.position.pixels ==
            _scrollController.position.maxScrollExtent) {
          final parentCubit = ParentCubit.get(context);
          // Only request the next page if:
          // - There is more data (currentPage < last_pages)
          // - A request is not already in progress (assuming isLoading is tracked in the cubit)
          if (!parentCubit.isLoading &&
              parentCubit.currentPage != null &&
              parentCubit.last_pages != null &&
              parentCubit.currentPage! < parentCubit.last_pages!) {
            parentCubit.getParent(pageNumber: parentCubit.currentPage! + 1);
          }
        }
      });
      _searchController.addListener(_handleSearchEmpty);
    } catch (e, stackTrace) {
      debugPrint('Error in initState: $e');
      debugPrint('Stack trace: $stackTrace');
    }
  }

  void _handleSearchEmpty() {
    if (_searchController.text.isEmpty) {
      setState(() => _isSearching = false);
    }
  }

  @override
  void dispose() {
    _scrollController.dispose();
    _searchController.dispose();
    super.dispose();
  }

  void _handleSearch() {
    try {
      setState(() => _isSearching = true);
      context.read<ParentFilterCubit>().getParentWithFilters(
        parentName: _searchController.text,
      );
    } catch (e, stackTrace) {
      debugPrint('Error in search: $e');
      debugPrint('Stack trace: $stackTrace');
    }
  }

  Future<void> _handleCall(String phone) async {
    try {
      if (!await launchUrl(Uri.parse("tel:$phone"))) {
        throw Exception('Could not launch url');
      }
    } catch (e, stackTrace) {
      debugPrint('Error in calling: $e');
      debugPrint('Stack trace: $stackTrace');
    }
  }

  void _showParentData(int parentId) {
    try {
      ParentCubit.get(context).getParentData(parentId: parentId);
      Navigator.of(context)
        ..pop()
        ..push(MaterialPageRoute(
          builder: (ctx) => const ParentDataScreen(),
        ));
    } catch (e, stackTrace) {
      debugPrint('Error in showing parent data: $e');
      debugPrint('Stack trace: $stackTrace');
    }
  }

  void _showStudents(int parentId, String parentName) {
    try {
      StudentCubit.get(context).getStudentWithParentId(parentId: parentId);
      Navigator.of(context)
        ..pop()
        ..push(MaterialPageRoute(
          builder: (ctx) => BusStudentsScreen(
            isFromParents: true,
            busName: parentName,
            id: parentId,
          ),
        ));
    } catch (e, stackTrace) {
      debugPrint('Error in showing students: $e');
      debugPrint('Stack trace: $stackTrace');
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: _buildAppBar(),
      body: SingleChildScrollView(
        controller: _scrollController,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            if (!widget.isFromStudents) _buildSearchField(),
            Padding(
              padding: const EdgeInsets.all(16.0),
              child: _buildTableContent(),
            ),
          ],
        ),
      ),
    );
  }

  CustomAppBar _buildAppBar() {
    return CustomAppBar(
      titleWidget: Center(
        child: CustomText(
          text: AppStrings.parents.tr(),
          fontSize: 18,
          textAlign: TextAlign.center,
          fontW: FontWeight.w600,
          color: TColor.white,
        ),
      ),
      leftWidget: _buildBackButton(),
    );
  }

  Widget _buildBackButton() {
    final isArabic = context.locale.toString() == "ar";
    return InkWell(
      onTap: () => Navigator.pop(context),
      child: SvgPicture.asset(
        isArabic ? AppAssets.arrowBack : AppAssets.forwardArrow,
        colorFilter:
        isArabic ? null : const ColorFilter.mode(TColor.white, BlendMode.srcIn),
        width: isArabic ? null : 25.w,
        height: isArabic ? null : 25.w,
      ),
    );
  }

  Widget _buildSearchField() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        CustomFormFieldWithBorder(
          controller: _searchController,
          paddingLeft: 0,
          paddingRight: 0,
          formFieldWidth: 428.w,
          height: true,
          fillColor: TColor.fillFormFieldB,
          borderColor: TColor.fillFormFieldB,
          suffix: InkWell(
            onTap: _handleSearch,
            child: const Icon(Icons.search),
          ),
        ),
      ],
    );
  }

  Widget _buildTableContent() {
    if (_isSearching) {
      return _buildFilteredContent();
    }

    return BlocBuilder<ParentCubit, ParentState>(
      builder: (context, state) {
        if (state is ParentLoadingStates) {
          return const Center(
            child: CircularProgressIndicator(color: TColor.mainColor),
          );
        }

        if (state is ParentSuccessStates) {
          return widget.isFromStudents
              ? _buildParentTable(widget.dataInfo ?? [], showPagination: false)
              : _buildParentTable(
            context.watch<ParentCubit>().data,
            showPagination: true,
          );
        }

        return _buildEmptyState();
      },
    );
  }

  Widget _buildFilteredContent() {
    return BlocBuilder<ParentFilterCubit, ParentFilterState>(
      builder: (context, state) {
        if (state is ParentFilterLoadingStates) {
          return const Center(
            child: CircularProgressIndicator(color: TColor.mainColor),
          );
        }

        if (state is ParentFilterSuccessStates) {
          final searchData = context.watch<ParentFilterCubit>().searchData;
          return searchData.isEmpty
              ? _buildEmptyState()
              : _buildParentTable(searchData, showPagination: false);
        }

        return _buildEmptyState();
      },
    );
  }

  Widget _buildParentTable(List<DataInfo> parents, {required bool showPagination}) {
    if (parents.isEmpty) {
      return _buildEmptyState();
    }

    return Column(
      children: [
        Table(
          columnWidths: const {
            0: FlexColumnWidth(2.5),
            1: FlexColumnWidth(1),
            2: FlexColumnWidth(1),
            3: FlexColumnWidth(1),
          },
          border: TableBorder.all(
            color: TColor.tabColors,
            borderRadius: BorderRadius.circular(15.0),
          ),
          children: [
            _buildTableHeader(),
            ...parents.map((parent) => _buildTableRow(parent)).toList(),
          ],
        ),
        const SBox(h: 10.0),
        if (showPagination) _buildPaginationIndicator(),
      ],
    );
  }

  TableRow _buildTableHeader() {
    return BuildTableRowWidget(
      cell: [
        AppStrings.name.tr(),
        AppStrings.call.tr(),
        AppStrings.notification.tr(),
        AppStrings.options.tr()
      ],
      header: true,
    ).build(context);
  }

  TableRow _buildTableRow(DataInfo parent) {
    return BuildTableRowWidget(
      is2Icon: true,
      isTabDown: true,
      isFirstIcon: true,
      cell: [
        parent.name ?? "",
        Icons.call,
        Icons.notifications_active,
        Icons.more_horiz,
      ],
      onTapFirstCell: () => _handleCall(parent.phone ?? ""),
      onTapBeforeLastCell: () {}, // Notify functionality
      onTapDown: (position) => _showOptionsDialog(position, parent),
    ).build(context);
  }

  void _showOptionsDialog(TapDownDetails position, DataInfo parent) {
    Helpers.customAbsenceShowDialog(
      context,
      position: position.globalPosition,
      onTapShow: () => _showParentData(parent.id!),
      items: [
        PopupMenuItem(
          child: Center(
            child: CustomContainerDialogW(
              icons: Icons.people,
              name: AppStrings.showSons.tr(),
              onTap: () => _showStudents(parent.id!, parent.name ?? ""),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildPaginationIndicator() {
    final parentCubit = context.watch<ParentCubit>();
    if (parentCubit.currentPage != null &&
        parentCubit.last_pages != null &&
        parentCubit.currentPage! < parentCubit.last_pages!) {
      return Icon(Icons.arrow_downward_rounded, size: 40.sp);
    }
    return const SizedBox.shrink();
  }

  Widget _buildEmptyState() {
    return Center(
      child: CustomText(text: AppStrings.parentsNotFound.tr()),
    );
  }
}
