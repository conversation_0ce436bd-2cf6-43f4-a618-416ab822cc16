import 'package:bus/config/theme_colors.dart';
import 'package:bus/constant/path_route_name.dart';
import 'package:bus/translations/local_keys.g.dart';
import 'package:bus/utils/assets_utils.dart';
import 'package:bus/utils/sized_box.dart';
import 'package:bus/views/custom_widgets/custom_button.dart';
import 'package:bus/views/custom_widgets/custom_form_field_border.dart';
import 'package:bus/views/custom_widgets/custom_text.dart';
import 'package:bus/views/screens/done_screen/done_screen.dart';
import 'package:bus/widgets/custom_background_image.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class NewPasswordScreen extends StatefulWidget {
  static const String routeName = PathRouteName.newPassword;
  const NewPasswordScreen({Key? key}) : super(key: key);

  @override
  State<NewPasswordScreen> createState() => _NewPasswordScreenState();
}

class _NewPasswordScreenState extends State<NewPasswordScreen> {
  bool securityCheck = false;
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: SingleChildScrollView(
        child: CustomBackgroundImage(
          child: Column(
            children: [
              const SBox(h: 60),
              Image.asset(
                assetsImages("logo.png"),
                height: 121.w,
                width: 124.w,
              ),
              const SBox(h: 60),
              CustomText(
                text: AppStrings.newPassword.tr(),
                color: TColor.white,
                fontW: FontWeight.w700,
                fontSize: 20,
              ),
              const SBox(h: 20),
              Container(
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(30.r),
                  color: TColor.white,
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withOpacity(0.2),
                      blurRadius: 3,
                      offset: const Offset(0, 4), // changes position of shadow
                    ),
                  ],
                ),
                width: 354.w,
                height: 310.w,
                child: Column(
                  children: [
                    const SBox(h: 60),
                    CustomFormFieldWithBorder(
                      prefix: const Icon(
                        Icons.lock_outline,
                        color: TColor.iconInputColor,
                      ),
                      formFieldWidth: 307,
                      borderColor: TColor.fillFormFieldB,
                      fillColor: TColor.fillFormFieldB,
                      radiusNumber: 15.0,
                      hintText: AppStrings.newPassword.tr(),
                      suffix: InkWell(
                        onTap: () {
                          setState(() {
                            securityCheck = !securityCheck;
                          });
                        },
                        child: securityCheck
                            ? const Icon(Icons.visibility_off)
                            : const Icon(Icons.visibility_outlined),
                      ),
                    ),
                    const SBox(h: 15),
                    CustomFormFieldWithBorder(
                      prefix: const Icon(
                        Icons.lock_outline,
                        color: TColor.iconInputColor,
                      ),
                      formFieldWidth: 307,
                      borderColor: TColor.fillFormFieldB,
                      fillColor: TColor.fillFormFieldB,
                      radiusNumber: 15.0,
                      hintText: AppStrings.againPassword.tr(),
                      suffix: InkWell(
                        onTap: () {
                          setState(() {
                            securityCheck = !securityCheck;
                          });
                        },
                        child: securityCheck
                            ? const Icon(Icons.visibility_off)
                            : const Icon(Icons.visibility_outlined),
                      ),
                    ),
                    const SBox(h: 50),
                    CustomButton(
                      text: AppStrings.changePassword.tr(),
                      onTap: () {
                        print("login");
                        Navigator.pushNamed(context, DoneScreen.routeName);
                      },
                      width: 307,
                      height: 48,
                      radius: 15,
                      borderColor: TColor.mainColor,
                      bgColor: TColor.mainColor,
                    ),
                    const SBox(h: 20),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
