import 'package:bus/config/theme_colors.dart';
import 'package:bus/constant/path_route_name.dart';
import 'package:bus/translations/local_keys.g.dart';
import 'package:bus/utils/assets_utils.dart';
import 'package:bus/utils/sized_box.dart';
import 'package:bus/views/custom_widgets/custom_button.dart';
import 'package:bus/views/custom_widgets/custom_form_field_border.dart';
import 'package:bus/views/custom_widgets/custom_text.dart';
import 'package:bus/widgets/custom_appbar.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:file_picker/file_picker.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';

class AddStudentBusFileScreen extends StatefulWidget {
  static const String routeName = PathRouteName.addStudentBusFile;

  const AddStudentBusFileScreen({Key? key}) : super(key: key);

  @override
  State<AddStudentBusFileScreen> createState() =>
      _AddStudentBusFileScreenState();
}

class _AddStudentBusFileScreenState extends State<AddStudentBusFileScreen> {
  FilePickerResult? result;

  String? file;

  Future getFile() async {
    final result1 = await FilePicker.platform.pickFiles();
    if (result1 != null) {
      setState(() {
        result = result1;
        file = result1.files.single.path!.split("/").last;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: CustomAppBar(
        rightWidget: const SizedBox(),
        titleWidget: Row(
          children: [
            CustomText(
              text: AppStrings.addStudentBusFile.tr(),
              fontSize: 18,
              textAlign: TextAlign.center,
              fontW: FontWeight.w600,
              color: TColor.white,
            ),
            const SizedBox(width: 10.0),
          ],
        ),
        leftWidget: context.locale.toString() == "ar"
            ? InkWell(
                onTap: () {
                  Navigator.pop(context);
                },
                child: SvgPicture.asset(AppAssets.arrowBack),
              )
            : InkWell(
                onTap: () {
                  Navigator.pop(context);
                },
                child: SvgPicture.asset(
                  AppAssets.forwardArrow,
                  colorFilter: const ColorFilter.mode(TColor.white, BlendMode.srcIn),
                  width: 25.w,
                  height: 25.w,
                ),
              ),
      ),
      body: SingleChildScrollView(
        child: SizedBox(
          height: 720.w,
          width: 1.sw,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              CustomFormFieldWithBorder(
                formFieldWidth: 428,
                heightA: 53,
                hintText: AppStrings.educationalLevel.tr(),
                borderColor: TColor.fillFormFieldB,
                fillColor: TColor.fillFormFieldB,
                radiusNumber: 15.0,
                paddingRight: 37.w,
                paddingLeft: 37.w,
                contentPaddingVertical: 15,
                contentPaddingHorizontal: 15,
              ),
              const SBox(h: 15),
              Padding(
                padding: EdgeInsets.symmetric(horizontal: 37.w),
                child: Container(
                  width: 428.w,
                  height: 45.w,
                  decoration: BoxDecoration(
                    borderRadius: context.locale.toString() == "ar"
                        ? BorderRadius.only(
                            topLeft: Radius.circular(10.r),
                            topRight: Radius.circular(10.r),
                            bottomLeft: Radius.circular(10.r))
                        : BorderRadius.only(
                            topLeft: Radius.circular(10.r),
                            topRight: Radius.circular(10.r),
                            bottomRight: Radius.circular(10.r)),
                    border:
                        Border.all(width: 1.w, color: TColor.fillFormFieldB),
                  ),
                  child: Row(
                    children: [
                      Container(
                        width: 110.w,
                        height: 45.w,
                        decoration: BoxDecoration(
                          color: TColor.borderContainer,
                          borderRadius: context.locale.toString() == "ar"
                              ? BorderRadius.only(
                                  topRight: Radius.circular(10.r),
                                  bottomLeft: Radius.circular(10.r))
                              : BorderRadius.only(
                                  topLeft: Radius.circular(10.r),
                                  bottomRight: Radius.circular(10.r)),
                          border: Border.all(
                              width: 1.w, color: TColor.borderContainer),
                        ),
                        child: InkWell(
                          onTap: () {
                            getFile();
                          },
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              Icon(
                                Icons.drive_folder_upload,
                                color: TColor.white,
                                size: 20.sp,
                              ),
                              const SBox(w: 10),
                              CustomText(
                                text: AppStrings.addFile.tr(),
                                color: TColor.white,
                                fontSize: 13,
                                fontW: FontWeight.w400,
                              ),
                            ],
                          ),
                        ),
                      ),
                      const SBox(w: 20),
                      CustomText(
                        text: file ?? "",
                        color: TColor.dialogName,
                        fontW: FontWeight.w400,
                        fontSize: 13,
                      ),
                    ],
                  ),
                ),
              ),
              const SBox(h: 15),
              Expanded(
                child: Align(
                  alignment: Alignment.bottomCenter,
                  child: Padding(
                    padding: EdgeInsets.symmetric(horizontal: 37.w),
                    child: CustomButton(
                      text: AppStrings.add.tr(),
                      onTap: () {},
                      width: 428,
                      height: 53,
                      radius: 15,
                      borderColor: TColor.mainColor,
                      bgColor: TColor.mainColor,
                    ),
                  ),
                ),
              ),
              const SBox(h: 40),
            ],
          ),
        ),
      ),
    );
  }
}
