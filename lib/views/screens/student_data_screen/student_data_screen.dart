import 'dart:developer';
import 'package:flutter/foundation.dart';

import 'package:bus/config/theme_colors.dart';
import 'package:bus/constant/path_route_name.dart';
import 'package:bus/data/models/student_models/student_model.dart';
import 'package:bus/translations/local_keys.g.dart';
import 'package:bus/utils/assets_utils.dart';
import 'package:bus/utils/sized_box.dart';
import 'package:bus/views/custom_widgets/custom_text.dart';
import 'package:bus/views/screens/add_student_screen/add_student_screen.dart';
import 'package:bus/widgets/custom_appbar.dart';
import 'package:bus/widgets/location_map_widget.dart';
import 'package:bus/widgets/student_data_widget/custom_student_c_w.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';

class StudentDataScreen extends StatelessWidget {
  static const String routeName = PathRouteName.studentData;

  const StudentDataScreen({super.key});

  @override
  Widget build(BuildContext context) {
    final args = ModalRoute.of(context)!.settings.arguments as StudentModel;
    log("args: $args", name: "StudentDataScreen");
    return Scaffold(
      appBar: CustomAppBar(
        rightWidget: IconButton(
          icon: const Icon(Icons.edit, color: TColor.white),
          onPressed: () {
            if (kDebugMode) {
              print(args.toString());
            }
            Navigator.push(
              context,
              MaterialPageRoute(builder: (ctx) {
                return AddStudentScreen(
                  isEdit: true,
                  studentModel: args,
                  isFromBus: false,
                );
              }),
            );
          },
        ),
        titleWidget: Row(
          children: [
            CustomText(
              text: AppStrings.showStudentData.tr(),
              fontSize: 18,
              textAlign: TextAlign.center,
              fontW: FontWeight.w600,
              color: TColor.white,
            ),
            const SizedBox(width: 10.0),
          ],
        ),
        leftWidget: context.locale.toString() == "ar"
            ? InkWell(
                onTap: () {
                  Navigator.pop(context);
                },
                child: SvgPicture.asset(AppAssets.arrowBack),
              )
            : InkWell(
                onTap: () {
                  Navigator.pop(context);
                },
                child: SvgPicture.asset(
                  AppAssets.forwardArrow,
                  colorFilter:
                      const ColorFilter.mode(TColor.white, BlendMode.srcIn),
                  width: 25.w,
                  height: 25.w,
                ),
              ),
      ),
      body: SingleChildScrollView(
        child: SizedBox(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              const SBox(h: 20),
              Padding(
                padding: EdgeInsets.symmetric(horizontal: 37.w),
                child: CircleAvatar(
                  radius: 50.r,
                  backgroundColor: TColor.borderContainer,
                  child: CircleAvatar(
                    radius: 49.r,
                    backgroundColor: TColor.white,
                    backgroundImage: NetworkImage(args.logo_path ??
                        "https://www.nj.com/resizer/iqV2J-QFgh0227ybHBor4exTVBk=/800x0/smart/cloudfront-us-east-1.images.arcpublishing.com/advancelocal/SJGKVE5UNVESVCW7BBOHKQCZVE.jpg"),
                  ),
                ),
              ),
              const SBox(h: 30),
              Padding(
                padding: EdgeInsets.symmetric(horizontal: 37.w),
                child: Column(
                  children: [
                    CustomStudentCW(
                      name: "${args.name}",
                      label: AppStrings.name.tr(),
                      isLabel: true,
                    ),
                    const SBox(h: 20),
                    // CustomStudentCW(
                    //   name: "${args.city_name}",
                    //   label: LocaleKeys.city.tr(),
                    //   isLabel: true,
                    // ),
                    // const SBox(h: 20),
                    CustomStudentCW(
                      name: args.address ?? 'none',
                      label: AppStrings.address.tr(),
                      isLabel: true,
                    ),
                    const SBox(h: 20),
                    LocationMapWidget(
                      latitude: args.latitude,
                      longitude: args.longitude,
                      height: 150,
                      width: 428,
                    ),
                    const SBox(h: 20),
                    CustomStudentCW(
                      name: "${args.grade?.name}",
                      label: AppStrings.grade.tr(),
                      isLabel: true,
                    ),
                    const SBox(h: 20),
                    CustomStudentCW(
                      name: args.trip_type == 'start_day'
                          ? AppStrings.morningTrip.tr()
                          : args.trip_type == 'end_day'
                              ? AppStrings.eveningTrip.tr()
                              : AppStrings.fullDay.tr(),
                      label: AppStrings.busSubscription.tr(),
                      isLabel: true,
                    ),
                    const SBox(h: 20),
                    // CustomStudentCW(
                    //   name: "${args.type_blood?.name}",
                    //   label: LocaleKeys.bloodType.tr(),
                    //   isLabel: true,
                    // ),
                    // const SBox(h: 20),
                    // CustomStudentCW(
                    //   name: "${args.Date_Birth}",
                    //   label: LocaleKeys.birthDate.tr(),
                    //   isLabel: true,
                    // ),
                    // const SBox(h: 20),
                    CustomStudentCW(
                      name: args.phone ?? AppStrings.notFound.tr(),
                      label: AppStrings.phone.tr(),
                      isLabel: true,
                    ),
                    const SBox(h: 20),
                    CustomStudentCW(
                      name: "${args.parent_key}",
                      label: AppStrings.code.tr(),
                      isLabel: true,
                    ),
                    const SBox(h: 20),
                    CustomStudentCW(
                      name: "${args.parent_secret}",
                      label: AppStrings.password.tr(),
                      isLabel: true,
                    ),
                    const SBox(h: 40),
                  ],
                ),
              )
            ],
          ),
        ),
      ),
    );
  }
}
