import 'dart:async';

import 'package:bus/translations/local_keys.g.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:geolocator/geolocator.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';

import '../../../config/theme_colors.dart';
import '../../../utils/assets_utils.dart';
import '../../../widgets/custom_appbar.dart';
import '../../custom_widgets/custom_text.dart';

class SetLocationOnMapScreen extends StatefulWidget {
  SetLocationOnMapScreen({
    super.key,
  });

  @override
  State<SetLocationOnMapScreen> createState() => SetLocationOnMapScreenState();
}

class SetLocationOnMapScreenState extends State<SetLocationOnMapScreen> {
  final Completer<GoogleMapController> _controller =
      Completer<GoogleMapController>();
  CameraPosition? selectedLocation;

  @override
  void initState() {
    super.initState();
    _determinePosition();
  }

  Position? currentPosition;

  Future<Future<Position>?> _determinePosition() async {
    bool serviceEnabled;
    LocationPermission permission;

    // Test if location services are enabled.
    serviceEnabled = await Geolocator.isLocationServiceEnabled();
    if (!serviceEnabled) {
      // Location services are not enabled don't continue
      // accessing the position and request users of the
      // App to enable the location services.
      return Future.error('Location services are disabled.');
    }

    permission = await Geolocator.checkPermission();
    if (permission == LocationPermission.denied) {
      permission = await Geolocator.requestPermission();
      if (permission == LocationPermission.denied) {
        // Permissions are denied, next time you could try
        // requesting permissions again (this is also where
        // Android's shouldShowRequestPermissionRationale
        // returned true. According to Android guidelines
        // your App should show an explanatory UI now.
        return Future.error('Location permissions are denied');
      }
    }

    if (permission == LocationPermission.deniedForever) {
      // Permissions are denied forever, handle appropriately.
      return Future.error(
          'Location permissions are permanently denied, we cannot request permissions.');
    }

    // When we reach here, permissions are granted and we can
    // continue accessing the position of the device.
    return await Geolocator.getCurrentPosition().then((Position position) {
      setState(() {
        currentPosition = position;
      });
    }).catchError((e) {
      print(e);
    });
  }

  LatLng location = const LatLng(0, 0);

  @override
  Widget build(BuildContext context) {
    var marker =
        Marker(markerId: const MarkerId("location-marker"), position: location);

    return Scaffold(
        appBar: CustomAppBar(
          titleWidget: CustomText(
            text: AppStrings.setLocation.tr(),
            fontSize: 18,
            textAlign: TextAlign.center,
            fontW: FontWeight.w600,
            color: TColor.white,
          ),
          leftWidget: context.locale.toString() == "ar"
              ? InkWell(
                  onTap: () {
                    Navigator.pop(context);
                  },
                  child: SvgPicture.asset(AppAssets.arrowBack),
                )
              : InkWell(
                  onTap: () {
                    Navigator.pop(context);
                  },
                  child: SvgPicture.asset(
                    AppAssets.forwardArrow,
                    colorFilter:
                        const ColorFilter.mode(TColor.white, BlendMode.srcIn),
                    width: 25.w,
                    height: 25.w,
                  ),
                ),
        ),
        floatingActionButtonLocation: FloatingActionButtonLocation.centerDocked,
        floatingActionButton: Padding(
          padding: const EdgeInsets.all(8.0),
          child: FloatingActionButton.extended(
            onPressed: () {
              Navigator.of(context).pop(location);
            },
            label: Text(AppStrings.save.tr()),
            icon: const Icon(Icons.location_on_outlined),
            backgroundColor: TColor.mainColor,
          ),
        ),
        body: currentPosition == null
            ? const Center(
                child: CircularProgressIndicator(
                  color: TColor.mainColor,
                ),
              )
            : GoogleMap(
                myLocationButtonEnabled: true,
                myLocationEnabled: true,
                markers: {marker},
                mapType: MapType.normal,
                onTap: (LatLng position) {
                  debugPrint(position.toString());
                  location = position;
                  setState(() {});
                },
                initialCameraPosition: CameraPosition(
                  target: LatLng(
                      currentPosition!.latitude, currentPosition!.longitude),
                  zoom: 15,
                ),
                onMapCreated: (GoogleMapController controller) {
                  _controller.complete(controller);
                },
              ));
  }
}

// {
// import 'dart:async';
//
// import 'package:bus/views/custom_widgets/custom_text.dart';
// import 'package:flutter/material.dart';
// import 'package:flutter_bloc/flutter_bloc.dart';
// import 'package:geolocator/geolocator.dart';
// import 'package:google_maps_flutter/google_maps_flutter.dart';
//
// class ShowChildOnMapScreen extends StatefulWidget {
//   ShowChildOnMapScreen({
//     super.key,
//   });
//
//   @override
//   State<ShowChildOnMapScreen> createState() => ShowChildOnMapScreenState();
// }
//
// class ShowChildOnMapScreenState extends State<ShowChildOnMapScreen> {
//   final Completer<GoogleMapController> _controller =
//   Completer<GoogleMapController>();
//   CameraPosition? selectedLocation;
//
//   Position? currentPosition;
//
//   @override
//   void initState() {
//     super.initState();
//     _determinePosition();
//   }
//
//
//   /// Determine the current position of the device.
//   ///
//   /// When the location services are not enabled or permissions
//   /// are denied the `Future` will return an error.
//   Future<Future<Position>?> _determinePosition() async {
//     bool serviceEnabled;
//     LocationPermission permission;
//
//     // Test if location services are enabled.
//     serviceEnabled = await Geolocator.isLocationServiceEnabled();
//     if (!serviceEnabled) {
//       // Location services are not enabled don't continue
//       // accessing the position and request users of the
//       // App to enable the location services.
//       return Future.error('Location services are disabled.');
//     }
//
//     permission = await Geolocator.checkPermission();
//     if (permission == LocationPermission.denied) {
//       permission = await Geolocator.requestPermission();
//       if (permission == LocationPermission.denied) {
//         // Permissions are denied, next time you could try
//         // requesting permissions again (this is also where
//         // Android's shouldShowRequestPermissionRationale
//         // returned true. According to Android guidelines
//         // your App should show an explanatory UI now.
//         return Future.error('Location permissions are denied');
//       }
//     }
//
//     if (permission == LocationPermission.deniedForever) {
//       // Permissions are denied forever, handle appropriately.
//       return Future.error(
//           'Location permissions are permanently denied, we cannot request permissions.');
//     }
//
//     // When we reach here, permissions are granted and we can
//     // continue accessing the position of the device.
//     return await Geolocator.getCurrentPosition().then((Position position) {
//       setState(() {
//         currentPosition = position;
//       });
//     }).catchError((e) {
//       print(e);
//     });
//   }
//
//   final _kGooglePlex = const CameraPosition(
//     target: LatLng(30, 30),
//     zoom: 6,
//   );
//   LatLng location = LatLng(32, 32);
//
//   // bool isloaded = true;
//   @override
//   Widget build(BuildContext context) {
//
//     var marker = {
//       Marker(
//           markerId:const MarkerId("Current-Position"),
//           position: LatLng(
//               currentPosition?.latitude ?? 0,
//               currentPosition?.longitude ?? 0
//           )
//       ),
//     };
//
//     return Scaffold(
//         floatingActionButtonLocation: FloatingActionButtonLocation.centerDocked,
//         floatingActionButton: Padding(
//           padding: const EdgeInsets.all(8.0),
//           child: FloatingActionButton.extended(
//             onPressed: () {
//               Navigator.of(context).pop(location);
//             },
//             label: const Text('Save Location'),
//             icon: const Icon(Icons.location_on_outlined),
//           ),
//         ),
//         body: currentPosition == null
//             ? const Center(
//           child: CircularProgressIndicator(),
//         )
//             : SafeArea(
//           child: GoogleMap(
//             myLocationButtonEnabled: true,
//             myLocationEnabled: true,
//             markers: marker,
//             mapType: MapType.normal,
//             onTap: (selectedPosition) {
//               marker.remove(const Marker(markerId: MarkerId('Current-Position')));
//               marker.add(
//                   Marker(
//                       visible: true,
//                       markerId: const MarkerId('Current-Position'),
//                       position: selectedPosition
//                   )
//               );
//               print('---------------------------');
//               print(selectedPosition.toString());
//               print('---------------------------');
//               location = selectedPosition;
//               setState(() {});
//             },
//             initialCameraPosition: CameraPosition(
//               target: LatLng(
//                   currentPosition?.latitude ?? 0,
//                   currentPosition?.longitude ?? 0
//               ),
//               zoom: 15,
//             ),
//             onMapCreated: (GoogleMapController controller) {
//               _controller.complete(controller);
//             },
//           ),
//         ));
//   }
//
//   Future<void> _goToTheLocation(CameraPosition? location) async {
//     if (location != null) {
//       final GoogleMapController controller = await _controller.future;
//       controller.animateCamera(CameraUpdate.newCameraPosition(location));
//     } else {}
//   }
// }
//
// }
