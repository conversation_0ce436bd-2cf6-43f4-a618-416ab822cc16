import 'dart:developer';

import 'package:bus/bloc/classroom__cubit/class_room_cubit.dart';
import 'package:bus/bloc/grade_cubit/grade_cubit.dart';
import 'package:bus/bloc/grade_cubit/grade_states.dart';
import 'package:bus/config/global_variable.dart';
import 'package:bus/config/theme_colors.dart';
import 'package:bus/data/models/student_models/student_grade_models.dart';
import 'package:bus/translations/local_keys.g.dart';
import 'package:bus/utils/assets_utils.dart';
import 'package:bus/views/custom_widgets/custom_button.dart';
import 'package:bus/views/custom_widgets/custom_drop_down_button.dart';
import 'package:bus/views/custom_widgets/custom_form_field_border.dart';
import 'package:bus/views/custom_widgets/custom_text.dart';
import 'package:bus/widgets/custom_appbar.dart';
import 'package:dio/dio.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';

import '../../../config/config_base.dart';

class AddNewClassroomScreen extends StatefulWidget {
  // static const String routeName = PathRouteName.addBus;
  final ClassRoomModel? editBus;
  final bool isEdit;

  AddNewClassroomScreen({Key? key, required this.isEdit, this.editBus})
      : super(key: key);

  @override
  State<AddNewClassroomScreen> createState() => _AddNewClassroomScreenState();
}

class _AddNewClassroomScreenState extends State<AddNewClassroomScreen> {
  String name = "";
  final _formKey = GlobalKey<FormState>();
  TextEditingController? txcBusName;
  bool isLoading = false;

  int? gradeId = 4;
  @override
  void initState() {
    try {
      if (widget.isEdit) {
        txcBusName = TextEditingController(text: widget.editBus?.name ?? "");
        gradeId = widget.editBus!.grade_id!;
        name = txcBusName?.text ?? "";
      }
    } catch (e) {
      debugPrint(e.toString());
    }

    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: CustomAppBar(
        rightWidget: const SizedBox(),
        titleWidget: Row(
          children: [
            CustomText(
              text: AppStrings.addClassroom.tr(),
              fontSize: 18,
              textAlign: TextAlign.center,
              fontW: FontWeight.w600,
              color: TColor.white,
            ),
            const SizedBox(width: 10.0),
          ],
        ),
        leftWidget: context.locale.toString() == "ar"
            ? InkWell(
                onTap: () {
                  Navigator.pop(context);
                },
                child: SvgPicture.asset(AppAssets.arrowBack),
              )
            : InkWell(
                onTap: () {
                  Navigator.pop(context);
                },
                child: SvgPicture.asset(
                  AppAssets.forwardArrow,
                  colorFilter:
                      const ColorFilter.mode(TColor.white, BlendMode.srcIn),
                  width: 25.w,
                  height: 25.w,
                ),
              ),
      ),
      body: Form(
        key: _formKey,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisAlignment: MainAxisAlignment.end,
          children: [
            Padding(
              padding: EdgeInsets.only(left: 40.w, right: 40.w, bottom: 10.h),
              child: CustomText(
                text: AppStrings.grade.tr(),
                fontSize: 15,
                textAlign: TextAlign.center,
                fontW: FontWeight.bold,
                color: TColor.black,
              ),
            ),
            BlocBuilder<GradeCubit, GradeStates>(
              builder: (context, state) {
                if (state is GradeLoadingStates) {
                  return const Center(
                    child: CircularProgressIndicator(),
                  );
                } else if (state is GradeSuccessStates) {
                  List<StudentGradeModels> listOfGradeIds = [];
                  listOfGradeIds.add(StudentGradeModels(
                      id: 0, name: AppStrings.selectGrade.tr()));
                  state.gradeModels?.data?.forEach(
                    (element) {
                      listOfGradeIds.add(StudentGradeModels(
                          id: element.id, name: element.name));
                      print(element.id);
                      print(element.name);
                    },
                  );
                  listOfGradeIds.map<DropdownMenuItem<int>>((dynamic value) {
                    return DropdownMenuItem<int>(
                      value: value.id,
                      child: Padding(
                        padding: EdgeInsets.symmetric(horizontal: 20.w),
                        child: Text(value.name!),
                      ),
                    );
                  }).toList();

                  return CustomDropDownButton(
                      items: listOfGradeIds
                          .map<DropdownMenuItem<int>>((dynamic value) {
                        return DropdownMenuItem<int>(
                          value: value.id,
                          child: Padding(
                            padding: EdgeInsets.symmetric(horizontal: 20.w),
                            child: Text(value.name!),
                          ),
                        );
                      }).toList(),
                      onChanged: (v) {
                        setState(() {
                          if (kDebugMode) {
                            print("id: ${v}");
                          }
                          gradeId = v as int;
                        });
                        if (v == 0) {
                          return;
                        }
                      },
                      value: gradeId);
                } else if (state is GradeErrorStates) {
                  return const SizedBox();
                } else {
                  return const SizedBox();
                }
              },
            ),
            20.verticalSpace,
            CustomFormFieldWithBorder(
              isTitled: true,
              title: AppStrings.classroomName.tr(),
              controller: txcBusName,
              requierdNumber: 4,
              formFieldWidth: 428,
              validation: AppStrings.validName.tr(),
              heightA: 53,
              hintText: AppStrings.classroomName.tr(),
              borderColor: TColor.fillFormFieldB,
              fillColor: TColor.fillFormFieldB,
              radiusNumber: 15.0,
              paddingRight: 37.w,
              onChanged: (p0) {
                name = p0;
              },
              paddingLeft: 37.w,
              contentPaddingVertical: 15,
              contentPaddingHorizontal: 15,
            ),
            const Spacer(),
            Padding(
              padding: EdgeInsets.symmetric(horizontal: 37.w),
              child: CustomButton(
                loading: isLoading,
                text: AppStrings.add.tr(),
                onTap: () async {
                  if (_formKey.currentState!.validate()) {
                    _formKey.currentState!.save();
                    setState(() {
                      isLoading = true;
                    });
                    Response? response;
                    try {
                      print(gradeId);
                      Map<String, dynamic> datamap = {
                        "name": name,
                        "grade_id": gradeId,
                      };

                      log("datamap:${datamap.toString()}  ");
                      FormData formData = FormData.fromMap(datamap);

                      if (kDebugMode) {
                        print(formData.toString());
                      }
                      final authHeaders = {'Authorization': "Bearer $token"};
                      String? classid = widget.editBus?.id.toString();
                      print(classid);
                      response = widget.isEdit
                          ? await Dio().post(
                              "${ConfigBase.baseUrl}classrooms/update/$classid",
                              options: Options(
                                headers: authHeaders,
                              ),
                              data: formData)
                          : await Dio()
                              .post("${ConfigBase.baseUrl}classrooms/store",
                                  options: Options(
                                    headers: authHeaders,
                                  ),
                                  data: formData);

                      if (kDebugMode) {
                        print(response.data.toString());
                      }
                      Navigator.pop(context);
                      // ignore: use_build_context_synchronously
                      BlocProvider.of<ClassRoomCubit>(context)
                          .getAllClassRooms();
                      ScaffoldMessenger.of(context).showSnackBar(
                        SnackBar(
                          backgroundColor: TColor.greenSuccess,
                          content: CustomText(
                            text: AppStrings.successfullyDone.tr(),
                            fontSize: 18,
                            maxLine: 5,
                            color: TColor.white,
                          ),
                        ),
                      );
                    } on DioException catch (e) {
                      if (e.response != null) {
                        response = e.response;

                        log(e.error.toString(), name: 'dioError');
                        log(response.toString(), name: 'dioError');
                        String text = response!.data["messages"]
                                .toString()
                                .contains('The selected grade id is invalid')
                            ? AppStrings.selectGradeFirst.tr()
                            : response.data["messages"].toString();
                        ScaffoldMessenger.of(context).showSnackBar(
                          SnackBar(
                            backgroundColor: TColor.redAccent,
                            content: CustomText(
                              text: text,
                              fontSize: 18,
                              maxLine: 5,
                              color: TColor.white,
                            ),
                          ),
                        );
                      }
                    }
                  } else {
                    print("error");
                  }
                  setState(() {
                    isLoading = false;
                  });
                },
                width: 428,
                height: 53,
                radius: 15,
                borderColor: TColor.mainColor,
                bgColor: TColor.mainColor,
              ),
            ),
            20.verticalSpace,
          ],
        ),
      ),
    );
  }
}
