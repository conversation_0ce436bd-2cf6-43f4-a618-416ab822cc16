
import 'package:bus/constant/path_route_name.dart';
import 'package:bus/widgets/custom_appbar.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:easy_localization/easy_localization.dart';

import '../../../config/theme_colors.dart';
import '../../../bloc/subscription_cubit/subscription_cubit.dart';
import '../../custom_widgets/custom_button.dart';
import '../../custom_widgets/custom_form_field_border.dart';
import '../../custom_widgets/custom_text.dart';
import '../../../translations/local_keys.g.dart';
class CouponScreen extends StatefulWidget {
  static const String routeName = PathRouteName.couponScreen;

  const CouponScreen({super.key});

  @override
  State<CouponScreen> createState() => _CouponScreenState();
}

class _CouponScreenState extends State<CouponScreen> {
  final _formKey = GlobalKey<FormState>();
  final TextEditingController _couponController = TextEditingController();

  final List<Map<String, dynamic>> _subscriptionFeatures = [
    {
      'icon': Icons.directions_bus_outlined,
      'title': AppStrings.busManagement,
      'description': AppStrings.busManagementDesc
    },
    {
      'icon': Icons.people_outline,
      'title': AppStrings.staffManagement,
      'description': AppStrings.staffManagementDesc
    },
    {
      'icon': Icons.location_on_outlined,
      'title': AppStrings.realTimeTracking,
      'description': AppStrings.realTimeTrackingDesc
    },
    {
      'icon': Icons.fact_check_outlined,
      'title': AppStrings.attendanceSystem,
      'description': AppStrings.attendanceSystemDesc
    },
  ];

  @override
  void dispose() {
    _couponController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: CustomAppBar(
        titleWidget: CustomText(
          text: AppStrings.applyCoupon.tr(),
          fontSize: 20.sp,
          textAlign: TextAlign.center,
          fontW: FontWeight.w600,
          color: TColor.white,
        ),
     
      
      
      ),
      body: SafeArea(
        child: SingleChildScrollView(
          physics: const BouncingScrollPhysics(),
          child: Padding(
            padding: EdgeInsets.symmetric(horizontal: 24.w, vertical: 16.h),
            child: Form(
              key: _formKey,
              child: Column(
                children: [
                  // Header Section
                  
                  Container(
                    margin: EdgeInsets.only(bottom: 10.h),
                    child: Column(
                      crossAxisAlignment:  CrossAxisAlignment.start,
                      children: [
                         CustomText(
                          text: AppStrings.enterCouponDescription.tr(),
                          fontSize: 18.sp,
                          color: Colors.grey[600]?? Colors.grey,
                          textAlign: TextAlign.center,
                          maxLine: 2,
                        ),
                       SizedBox(height: 8.h),
                           Container(
                    margin: EdgeInsets.only(bottom: 10.h),
                    padding: EdgeInsets.all(10.w),
                    decoration: BoxDecoration(
                      color: TColor.white,
                      borderRadius: BorderRadius.circular(15.r),
                      border: Border.all(
                        color: TColor.mainColor.withOpacity(0.3),
                      ),
                    ),
                    child: Row(
                      children: [
                        CustomText(
                          text: "${AppStrings.coupon.tr()}: ",
                          fontSize: 16.sp,
                          fontW: FontWeight.w600,
                          color: TColor.mainColor,
                        ),
                        Expanded(
                          child: CustomText(
                            text: "AmwagSoft", // Replace with your actual coupon code
                            fontSize: 18.sp,
                            fontW: FontWeight.bold,
                            color: TColor.black,
                          ),
                        ),
                        IconButton(
                          onPressed: () {
                            Clipboard.setData(
                              const ClipboardData(text: "AmwagSoft"), // Replace with your actual coupon code
                            ).then((_) {
                              _showSnackBar(
                                context,
                                AppStrings.copied.tr(),
                                TColor.greenSuccess,
                              );
                            });
                          },
                          icon: Icon(
                            Icons.copy,
                            color: TColor.mainColor,
                            size: 24.w,
                          ),
                        ),
                      ],
                    ),
                  ),

                  
                         SizedBox(height: 8.h),

                        CustomText(
                          text: AppStrings.benefits.tr(),
                          fontSize: 24.sp,
                          fontW: FontWeight.bold,
                          color: TColor.mainColor,
                        ),
                       
                      ],
                    ),
                  ),

                  // Features List
                  ListView.separated(
                    shrinkWrap: true,
                    physics: const NeverScrollableScrollPhysics(),
                    itemCount: _subscriptionFeatures.length,
                    separatorBuilder: (context, index) => SizedBox(height: 16.h),
                    itemBuilder: (context, index) => buildFeatureCard(
                      icon: _subscriptionFeatures[index]['icon'],
                      title: _subscriptionFeatures[index]['title'],
                      description: _subscriptionFeatures[index]['description'],
                    ),
                  ),

                  // Coupon Input Section
                  Container(
                    margin: EdgeInsets.symmetric(vertical: 32.h),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        CustomText(
                          text: AppStrings.enterCoupon.tr(),
                          fontSize: 16.sp,
                          fontW: FontWeight.w600,
                          color: TColor.mainColor,
                          padding: EdgeInsets.only(bottom: 8.h),
                        ),
                        CustomFormFieldWithBorder(
                          controller: _couponController,
                          validation: '${AppStrings.coupon.tr()} ${AppStrings.isRequired.tr()}',
                          contentPaddingVertical: 16,
                          inputType: TextInputType.text,
                          textCapitalization: TextCapitalization.characters,
                          prefix: Container(
                            padding: EdgeInsets.all(12.w),
                            child: Icon(
                              Icons.confirmation_number_outlined,
                              color: TColor.mainColor,
                              size: 20.w,
                            ),
                          ),
                          suffix: IconButton(
                            icon: Icon(
                              Icons.clear,
                              size: 20.w,
                              color: Colors.grey,
                            ),
                            onPressed: () => _couponController.clear(),
                          ),
                          formFieldWidth: double.infinity,
                          hintText: AppStrings.enterCoupon.tr(),
                          borderColor: TColor.mainColor.withOpacity(0.3),
                          fillColor: TColor.white,
                          radiusNumber: 15.0,
                        ),
                      ],
                    ),
                  ),

                  // Coupon Display Row
               // Apply Button
                  BlocConsumer<SubscriptionCubit, SubscriptionState>(
                    listener: (context, state) {
                      if (state is SubscriptionSuccess) {
                        _showSnackBar(
                          context,
                          AppStrings.subscribedSuccessfully.tr(),
                          TColor.greenSuccess,
                        );
                        Navigator.pop(context);
                      } else if (state is SubscriptionError) {
                        _showSnackBar(
                          context,
                          state.message,
                          TColor.redAccent,
                        );
                      }
                    },
                    builder: (context, state) {
                      return CustomButton(
                        loading: state is couponLoading,
                        text: AppStrings.apply.tr(),
                        fontSize: 18.sp,
                        onTap: () {
                          if (_formKey.currentState!.validate()) {
                            context.read<SubscriptionCubit>().couponSubscription(
                                  code: _couponController.text,
                                  context: context,
                                );
                          }
                        },
                        radius: 15.sp,
                        width: double.infinity,
                        height: 56.h,
                        borderColor: TColor.mainColor,
                        bgColor: TColor.mainColor,
                      );
                    },
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget buildFeatureCard({
    required IconData icon,
    required String title,
    required String description,
  }) {
    return Container(
      padding: EdgeInsets.all(16.w),
      decoration: BoxDecoration(
        color: TColor.white,
        borderRadius: BorderRadius.circular(16.r),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.08),
            spreadRadius: 1,
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Row(
        children: [
          Container(
            padding: EdgeInsets.all(12.w),
            decoration: BoxDecoration(
              color: TColor.mainColor.withOpacity(0.1),
              borderRadius: BorderRadius.circular(12.r),
            ),
            child: Icon(
              icon,
              color: TColor.mainColor,
              size: 24.w,
            ),
          ),
          SizedBox(width: 16.w),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                CustomText(
                  text: title.tr(),
                  fontSize: 16.sp,
                  fontW: FontWeight.w600,
                  color: TColor.black,
                ),
                SizedBox(height: 4.h),
                CustomText(
                  text: description.tr(),
                  fontSize: 13.sp,
                  color: Colors.grey[600]?? Colors.grey,
                  maxLine: 2,
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  void _showSnackBar(BuildContext context, String message, Color backgroundColor) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        backgroundColor: backgroundColor,
        behavior: SnackBarBehavior.floating,
        margin: EdgeInsets.all(16.w),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(12.r),
        ),
        content: CustomText(
          text: message,
          color: TColor.white,
          maxLine: 3,
        ),
      ),
    );
  }
}