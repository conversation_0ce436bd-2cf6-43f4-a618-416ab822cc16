import 'package:bus/bloc/bus_available_cubit/bus_available_cubit.dart';
import 'package:bus/bloc/delete_driver_supervisor_cubit/delete_driver_supervisor_cubit.dart';
import 'package:bus/bloc/driver_cubit/driver_cubit.dart';
import 'package:bus/bloc/driver_cubit/driver_states.dart';
import 'package:bus/config/theme_colors.dart';
import 'package:bus/utils/assets_utils.dart';
import 'package:bus/utils/helpers.dart';
import 'package:bus/utils/sized_box.dart';
import 'package:bus/views/custom_widgets/build_table_row_widget.dart';
import 'package:bus/views/custom_widgets/custom_text.dart';
import 'package:bus/views/screens/add_driver_screen/add_driver_screen.dart';
import 'package:bus/widgets/custom_appbar.dart';
import 'package:bus/widgets/student_widgets/custom_search_w.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:url_launcher/url_launcher.dart';

import '../../../constant/path_route_name.dart';
import 'package:bus/translations/local_keys.g.dart';
import '../show_bus_on_map_screen/show_bus_on_map_screen.dart';

class AllDriverScreen extends StatefulWidget {
  static const String routeName = PathRouteName.allDriver;

  const AllDriverScreen({Key? key}) : super(key: key);

  @override
  State<AllDriverScreen> createState() => _AllDriverScreenState();
}

class _AllDriverScreenState extends State<AllDriverScreen> {
  final ScrollController? scrollController = ScrollController();

  @override
  void initState() {
    context.read<DriverCubit>().initScrollController(
        isFirst: false,
        scrollController: scrollController,
        setStates: () {
          setState(() {});
        });
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: CustomAppBar(
        titleWidget: CustomText(
          text: AppStrings.allDrivers.tr(),
          fontSize: 18,
          textAlign: TextAlign.center,
          fontW: FontWeight.w600,
          color: TColor.white,
        ),
        leftWidget: context.locale.toString() == "ar"
            ? InkWell(
                onTap: () {
                  Navigator.pop(context);
                },
                child: SvgPicture.asset(AppAssets.arrowBack),
              )
            : InkWell(
                onTap: () {
                  Navigator.pop(context);
                },
                child: SvgPicture.asset(
                  AppAssets.forwardArrow,
                  colorFilter:
                      const ColorFilter.mode(TColor.white, BlendMode.srcIn),
                  width: 25.w,
                  height: 25.w,
                ),
              ),
      ),
      body: SingleChildScrollView(
        controller: scrollController,
        child: Column(
          children: [
            CustomSearchW(
              hintText: AppStrings.searchDriverHint.tr(),
              type: "drive",
            ),
            BlocBuilder<DriverCubit, DriverStates>(
              buildWhen: (previous, current) => current is DriverSuccessStates,
              builder: (context, states) {
                if (states is DriverLoadingStates) {
                  return const Center(
                    child: CircularProgressIndicator(
                      color: TColor.mainColor,
                    ),
                  );
                } else if (states is DriverSuccessStates) {
                  if (context.watch<DriverCubit>().data.isEmpty) {
                    return SizedBox(
                      width: 300.w,
                      height: 300.w,
                      child: Center(
                        child: CustomText(
                          text: AppStrings.driverNotFound.tr(),
                          fontSize: 17,
                          fontW: FontWeight.w600,
                        ),
                      ),
                    );
                  } else {
                    int length = context.watch<DriverCubit>().data.length;
                    if (kDebugMode) {
                      debugPrint(length.toString());
                    }
                    return Column(
                      children: [
                        Padding(
                          padding: const EdgeInsets.all(8.0),
                          child: Container(
                            margin: const EdgeInsets.symmetric(horizontal: 8.0),
                            clipBehavior: Clip.antiAlias,
                            decoration: BoxDecoration(
                              borderRadius: BorderRadius.circular(15.0),
                            ),
                            child: Table(
                              columnWidths: const {
                                0: FlexColumnWidth(2.5),
                                1: FlexColumnWidth(2),
                                2: FlexColumnWidth(1),
                                3: FlexColumnWidth(1),
                              },
                              border: TableBorder.all(
                                  color: TColor.tabColors,
                                  borderRadius: BorderRadius.circular(15.0)),
                              children: List.generate(
                                  1 + context.watch<DriverCubit>().data.length,
                                  (index) {
                                if (index == 0) {
                                  return BuildTableRowWidget(
                                    cell: [
                                      AppStrings.name.tr(),
                                      AppStrings.busName.tr(),
                                      AppStrings.call.tr(),
                                      AppStrings.options.tr()
                                    ],
                                    header: true,
                                  ).build(context);
                                } else {
                                  final newDriver = context
                                      .watch<DriverCubit>()
                                      .data[index - 1];
                                  return BuildTableRowWidget(
                                      is2Icon: true,
                                      isTabDown: true,
                                      cell: [
                                        newDriver.name,
                                        newDriver.bus?.name ??
                                            AppStrings.notFound.tr(),
                                        Icons.call,
                                        Icons.more_horiz,
                                      ],
                                      onTapDown: (position) {
                                        context
                                            .read<BusAvailableCubit>()
                                            .busAvailable();
                                        Helpers.customShowDialog(context,
                                            position: position.globalPosition,
                                            onTapEdit: () {
                                          Navigator.of(context)
                                            ..pop()
                                            ..push(MaterialPageRoute(
                                                builder: (ctx) {
                                              return AddDriverScreen(
                                                isEdit: true,
                                                driverInfo: newDriver,
                                              );
                                            }));
                                        }, onTapShow: () {
                                          Navigator.of(context)
                                            ..pop()
                                            ..pushNamed(
                                              PathRouteName.driverData,
                                              arguments: newDriver,
                                            );
                                        }, onTapDelete: () {
                                          context
                                              .read<
                                                  DeleteDriverSupervisorCubit>()
                                              .deleteDriverSupervisor(
                                                  id: newDriver.id)
                                              .whenComplete(() => context
                                                  .read<DriverCubit>()
                                                  .getDriver(
                                                      pageNumber: 1,
                                                      isFirst: true));
                                          Navigator.pop(context);
                                        }, onTapLocation: () {
                                          Navigator.of(context)
                                            ..pop()
                                            ..push(MaterialPageRoute(
                                                builder: (ctx) {
                                              return ShowBusOnMapScreen(
                                                name: newDriver.name ?? '',
                                                busName:
                                                    newDriver.bus?.name ?? '',
                                                busId:
                                                    (newDriver.bus_id != null)
                                                        ? newDriver.bus_id
                                                            .toString()
                                                        : '',
                                                type: newDriver.type!,
                                              );
                                            }));
                                        });
                                      },
                                      onTapBeforeLastCell: () async {
                                        if (!await launchUrl(Uri.parse(
                                            "tel:${newDriver.phone}"))) {
                                          throw Exception(
                                              'Could not launch url');
                                        }
                                      }).build(context);
                                }
                              }),
                            ),
                          ),
                        ),
                        const SBox(h: 10.0),
                        context.watch<DriverCubit>().currentPage! <
                                context.watch<DriverCubit>().last_pages!
                            ? Icon(
                                Icons.arrow_downward_rounded,
                                size: 40.sp,
                              )
                            : const SizedBox.shrink(),
                        const SBox(
                          h: 50,
                        )
                      ],
                    );
                  }
                } else if (states is DriverErrorStates) {
                  if (kDebugMode) {
                    debugPrint(states.error);
                  }
                  if (states.error == "drivers not found") {
                    return SizedBox(
                      width: 300.w,
                      height: 300.w,
                      child: Center(
                        child: CustomText(
                          text: AppStrings.driverNotFound.tr(),
                          fontSize: 17,
                          fontW: FontWeight.w600,
                        ),
                      ),
                    );
                  } else {
                    return const SizedBox();
                  }
                } else {
                  return const SizedBox();
                }
              },
            ),
          ],
        ),
      ),
    );
  }
}
