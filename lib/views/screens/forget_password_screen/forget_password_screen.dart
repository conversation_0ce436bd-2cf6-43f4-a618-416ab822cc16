import 'package:bus/bloc/forgot_password_cubit/forgot_password_cubit.dart';
import 'package:bus/config/theme_colors.dart';
import 'package:bus/constant/path_route_name.dart';
import 'package:bus/translations/local_keys.g.dart';
import 'package:bus/utils/assets_utils.dart';
import 'package:bus/utils/sized_box.dart';
import 'package:bus/views/custom_widgets/custom_button.dart';
import 'package:bus/views/custom_widgets/custom_form_field_border.dart';
import 'package:bus/views/custom_widgets/custom_text.dart';
import 'package:bus/views/screens/send_code_screen/send_code_screen.dart';
import 'package:bus/widgets/custom_background_image.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class ForgetPasswordScreen extends StatefulWidget {
  static const String routeName = PathRouteName.forget;

  const ForgetPasswordScreen({Key? key}) : super(key: key);

  @override
  State<ForgetPasswordScreen> createState() => _ForgetPasswordScreenState();
}

class _ForgetPasswordScreenState extends State<ForgetPasswordScreen> {
  String? email;
  final _formKey = GlobalKey<FormState>();

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: SingleChildScrollView(
        child: CustomBackgroundImage(
          child: Column(
            children: [
              const SBox(h: 60),
              Image.asset(
                assetsImages("logo.png"),
                height: 121.w,
                width: 124.w,
              ),
              const SBox(h: 60),
              CustomText(
                text: AppStrings.forget.tr(),
                color: TColor.white,
                fontW: FontWeight.w700,
                fontSize: 20,
              ),
              const SBox(h: 20),
              Container(
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(30.r),
                  color: TColor.white,
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withOpacity(0.2),
                      blurRadius: 3,
                      offset: const Offset(0, 4), // changes position of shadow
                    ),
                  ],
                ),
                width: 354.w,
                child: Column(
                  children: [
                    const SBox(h: 40),
                    Form(
                      key: _formKey,
                      child: CustomFormFieldWithBorder(
                        prefix: const Icon(
                          Icons.mail_outline_outlined,
                          color: TColor.iconInputColor,
                        ),
                        formFieldWidth: 307,
                        hintText: AppStrings.email.tr(),
                        borderColor: TColor.fillFormFieldB,
                        fillColor: TColor.fillFormFieldB,
                        radiusNumber: 15.0,
                        validation:
                            '${AppStrings.email.tr()} ${AppStrings.isRequired.tr()}',
                        onChanged: (value) {
                          email = value;
                        },
                      ),
                    ),
                    const SBox(h: 20),
                    CustomText(
                      text: AppStrings.sendCodeRegister.tr(),
                      color: TColor.textLogin,
                      fontW: FontWeight.w400,
                      fontSize: 11,
                    ),
                    const SBox(h: 50),
                    BlocConsumer<ForgotPasswordCubit, ForgotPasswordState>(
                      listener: (context, state) {
                        if (state is ForgotPasswordSuccess) {
                          Navigator.push(
                            context,
                            MaterialPageRoute(
                              builder: (BuildContext context) => SendCodeScreen(
                                isForgotPassword: true,
                                email: email,
                              ),
                            ),
                          );
                        }
                      },
                      builder: (context, state) {
                        if (state is ForgotPasswordLoading) {
                          return const CircularProgressIndicator(
                            color: TColor.mainColor,
                          );
                        } else {
                          return CustomButton(
                            text: AppStrings.sendCode.tr(),
                            onTap: () {
                              if (_formKey.currentState!.validate()) {
                                _formKey.currentState!.save();
                                ForgotPasswordCubit.get(context)
                                    .forgotPassword(email: email);
                              }
                            },
                            width: 307,
                            height: 48,
                            radius: 15,
                            borderColor: TColor.mainColor,
                            bgColor: TColor.mainColor,
                          );
                        }
                      },
                    ),
                    const SBox(h: 40),
                    // InkWell(
                    //   onTap: () {
                    //     Navigator.pushNamed(context, PathRouteName.signup);
                    //   },
                    //   child: CustomText(
                    //     text: LocaleKeys.sendCodeAgain.tr(),
                    //     fontSize: 13,
                    //     fontW: FontWeight.w400,
                    //     color: TColor.mainColor,
                    //   ),
                    // ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
