import 'package:bus/bloc/grade_cubit/grade_cubit.dart';
import 'package:bus/bloc/grade_cubit/grade_states.dart';
import 'package:bus/config/global_variable.dart';
import 'package:bus/config/theme_colors.dart';
import 'package:bus/constant/path_route_name.dart';
import 'package:bus/data/models/student_models/student_grade_models.dart';
import 'package:bus/utils/assets_utils.dart';
import 'package:bus/utils/sized_box.dart';
import 'package:bus/views/custom_widgets/custom_button.dart';
import 'package:bus/views/custom_widgets/custom_text.dart';
import 'package:bus/widgets/custom_appbar.dart';
import 'package:dio/dio.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';

import '../../../config/config_base.dart';
import 'package:bus/translations/local_keys.g.dart';

class SelectGradesScreen extends StatefulWidget {
  static const String routeName = PathRouteName.student;

  const SelectGradesScreen({Key? key}) : super(key: key);

  @override
  State<SelectGradesScreen> createState() => _SelectGradesScreenState();
}

class _SelectGradesScreenState extends State<SelectGradesScreen> {
  List<int> selectedgradeIds = [];
  bool? incubation4 = false;
  bool? primary1 = false;
  bool? preparatory2 = false;
  bool? secondary3 = false;
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: CustomAppBar(
        rightWidget: Container(
          padding: EdgeInsets.symmetric(horizontal: 6.0.w, vertical: 3.0.w),
          decoration: BoxDecoration(
            color: TColor.darkRed,
            borderRadius: BorderRadius.circular(5.0.r),
          ),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              const SizedBox(width: 5.0),
              SvgPicture.asset(AppAssets.supervisorIcon),
            ],
          ),
        ),
        titleWidget: Row(
          children: [
            CustomText(
              text: AppStrings.students.tr(),
              fontSize: 18,
              textAlign: TextAlign.center,
              fontW: FontWeight.w600,
              color: TColor.white,
            ),
            const SizedBox(width: 10.0),
          ],
        ),
        leftWidget: context.locale.toString() == "ar"
            ? InkWell(
                onTap: () {
                  Navigator.pop(context);
                },
                child: SvgPicture.asset(AppAssets.arrowBack),
              )
            : InkWell(
                onTap: () {
                  Navigator.pop(context);
                },
                child: SvgPicture.asset(
                  AppAssets.forwardArrow,
                  colorFilter: const ColorFilter.mode(TColor.white, BlendMode.srcIn),
                  width: 25.w,
                  height: 25.w,
                ),
              ),
      ),
      body: SingleChildScrollView(
        child: SafeArea(
          child: Column(
            children: [
              const SBox(h: 20),
              BlocBuilder<GradeCubit, GradeStates>(
                builder: (context, states) {
                  if (states is GradeLoadingStates) {
                    return const Center(
                      child: CircularProgressIndicator(),
                    );
                  } else if (states is GradeSuccessStates) {
                    List<StudentGradeModels> listOfGradeIds = [];

                    states.gradeModels?.data?.forEach(
                      (element) {
                        print("${element.name}+ id =${element.id}");
                        listOfGradeIds.add(StudentGradeModels(
                            id: element.id, name: element.name));
                      },
                    );

                    return Column(children: [
                      ListTile(
                        title: const CustomText(
                          color: Colors.black54,
                          text: "رياض الأطفال",
                          fontSize: 15,
                          fontW: FontWeight.w600,
                        ),
                        trailing: Checkbox(
                          value: incubation4,
                          onChanged: (value) {
                            incubation4 = value;
                            setState(() {});
                          },
                        ),
                      ),
                      ListTile(
                        title: const CustomText(
                          color: Colors.black54,
                          text: "المرحلة الإبتدائية",
                          fontSize: 15,
                          fontW: FontWeight.w600,
                        ),
                        trailing: Checkbox(
                          value: primary1,
                          onChanged: (value) {
                            primary1 = value;
                            setState(() {});
                          },
                        ),
                      ),
                      ListTile(
                        title: const CustomText(
                          color: Colors.black54,
                          text: "المرحلة الإعدادية",
                          fontSize: 15,
                          fontW: FontWeight.w600,
                        ),
                        trailing: Checkbox(
                          value: preparatory2,
                          onChanged: (value) {
                            setState(() {});
                            preparatory2 = value;
                          },
                        ),
                      ),
                      ListTile(
                        title: const CustomText(
                          color: Colors.black54,
                          text: "المرحلة الثانوية",
                          fontSize: 15,
                          fontW: FontWeight.w600,
                        ),
                        trailing: Checkbox(
                          value: secondary3,
                          onChanged: (value) {
                            secondary3 = value;
                            setState(() {});
                          },
                        ),
                      ),
                      Padding(
                        padding: EdgeInsets.symmetric(horizontal: 37.w),
                        child: CustomButton(
                          text: AppStrings.add.tr(),
                          onTap: () async {
                            selectedgradeIds = [];
                            if (secondary3!) {
                              selectedgradeIds.add(3);
                            }
                            if (preparatory2!) {
                              selectedgradeIds.add(2);
                            }
                            if (primary1!) {
                              selectedgradeIds.add(1);
                            }
                            if (incubation4!) {
                              selectedgradeIds.add(4);
                            }
                            final authHeaders = {
                              'Authorization': "Bearer $token",
                            };
                            var respo = await Dio().post(
                              "${ConfigBase.baseUrl}grades/store",
                              data: {"grade_id": selectedgradeIds},
                              options: Options(
                                headers: authHeaders,
                              ),
                            );
                            print(respo.data.toString());
                            if (respo.statusCode == 200) {
                              Navigator.pop(context);
                              ScaffoldMessenger.of(context).showSnackBar(
                                const SnackBar(
                                  backgroundColor: TColor.greenSuccess,
                                  content: CustomText(
                                    text: "Grades added Successfully .",
                                    color: TColor.white,
                                  ),
                                ),
                              );
                            }
                          },
                          width: 428,
                          height: 53,
                          radius: 15,
                          borderColor: TColor.mainColor,
                          bgColor: TColor.mainColor,
                        ),
                      ),
                    ]);
                  } else if (states is GradeErrorStates) {
                    return const SizedBox();
                  } else {
                    return const SizedBox();
                  }
                },
              ),
              const SBox(h: 80),
            ],
          ),
        ),
      ),
    );
  }
}
