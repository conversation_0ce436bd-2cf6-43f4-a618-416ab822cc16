// ignore_for_file: use_build_context_synchronously

import 'dart:io'; // Import to check platform
import 'package:bus/bloc/register_cubit/register_cubit.dart';
import 'package:bus/bloc/subscription_cubit/subscription_cubit.dart';
import 'package:bus/config/config_base.dart';
import 'package:bus/helper/network_serviecs.dart';
import 'package:bus/utils/sized_box.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:logger/logger.dart';

import '../../../config/global_variable.dart';
import '../../../config/theme_colors.dart';
import '../../../constant/path_route_name.dart';
import '../../../services/payment_service.dart';
import '../../../translations/local_keys.g.dart';
import '../../../utils/assets_utils.dart';
import '../../../widgets/custom_appbar.dart';
import '../../../widgets/custom_background_image.dart';
import '../../custom_widgets/custom_button.dart';
import '../../custom_widgets/custom_form_field_border.dart';
import '../../custom_widgets/custom_text.dart';

class PROScreen extends StatefulWidget {
  static const String routeName = PathRouteName.proScreen;

  const PROScreen({super.key});

  @override
  State<PROScreen> createState() => _PROScreenState();
}

class _PROScreenState extends State<PROScreen> {
  final formKey = GlobalKey<FormState>();
  TextEditingController couponController = TextEditingController();

  bool isLoading = false;
  final List<Map<String, dynamic>> _subscriptionFeatures = [
    {
      'icon': Icons.directions_bus_outlined,
      'title': AppStrings.busManagement,
      'description': AppStrings.busManagementDesc
    },
    {
      'icon': Icons.people_outline,
      'title': AppStrings.staffManagement,
      'description': AppStrings.staffManagementDesc
    },
    {
      'icon': Icons.location_on_outlined,
      'title': AppStrings.realTimeTracking,
      'description': AppStrings.realTimeTrackingDesc
    },
    {
      'icon': Icons.fact_check_outlined,
      'title': AppStrings.attendanceSystem,
      'description': AppStrings.attendanceSystemDesc
    },
  ];

  @override
  void initState() {
    setState(() {
      isLoading = true;
      print(isLoading);
    });
    fetchUserStatus();
    get();
    super.initState();
    PaymentService.instance.initConnection().then((_) {
      Logger()
          .e('Products loaded: ${PaymentService.instance.allProducts.length}');
      setState(() {
        isLoading = false;
        print("$isLoading + =================k");
      });
    });
  }

  @override
  void dispose() {
    couponController.dispose();
    super.dispose();
  }

  Future<void> fetchUserStatus() async {
    // Assuming you have a default name or you can pass the name as needed
    String defaultName = "schools";
    await context.read<RegisterCubit>().getUserStatus(defaultName);
    // Handle the status as needed
    debugPrint("User Status: ${context.read<RegisterCubit>().getStatus}");
    setState(() {});
  }

  String? startAt;
  String? endAt;

  Future<void> get() async {
    try {
      final _request = await NetworkService().get(
        url: ConfigBase.profile,
        isAuth: true,
      );
      if (_request.statusCode == 200) {
        startAt = _request.data["subscriptions"][0]["start_date"];
        endAt = _request.data["subscriptions"][0]["end_date"];
        debugPrint("ggggggggggggggggg");
        debugPrint(startAt);
        debugPrint("ggggggggggggggggg");

        debugPrint(_request.data["subscriptions"][0]["start_date"]);
        debugPrint("ggggggggggggggggg");

        debugPrint(endAt);
      } else {}
    } catch (e, s) {
      debugPrint("catch error $s");
      Logger().e('Error in repoHome(): $e');
    }
  }

  @override
  Widget build(BuildContext context) {
    Logger().e('Platform is iOS: ${Platform.isIOS}');
    Logger().e('subscription - Status: $subscriptionStatus');

    return CustomBackgroundImage(
      child: Scaffold(
        backgroundColor: Colors.transparent,
        appBar: CustomAppBar(
          titleWidget: CustomText(
            text: AppStrings.pro.tr(),
            fontSize: 18,
            textAlign: TextAlign.center,
            fontW: FontWeight.w600,
            color: TColor.white,
          ),
          leftWidget: context.locale.toString() == "ar"
              ? InkWell(
                  onTap: () {
                    Navigator.pop(context);
                  },
                  child: SvgPicture.asset(AppAssets.arrowBack),
                )
              : InkWell(
                  onTap: () {
                    Navigator.pop(context);
                  },
                  child: SvgPicture.asset(
                    AppAssets.forwardArrow,
                    colorFilter:
                        const ColorFilter.mode(TColor.white, BlendMode.srcIn),
                    width: 20.w,
                    height: 20.w,
                  ),
                ),
        ),
        body: isLoading == true
            ? const Center(child: CircularProgressIndicator())
            : SingleChildScrollView(
                child: Center(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.center,
                    children: [
                      10.verticalSpace,
                      Container(
                        padding: EdgeInsetsDirectional.symmetric(
                            horizontal: 16.w, vertical: 16.h),
                        decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(30.r),
                          color: TColor.white,
                          boxShadow: [
                            BoxShadow(
                              color: Colors.black.withOpacity(0.2),
                              blurRadius: 3,
                              offset: const Offset(0, 4),
                            ),
                          ],
                        ),
                        width: 354.w,
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Padding(
                              padding: const EdgeInsets.symmetric(
                                  horizontal: 15, vertical: 10),
                              child: CustomText(
                                text: AppStrings.benefitBusatyBro.tr(),
                                color: TColor.mainColor,
                                fontW: FontWeight.w700,
                                fontSize: 20,
                              ),
                            ),
                            ListView.separated(
                              shrinkWrap: true,
                              physics: const NeverScrollableScrollPhysics(),
                              itemCount: _subscriptionFeatures.length,
                              separatorBuilder: (context, index) =>
                                  SizedBox(height: 16.h),
                              itemBuilder: (context, index) => buildFeatureCard(
                                icon: _subscriptionFeatures[index]['icon'],
                                title: _subscriptionFeatures[index]['title'],
                                description: _subscriptionFeatures[index]
                                    ['description'],
                              ),
                            ),
                          ],
                        ),
                      ),
                      20.verticalSpace,
                      if (context.read<RegisterCubit>().getStatus == true)
                        Container(
                          padding: EdgeInsetsDirectional.symmetric(
                              horizontal: 16.w, vertical: 16.h),
                          decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(30.r),
                            color: TColor.white,
                            boxShadow: [
                              BoxShadow(
                                color: Colors.black.withOpacity(0.2),
                                blurRadius: 3,
                                offset: const Offset(0, 4),
                              ),
                            ],
                          ),
                          width: 354.w,
                          child:
                              BlocBuilder<SubscriptionCubit, SubscriptionState>(
                            builder: (context, state) {
                              return Column(
                                crossAxisAlignment: CrossAxisAlignment.center,
                                children: [
                                  Padding(
                                    padding: const EdgeInsets.symmetric(
                                        horizontal: 15, vertical: 10),
                                    child: Row(
                                      mainAxisAlignment:
                                          MainAxisAlignment.spaceBetween,
                                      children: [
                                        CustomText(
                                          text: subscriptionStatus == false
                                              ? AppStrings.subscribeWithCoupon
                                                  .tr()
                                              : AppStrings.alreadySubscribed
                                                  .tr(),
                                          color: TColor.mainColor,
                                          fontW: FontWeight.w700,
                                          fontSize: 20,
                                        ),
                                        SBox(),
                                      ],
                                    ),
                                  ),
                                  // Add subscription details when user is subscribed
                                  if (subscriptionStatus == true &&
                                      startAt != null &&
                                      endAt != null) ...[
                                    10.verticalSpace,
                                    Padding(
                                      padding: const EdgeInsets.symmetric(
                                          horizontal: 15.0),
                                      child: Column(
                                        crossAxisAlignment:
                                            CrossAxisAlignment.start,
                                        children: [
                                          Row(
                                            children: [
                                              Icon(Icons.calendar_today,
                                                  color: TColor.mainColor,
                                                  size: 18),
                                              SizedBox(width: 8),
                                              CustomText(
                                                text:
                                                    "${AppStrings.startDate.tr()}: ${_formatDate(startAt!)}",
                                                fontSize: 14.sp,
                                                color: TColor.black,
                                              ),
                                            ],
                                          ),
                                          10.verticalSpace,
                                          Row(
                                            children: [
                                              Icon(Icons.event,
                                                  color: TColor.mainColor,
                                                  size: 18),
                                              SizedBox(width: 8),
                                              CustomText(
                                                text:
                                                    "${AppStrings.endDate.tr()}: ${_formatDate(endAt!)}",
                                                fontSize: 14.sp,
                                                color: TColor.black,
                                              ),
                                            ],
                                          ),
                                          10.verticalSpace,
                                          Row(
                                            children: [
                                              Icon(Icons.timelapse,
                                                  color: TColor.mainColor,
                                                  size: 18),
                                              SizedBox(width: 8),
                                              CustomText(
                                                text:
                                                    "${AppStrings.remainingTime.tr()}: ${_calculateRemainingTime(endAt!)}",
                                                fontSize: 14.sp,
                                                color: TColor.black,
                                                fontW: FontWeight.w600,
                                              ),
                                            ],
                                          ),
                                        ],
                                      ),
                                    ),
                                    20.verticalSpace,
                                  ],
                                  if (subscriptionStatus == false) ...[
                                    10.verticalSpace,
                                    Padding(
                                      padding: const EdgeInsets.symmetric(
                                          horizontal: 15.0),
                                      child: Form(
                                        key: formKey,
                                        child: CustomFormFieldWithBorder(
                                          paddingLeft: 0,
                                          paddingRight: 0,
                                          readOnly: isPro == true,
                                          titleColor: TColor.mainColor,
                                          controller: couponController,
                                          validation:
                                              '${AppStrings.coupon.tr()}${AppStrings.isRequired.tr()}',
                                          contentPaddingVertical: 0,
                                          inputType: TextInputType.name,
                                          prefix: const Icon(
                                            Icons.local_offer,
                                            color: TColor.iconInputColor,
                                          ),
                                          // formFieldWidth: 307.w,
                                          hintText: AppStrings.enterCoupon.tr(),
                                          borderColor: TColor.fillFormFieldB,
                                          fillColor: TColor.fillFormFieldB,
                                          radiusNumber: 15.0,
                                        ),
                                      ),
                                    ),
                                    30.verticalSpace,
                                    Padding(
                                      padding: const EdgeInsets.symmetric(
                                          horizontal: 15.0),
                                      child: CustomButton(
                                        loading: state is couponLoading,
                                        text: AppStrings.subscribe.tr(),
                                        fontSize: 22,
                                        onTap: () {
                                          if (formKey.currentState!
                                              .validate()) {
                                            SubscriptionCubit.get(context)
                                                .couponSubscription(
                                                    code: couponController.text,
                                                    context: context);
                                          }
                                        },
                                        radius: 15.sp,
                                        height: 53,
                                        borderColor: TColor.mainColor,
                                        bgColor: TColor.mainColor,
                                      ),
                                    ),
                                    10.verticalSpace
                                  ],
                                ],
                              );
                            },
                          ),
                        ),
                      subscriptionStatus == false
                          ? 20.verticalSpace
                          : const SizedBox(),
                      if (subscriptionStatus == false)
                        Padding(
                          padding: const EdgeInsets.symmetric(
                              horizontal: 0, vertical: 10),
                          child: Container(
                            padding: EdgeInsetsDirectional.symmetric(
                                horizontal: 16.w, vertical: 16.h),
                            decoration: BoxDecoration(
                              borderRadius: BorderRadius.circular(30.r),
                              color: TColor.white,
                              boxShadow: [
                                BoxShadow(
                                  color: Colors.black.withOpacity(0.2),
                                  blurRadius: 3,
                                  offset: const Offset(0, 4),
                                ),
                              ],
                            ),
                            width: 354.w,
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                // Updated title to clearly indicate it's a subscription option.
                                Padding(
                                  padding: const EdgeInsets.symmetric(
                                      horizontal: 15, vertical: 10),
                                  child: CustomText(
                                    text: AppStrings.subscription.tr(),
                                    color: TColor.mainColor,
                                    fontW: FontWeight.w700,
                                    fontSize: 20,
                                  ),
                                ),
                                10.verticalSpace,
                                PaymentService.instance.allProducts.isNotEmpty
                                    ? ListView.separated(
                                        shrinkWrap: true,
                                        itemBuilder: (context, index) {
                                          final product = PaymentService
                                              .instance.allProducts[index];
                                          String? title = product.title;
                                          if (title != null) {
                                            // Remove any occurrence of "Apple Pay" from the product title.
                                            title = title
                                                .replaceAll("Apple Pay", "")
                                                .trim();
                                            if (title.contains("(")) {
                                              title =
                                                  title.split('(')[0].trim();
                                            }
                                          }
                                          return Padding(
                                            padding: const EdgeInsets.symmetric(
                                                horizontal: 15, vertical: 10),
                                            child: CustomButton(
                                              text:
                                                  '$title ${product.price} ${product.currency}',
                                              fontSize: 14,
                                              onTap: () async {
                                                await PaymentService.instance
                                                    .buyProduct(product);
                                              },
                                              radius: 15.sp,
                                              width: 307.w,
                                              height: 53,
                                              borderColor: TColor.mainColor,
                                              bgColor: TColor.mainColor,
                                            ),
                                          );
                                        },
                                        separatorBuilder: (context, index) =>
                                            10.verticalSpace,
                                        itemCount: PaymentService
                                            .instance.allProducts.length,
                                      )
                                    : CustomText(
                                        text: AppStrings.notFound.tr(),
                                        fontSize: 18.sp,
                                      ),
                                10.verticalSpace
                              ],
                            ),
                          ),
                        )
                      else
                        const SizedBox(),
                    ],
                  ),
                ),
              ),
      ),
    );
  }

  Widget buildFeatureCard({
    required IconData icon,
    required String title,
    required String description,
  }) {
    return Container(
      padding: EdgeInsets.all(16.w),
      decoration: BoxDecoration(
        color: TColor.white,
        borderRadius: BorderRadius.circular(16.r),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.08),
            spreadRadius: 1,
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Row(
        children: [
          Container(
            padding: EdgeInsets.all(12.w),
            decoration: BoxDecoration(
              color: TColor.mainColor.withOpacity(0.1),
              borderRadius: BorderRadius.circular(12.r),
            ),
            child: Icon(
              icon,
              color: TColor.mainColor,
              size: 24.w,
            ),
          ),
          SizedBox(width: 16.w),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                CustomText(
                  text: title.tr(),
                  fontSize: 16.sp,
                  fontW: FontWeight.w600,
                  color: TColor.black,
                ),
                SizedBox(height: 4.h),
                CustomText(
                  text: description.tr(),
                  fontSize: 13.sp,
                  color: Colors.grey[600] ?? Colors.grey,
                  maxLine: 2,
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}

// Helper method to format date string
String _formatDate(String dateString) {
  try {
    final DateTime date = DateTime.parse(dateString);
    return DateFormat('dd/MM/yyyy').format(date);
  } catch (e) {
    return dateString; // Return original string if parsing fails
  }
}

// Helper method to calculate remaining time
String _calculateRemainingTime(String endDateString) {
  try {
    final DateTime endDate = DateTime.parse(endDateString);
    final DateTime now = DateTime.now();

    if (now.isAfter(endDate)) {
      return AppStrings.expired.tr();
    }

    final Duration remaining = endDate.difference(now);
    final int days = remaining.inDays;
    final int hours = remaining.inHours % 24;

    if (days > 30) {
      // Calculate months and remaining days more accurately
      int months = 0;
      DateTime tempDate = DateTime(now.year, now.month, now.day);

      while (tempDate.add(Duration(days: 30)).isBefore(endDate)) {
        months++;
        tempDate = DateTime(tempDate.year, tempDate.month + 1, tempDate.day);
      }

      final int remainingDays = days - (months * 30);

      if (remainingDays > 0) {
        return "$months ${AppStrings.months.tr()} ${AppStrings.and.tr()} $remainingDays ${AppStrings.days.tr()}";
      } else {
        return "$months ${AppStrings.months.tr()}";
      }
    } else if (days > 0) {
      if (hours > 0) {
        return "$days ${AppStrings.days.tr()} ${AppStrings.and.tr()} $hours ${hours == 1 ? 'hour' : 'hours'}";
      } else {
        return "$days ${AppStrings.days.tr()}";
      }
    } else {
      // Less than a day remaining
      final int hours = remaining.inHours;
      final int minutes = remaining.inMinutes % 60;

      if (hours > 0) {
        return "$hours ${hours == 1 ? 'hour' : 'hours'} ${AppStrings.and.tr()} $minutes ${minutes == 1 ? 'minute' : 'minutes'}";
      } else {
        return "$minutes ${minutes == 1 ? 'minute' : 'minutes'}";
      }
    }
  } catch (e) {
    return AppStrings.unknown.tr(); // Return unknown if calculation fails
  }
}
