import 'dart:io';

import 'package:bus/bloc/profile_cubit/profile_cubit.dart';
import 'package:bus/bloc/update_profile_cubit/update_profile_cubit.dart';
import 'package:bus/bloc/update_profile_cubit/update_profile_states.dart';
import 'package:bus/config/theme_colors.dart';
import 'package:bus/constant/path_route_name.dart';
import 'package:bus/data/models/pickup_location_local_models/pickup_location_local_models.dart';
import 'package:bus/data/models/user_models/user_models.dart';
import 'package:bus/helper/response_state.dart';
import 'package:bus/translations/local_keys.g.dart';
import 'package:bus/utils/assets_utils.dart';
import 'package:bus/utils/sized_box.dart';
import 'package:bus/views/custom_widgets/custom_button.dart';
import 'package:bus/views/custom_widgets/custom_form_field_border.dart';
import 'package:bus/views/custom_widgets/custom_text.dart';
import 'package:bus/widgets/custom_appbar.dart';
import 'package:bus/widgets/custom_label_w.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:geocoding/geocoding.dart';
import 'package:image_picker/image_picker.dart';
import 'package:open_street_map_search_and_pick/open_street_map_search_and_pick.dart';

import '../../../widgets/pick_location_widget.dart';

class UpdateProfileScreen extends StatefulWidget {
  static const String routeName = PathRouteName.updateProfile;
  final UserModel? userModel;

  const UpdateProfileScreen({Key? key, this.userModel}) : super(key: key);

  @override
  State<UpdateProfileScreen> createState() => _UpdateProfileScreenState();
}

class _UpdateProfileScreenState extends State<UpdateProfileScreen> {
  bool securityCheck = true;
  bool isChecked = false;
  File? selectedImage;
  String? currentPassword;
  LatLong? position;
  PickedData? pickedData;
  String? addressData;
  PickupLocationLocalModels? pickupLocationLocalModels;
  final nameController = TextEditingController();
  final emailController = TextEditingController();
  final addressController = TextEditingController();
  final phoneController = TextEditingController();
  // final cityController = TextEditingController();

  Future<void> pickImage() async {
    final image = await ImagePicker().pickImage(source: ImageSource.gallery);
    if (image != null) {
      setState(() {
        selectedImage = File(image.path);
      });
    }
  }

  /// get address from lat and long
  Future<void> _getAddressFromLatLng({
    double? lat,
    double? long,
    BuildContext? context,
  }) async {
    try {
      List<Placemark>? placeMarket = await placemarkFromCoordinates(
        lat!,
        long!,
      );
      Placemark place = placeMarket[0];

      addressData =
          "${place.administrativeArea} ; ${place.locality} ; ${place.street}";
      setState(() {});
    } catch (e) {
      debugPrint("this is error $e");
    }
  }

  @override
  void initState() {
    super.initState();
    nameController.text = widget.userModel?.name ?? '';
    emailController.text = widget.userModel?.email ?? '';
    addressController.text = widget.userModel?.address ?? '';
    phoneController.text = widget.userModel?.phone ?? '';
    // cityController.text = widget.userModel?.city_name ?? '';
    position = LatLong(double.parse(widget.userModel!.latitude!),
        double.parse(widget.userModel!.longitude!));
  }

  @override
  Widget build(BuildContext context) {
    _getAddressFromLatLng(
      lat: double.parse(widget.userModel!.latitude!),
      long: double.parse(widget.userModel!.longitude!),
      context: context,
    );
    return Scaffold(
      appBar: CustomAppBar(
        rightWidget: const SizedBox(),
        titleWidget: Row(
          children: [
            CustomText(
              text: AppStrings.updateProfile.tr(),
              fontSize: 18,
              textAlign: TextAlign.center,
              fontW: FontWeight.w600,
              color: TColor.white,
            ),
            const SizedBox(width: 10.0),
          ],
        ),
        leftWidget: context.locale.toString() == "ar"
            ? InkWell(
                onTap: () {
                  Navigator.pop(context);
                },
                child: SvgPicture.asset(AppAssets.arrowBack),
              )
            : InkWell(
                onTap: () {
                  Navigator.pop(context);
                },
                child: SvgPicture.asset(
                  AppAssets.forwardArrow,
                  colorFilter:
                      const ColorFilter.mode(TColor.white, BlendMode.srcIn),
                  width: 25.w,
                  height: 25.w,
                ),
              ),
      ),
      body: SingleChildScrollView(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Center(
              child: Padding(
                padding: EdgeInsets.symmetric(horizontal: 37.w),
                child: InkWell(
                  onTap: () {
                    pickImage();
                  },
                  child: selectedImage != null
                      ? CircleAvatar(
                          radius: 34.r,
                          backgroundColor: TColor.white,
                          child: CircleAvatar(
                            backgroundImage: FileImage(selectedImage!),
                          ),
                        )
                      : Stack(
                          children: [
                            CircleAvatar(
                              radius: 35.r,
                              backgroundColor: TColor.borderContainer,
                              child: CircleAvatar(
                                radius: 34.r,
                                backgroundColor: TColor.white,
                                child: Image.asset(
                                  assetsImages("pc.png"),
                                  width: 25.w,
                                  height: 25.w,
                                ),
                              ),
                            ),
                            context.locale.toString() == "ar"
                                ? Positioned(
                                    left: 0,
                                    bottom: 5.w,
                                    child: Container(
                                      width: 20.w,
                                      height: 20.w,
                                      decoration: const BoxDecoration(
                                          color: TColor.borderContainer,
                                          shape: BoxShape.circle),
                                      child: Icon(
                                        Icons.folder_copy,
                                        color: TColor.white,
                                        size: 10.sp,
                                      ),
                                    ),
                                  )
                                : Positioned(
                                    right: 0,
                                    bottom: 5.w,
                                    child: Container(
                                      width: 20.w,
                                      height: 20.w,
                                      decoration: const BoxDecoration(
                                          color: TColor.borderContainer,
                                          shape: BoxShape.circle),
                                      child: Icon(
                                        Icons.folder_copy,
                                        color: TColor.white,
                                        size: 10.sp,
                                      ),
                                    ),
                                  )
                          ],
                        ),
                ),
              ),
            ),
            const SBox(h: 40),
            Padding(
              padding: EdgeInsets.symmetric(horizontal: 40.w),
              child: CustomLabelW(
                label: AppStrings.schoolName.tr(),
              ),
            ),
            const SBox(h: 3),
            CustomFormFieldWithBorder(
              formFieldWidth: 428,
              heightA: 53,
              hintText: AppStrings.name.tr(),
              borderColor: TColor.fillFormFieldB,
              fillColor: TColor.fillFormFieldB,
              radiusNumber: 15.0,
              paddingRight: 37.w,
              paddingLeft: 37.w,
              contentPaddingVertical: 15,
              contentPaddingHorizontal: 15,
              controller: nameController,
            ),
            const SBox(h: 10),
            Padding(
              padding: EdgeInsets.symmetric(horizontal: 40.w),
              child: CustomLabelW(
                label: AppStrings.email.tr(),
              ),
            ),
            const SBox(h: 3),
            CustomFormFieldWithBorder(
              formFieldWidth: 428,
              hintText: AppStrings.email.tr(),
              borderColor: TColor.fillFormFieldB,
              fillColor: TColor.fillFormFieldB,
              radiusNumber: 15.0,
              heightA: 53,
              paddingRight: 37.w,
              paddingLeft: 37.w,
              contentPaddingVertical: 15,
              contentPaddingHorizontal: 15,
              controller: emailController,
            ),
            const SBox(h: 10),
            Padding(
              padding: EdgeInsets.symmetric(horizontal: 40.w),
              child: CustomLabelW(
                label: AppStrings.phoneNumber.tr(),
              ),
            ),
            const SBox(h: 3),
            CustomFormFieldWithBorder(
              formFieldWidth: 428,
              heightA: 53,
              hintText: AppStrings.phoneNumber.tr(),
              borderColor: TColor.fillFormFieldB,
              fillColor: TColor.fillFormFieldB,
              radiusNumber: 15.0,
              paddingRight: 37.w,
              paddingLeft: 37.w,
              contentPaddingVertical: 15,
              contentPaddingHorizontal: 15,
              controller: phoneController,
              inputType: TextInputType.phone,
            ),
            const SBox(h: 10),
            Padding(
              padding: EdgeInsets.symmetric(horizontal: 40.w),
              child: CustomLabelW(
                label: AppStrings.address.tr(),
              ),
            ),
            const SBox(h: 3),
            CustomFormFieldWithBorder(
              formFieldWidth: 428,
              heightA: 53,
              hintText: AppStrings.address.tr(),
              borderColor: TColor.fillFormFieldB,
              fillColor: TColor.fillFormFieldB,
              radiusNumber: 15.0,
              paddingRight: 37.w,
              paddingLeft: 37.w,
              contentPaddingVertical: 15,
              contentPaddingHorizontal: 15,
              controller: addressController,
            ),
            // Padding(
            //   padding: EdgeInsets.symmetric(horizontal: 40.w),
            //   child: CustomLabelW(
            //     label: AppStrings.city.tr(),
            //   ),
            // ),
            // const SBox(h: 3),
            // CustomFormFieldWithBorder(
            //   formFieldWidth: 428,
            //   heightA: 53,
            //   hintText: AppStrings.city.tr(),
            //   borderColor: TColor.fillFormFieldB,
            //   fillColor: TColor.fillFormFieldB,
            //   radiusNumber: 15.0,
            //   paddingRight: 37.w,
            //   paddingLeft: 37.w,
            //   contentPaddingVertical: 15,
            //   contentPaddingHorizontal: 15,
            //   controller: cityController,
            // ),
            const SBox(h: 10),
            Padding(
              padding: EdgeInsets.symmetric(horizontal: 40.w),
              child: CustomLabelW(
                label: AppStrings.getLocations.tr(),
              ),
            ),
            const SBox(h: 3),
            Padding(
              padding: EdgeInsets.symmetric(horizontal: 37.w),
              child: InkWell(
                onTap: () async {
                  pickupLocationLocalModels = await Navigator.push(context,
                      MaterialPageRoute(builder: (ctx) {
                    return const PickLocationWidget();
                  }));
                  setState(() {});
                  // debugPrint(pickedData.toString());
                  position = LatLong(pickupLocationLocalModels!.lat!,
                      pickupLocationLocalModels!.long!);
                },
                child: Container(
                  width: 428.w,
                  height: 53.w,
                  decoration: BoxDecoration(
                    color: TColor.fillFormFieldB,
                    borderRadius: BorderRadius.circular(15.r),
                  ),
                  child: Padding(
                    padding: context.locale.toString() == "ar"
                        ? EdgeInsets.only(top: 15.w, right: 15.w)
                        : EdgeInsets.only(top: 15.w, left: 15.w),
                    child: CustomText(
                      text: pickupLocationLocalModels != null
                          ? pickupLocationLocalModels?.address
                          : "$addressData",
                      color: TColor.tabColors,
                      fontSize: 15,
                      fontW: FontWeight.w500,
                    ),
                  ),
                ),
              ),
            ),
            const SBox(h: 10),
            Padding(
              padding: EdgeInsets.symmetric(horizontal: 40.w),
              child: CustomLabelW(
                label: AppStrings.password.tr(),
              ),
            ),
            const SBox(h: 3),
            CustomFormFieldWithBorder(
              formFieldWidth: 428,
              heightA: 53,
              hintText: AppStrings.password.tr(),
              borderColor: TColor.fillFormFieldB,
              fillColor: TColor.fillFormFieldB,
              radiusNumber: 15.0,
              paddingRight: 37.w,
              paddingLeft: 37.w,
              onChanged: (value) {
                setState(() {
                  currentPassword = value;
                });
              },
              contentPaddingVertical: 15,
              contentPaddingHorizontal: 15,
              security: securityCheck,
              suffix: InkWell(
                onTap: () {
                  setState(() {
                    securityCheck = !securityCheck;
                  });
                },
                child: securityCheck
                    ? const Icon(
                        Icons.visibility_off,
                        color: TColor.iconInputColor,
                      )
                    : const Icon(
                        Icons.visibility_outlined,
                        color: TColor.iconInputColor,
                      ),
              ),
            ),
            const SBox(h: 50),
            BlocConsumer<UpdateProfileCubit, UpdateProfileStates>(
              listener: (context, states) {
                if (states.rStates == ResponseState.success) {
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(
                      backgroundColor: TColor.greenSuccess,
                      content: CustomText(
                        text: states.updateSchoolsModels!.status!.messages,
                        color: TColor.white,
                      ),
                    ),
                  );
                  Navigator.pop(context);
                } else if (states.rStates == ResponseState.failure) {
                  ScaffoldMessenger.of(context).showSnackBar(
                    const SnackBar(
                      backgroundColor: TColor.redAccent,
                      content: CustomText(
                        text: "error to update profile schools",
                        color: TColor.white,
                      ),
                    ),
                  );
                }
              },
              builder: (context, states) {
                if (states.rStates != ResponseState.loading) {
                  return Padding(
                    padding: EdgeInsets.symmetric(horizontal: 37.w),
                    child: CustomButton(
                      text: AppStrings.save.tr(),
                      onTap: () {
                        if (currentPassword == null) {
                          ScaffoldMessenger.of(context).showSnackBar(
                            SnackBar(
                              backgroundColor: TColor.redAccent,
                              content: CustomText(
                                text: AppStrings.validPassword.tr(),
                                color: TColor.white,
                              ),
                            ),
                          );
                        } else {
                          context
                              .read<UpdateProfileCubit>()
                              .updateProfile(
                                  name: nameController.text,
                                  address: addressController.text,
                                  email: emailController.text,
                                  currentPassword: currentPassword,
                                  cityName: "D",
                                  phone: phoneController.text,
                                  position: position ??
                                      LatLong(
                                          double.parse(
                                              widget.userModel!.latitude!),
                                          double.parse(
                                              widget.userModel!.longitude!)),
                                  image: selectedImage)
                              .whenComplete(
                            () {
                              context.read<ProfileCubit>().getProfile();
                            },
                          );
                        }
                      },
                      width: 428,
                      height: 53,
                      radius: 15,
                      borderColor: TColor.mainColor,
                      bgColor: TColor.mainColor,
                    ),
                  );
                } else {
                  return const Center(
                    child: CircularProgressIndicator(
                      color: TColor.mainColor,
                    ),
                  );
                }
              },
            ),
            const SBox(h: 20),
          ],
        ),
      ),
    );
  }
}
