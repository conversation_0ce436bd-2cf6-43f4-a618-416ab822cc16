import 'package:bus/bloc/add_bus_cubit/add_bus_cubit.dart';
import 'package:bus/bloc/add_bus_cubit/add_bus_states.dart';
import 'package:bus/bloc/buses_cubit/buses_cubit.dart';
import 'package:bus/bloc/update_bus_cubit/update_bus_cubit.dart';
import 'package:bus/bloc/update_bus_cubit/update_bus_states.dart';
import 'package:bus/config/theme_colors.dart';
import 'package:bus/constant/path_route_name.dart';
import 'package:bus/data/models/buses_models/buses_info_models.dart';
import 'package:bus/translations/local_keys.g.dart';
import 'package:bus/utils/assets_utils.dart';
import 'package:bus/utils/sized_box.dart';
import 'package:bus/views/custom_widgets/custom_button.dart';
import 'package:bus/views/custom_widgets/custom_text.dart';
import 'package:bus/widgets/custom_appbar.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';

class AddBusScreen extends StatefulWidget {
  static const String routeName = PathRouteName.addBus;
  final BusesInfoModel? editBus;
  final bool isEdit;

  const AddBusScreen({super.key, required this.isEdit, this.editBus});

  @override
  State<AddBusScreen> createState() => _AddBusScreenState();
}

class _AddBusScreenState extends State<AddBusScreen> {
  String car_number = "";
  String notes = '';
  String name = "";
  final _formKey = GlobalKey<FormState>();
  TextEditingController? txcBusname;
  TextEditingController? txcBusnotes;
  TextEditingController? txcBustnumber;

  Widget _buildTextFormField({
    required bool? validation,
    required String title,
    required TextEditingController? controller,
    required String hintText,
    required Function(String) onChanged,
  }) {
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: 37.w),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            title,
            style: const TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.w500,
              color: Colors.black87,
            ),
          ),
          const SizedBox(height: 8),
          SizedBox(
            height: 53,
            child: TextFormField(
              controller: controller,
              decoration: InputDecoration(
                hintText: hintText,
                filled: true,
                fillColor: TColor.fillFormFieldB,
                contentPadding: const EdgeInsets.symmetric(
                  vertical: 15,
                  horizontal: 15,
                ),
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(15.0),
                  borderSide: const BorderSide(color: TColor.fillFormFieldB),
                ),
                enabledBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(15.0),
                  borderSide: const BorderSide(color: TColor.fillFormFieldB),
                ),
                focusedBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(15.0),
                  borderSide: const BorderSide(color: TColor.mainColor),
                ),
              ),
              validator: validation == true ? (value) {
                if (value == null || value.isEmpty) {
                  return AppStrings.required.tr();
                }
                return null;
              }: null,
              onChanged: onChanged,
            ),
          ),
        ],
      ),
    );
  }

  @override
  void initState() {
    try {
      if (widget.isEdit) {
        debugPrint("IsEdit");
        txcBusname = TextEditingController(text: widget.editBus?.name ?? "");
        txcBusnotes = TextEditingController(text: widget.editBus?.notes ?? "");
        txcBustnumber = TextEditingController(text: widget.editBus?.car_number ?? "");
        name = txcBusname?.text ?? "";
        notes = txcBusnotes?.text ?? "";
        car_number = txcBustnumber?.text ?? "";
      } else {
        txcBusname = TextEditingController();
        txcBusnotes = TextEditingController();
        txcBustnumber = TextEditingController();
      }
    } catch (e, s) {
      debugPrint("catch error $s");
      debugPrint(e.toString());
    }
    super.initState();
  }

  @override
  void dispose() {
    txcBusname?.dispose();
    txcBusnotes?.dispose();
    txcBustnumber?.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: CustomAppBar(
        rightWidget: const SizedBox(),
        titleWidget: Row(
          children: [
            CustomText(
              text: widget.isEdit ? AppStrings.editBus.tr() : AppStrings.addBus.tr(),
              fontSize: 18,
              textAlign: TextAlign.center,
              fontW: FontWeight.w600,
              color: TColor.white,
            ),
            const SizedBox(width: 10.0),
          ],
        ),
        leftWidget: context.locale.toString() == "ar"
            ? InkWell(
                onTap: () => Navigator.pop(context),
                child: SvgPicture.asset(AppAssets.arrowBack),
              )
            : InkWell(
                onTap: () => Navigator.pop(context),
                child: SvgPicture.asset(
                  AppAssets.forwardArrow,
                  colorFilter: const ColorFilter.mode(TColor.white, BlendMode.srcIn),
                  width: 25.w,
                  height: 25.w,
                ),
              ),
      ),
      body: SingleChildScrollView(
        child: SizedBox(
          height: 680.w,
          width: 1.sw,
          child: Column(
            children: [
              Form(
                key: _formKey,
                child: Column(
                  children: [
                    _buildTextFormField(
                      validation: true,
                      title: AppStrings.busName.tr(),
                      controller: txcBusname,
                      hintText: AppStrings.busName.tr(),
                      onChanged: (value) {
                        name = value;
                      },
                    ),
                    const SBox(h: 15),
                    _buildTextFormField(
                      validation: true,
                      title: AppStrings.busNumber.tr(),
                      controller: txcBustnumber,
                      hintText: AppStrings.busNumber.tr(),
                      onChanged: (value) {
                        try {
                          car_number = value;
                        } catch (e) {
                          if (kDebugMode) {
                            debugPrint(e.toString());
                          }
                          ScaffoldMessenger.of(context).showSnackBar(
                            const SnackBar(
                              backgroundColor: TColor.redAccent,
                              content: CustomText(
                                text: "Invalid number",
                                fontSize: 18,
                                maxLine: 5,
                                color: TColor.white,
                              ),
                            ),
                          );
                        }
                      },
                    ),
                    const SBox(h: 15),
                    _buildTextFormField(
                      validation: false,
                      title: AppStrings.notes.tr(),
                      controller: txcBusnotes,
                      hintText: AppStrings.notes.tr(),
                      onChanged: (value) => notes = value,
                    ),
                  ],
                ),
              ),
              widget.isEdit
                  ? BlocConsumer<UpdateBusCubit, UpdateBusStates>(
                      listener: (context, states) {
                        if (states is UpdateBusSuccessStates) {
                          Navigator.pop(context);
                          ScaffoldMessenger.of(context).showSnackBar(
                            SnackBar(
                              backgroundColor: TColor.greenSuccess,
                              content: CustomText(
                                text: states.updateBusModels!.message,
                                fontSize: 18,
                                maxLine: 5,
                                color: TColor.white,
                              ),
                            ),
                          );
                        } else if (states is UpdateBusErrorStates) {
                          Navigator.pop(context);
                          ScaffoldMessenger.of(context).showSnackBar(
                            SnackBar(
                              backgroundColor: TColor.redAccent,
                              content: CustomText(
                                text: states.error,
                                fontSize: 18,
                                maxLine: 5,
                                color: TColor.white,
                              ),
                            ),
                          );
                        }
                      },
                      builder: (context, states) {
                        if (states is! UpdateBusLoadingStates) {
                          return Expanded(
                            child: Align(
                              alignment: Alignment.bottomCenter,
                              child: Padding(
                                padding: EdgeInsets.symmetric(horizontal: 37.w),
                                child: CustomButton(
                                  text: AppStrings.save.tr(),
                                  onTap: () async {
                                    if (_formKey.currentState!.validate()) {
                                      _formKey.currentState!.save();
                                      context
                                          .read<UpdateBusCubit>()
                                          .updateBus(
                                            busId: widget.editBus?.id.toString(),
                                            name: name,
                                            notes: notes,
                                            car_number: car_number,
                                          );
                                    }
                                  },
                                  width: 428,
                                  height: 53,
                                  radius: 15,
                                  borderColor: TColor.mainColor,
                                  bgColor: TColor.mainColor,
                                ),
                              ),
                            ),
                          );
                        } else {
                          return Expanded(
                            child: Align(
                              alignment: Alignment.bottomCenter,
                              child: SizedBox(
                                width: 30.w,
                                height: 30.w,
                                child: const CircularProgressIndicator(
                                  color: TColor.mainColor,
                                ),
                              ),
                            ),
                          );
                        }
                      },
                    )
                  : BlocConsumer<AddBusCubit, AddBusStates>(
                      listener: (context, states) {
                        if (states is AddBusSuccessStates) {
                          BlocProvider.of<BusesCubit>(context)
                              .getBuses(pageNumber: 1, isFirst: true);
                          Navigator.pop(context);
                          ScaffoldMessenger.of(context).showSnackBar(
                            SnackBar(
                              backgroundColor: TColor.greenSuccess,
                              content: CustomText(
                                text: states.addBusModels!.message,
                                fontSize: 18,
                                maxLine: 5,
                                color: TColor.white,
                              ),
                            ),
                          );
                        } else if (states is AddBusErrorStates) {
                          Navigator.pop(context);
                          ScaffoldMessenger.of(context).showSnackBar(
                            SnackBar(
                              backgroundColor: TColor.redAccent,
                              content: CustomText(
                                text: states.error,
                                fontSize: 18,
                                maxLine: 5,
                                color: TColor.white,
                              ),
                            ),
                          );
                        }
                      },
                      builder: (context, states) {
                        if (states is! AddBusLoadingStates) {
                          return Expanded(
                            child: Align(
                              alignment: Alignment.bottomCenter,
                              child: Padding(
                                padding: EdgeInsets.symmetric(horizontal: 37.w),
                                child: CustomButton(
                                  text: AppStrings.add.tr(),
                                  onTap: () async {
                                    if (_formKey.currentState!.validate()) {
                                      _formKey.currentState!.save();
                                      context.read<AddBusCubit>().addBus(
                                            name: name,
                                            notes: notes,
                                            car_number: car_number,
                                          );
                                    }
                                  },
                                  width: 428,
                                  height: 53,
                                  radius: 15,
                                  borderColor: TColor.mainColor,
                                  bgColor: TColor.mainColor,
                                ),
                              ),
                            ),
                          );
                        } else {
                          return Expanded(
                            child: Align(
                              alignment: Alignment.bottomCenter,
                              child: SizedBox(
                                width: 30.w,
                                height: 30.w,
                                child: const CircularProgressIndicator(
                                  color: TColor.mainColor,
                                ),
                              ),
                            ),
                          );
                        }
                      },
                    ),
              const SBox(h: 15),
            ],
          ),
        ),
      ),
    );
  }
}