import 'package:bus/config/theme_colors.dart';
import 'package:bus/constant/path_route_name.dart';
import 'package:bus/translations/local_keys.g.dart';
import 'package:bus/utils/assets_utils.dart';
import 'package:bus/utils/sized_box.dart';
import 'package:bus/views/custom_widgets/custom_text.dart';
import 'package:bus/widgets/custom_appbar.dart';
import 'package:bus/widgets/student_data_widget/custom_student_c_w.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';

import '../../../data/models/supervisor_mdodels/supervisor_info_models.dart';


class SupervisorDataScreen extends StatelessWidget {
  static const String routeName = PathRouteName.supervisorData;

  const SupervisorDataScreen({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final args =
        ModalRoute.of(context)!.settings.arguments as SupervisorInfoModels;
    debugPrint("args: $args");
    return Scaffold(
      appBar: CustomAppBar(
        titleWidget: CustomText(
          text: AppStrings.SupervisorData.tr(),
          fontSize: 18,
          textAlign: TextAlign.center,
          fontW: FontWeight.w600,
          color: TColor.white,
        ),
        leftWidget: context.locale.toString() == "ar"
            ? InkWell(
                onTap: () {
                  Navigator.pop(context);
                },
                child: SvgPicture.asset(AppAssets.arrowBack),
              )
            : InkWell(
                onTap: () {
                  Navigator.pop(context);
                },
                child: SvgPicture.asset(
                  AppAssets.forwardArrow,
                  colorFilter: const ColorFilter.mode(TColor.white, BlendMode.srcIn),
                  width: 25.w,
                  height: 25.w,
                ),
              ),
      ),
      body: SingleChildScrollView(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            Padding(
              padding: EdgeInsets.symmetric(horizontal: 37.w),
              child: CircleAvatar(
                radius: 50.r,
                backgroundColor: TColor.borderContainer,
                child: CircleAvatar(
                  radius: 49.r,
                  backgroundColor: TColor.white,
                  backgroundImage: NetworkImage(args.logo_path ??
                      "https://www.nj.com/resizer/iqV2J-QFgh0227ybHBor4exTVBk=/800x0/smart/cloudfront-us-east-1.images.arcpublishing.com/advancelocal/SJGKVE5UNVESVCW7BBOHKQCZVE.jpg"),
                ),
              ),
            ),
            const SBox(h: 30),
            Padding(
              padding: EdgeInsets.symmetric(horizontal: 37.w),
              child: Column(
                children: [
                  CustomStudentCW(
                    name: "${args.name}",
                    label: AppStrings.name.tr(),
                    isLabel: true,
                  ),
                  const SBox(h: 20),
                  CustomStudentCW(
                    name: args.username,
                    label: AppStrings.username.tr(),
                    isLabel: true,
                  ),
                  const SBox(h: 20),
                  CustomStudentCW(
                    name: args.bus?.name ?? AppStrings.notFound.tr(),
                    label: AppStrings.busName.tr(),
                    isLabel: true,
                  ),
                  const SBox(h: 20),
                  CustomStudentCW(
                    name: "${args.address}",
                    label: AppStrings.address.tr(),
                    isLabel: true,
                  ),
                  const SBox(h: 20),
                  CustomStudentCW(
                    name: "${args.phone}",
                    label: AppStrings.phone.tr(),
                    isLabel: true,
                  ),
                  const SBox(h: 20),
                ],
              ),
            )
          ],
        ),
      ),
    );
  }
}
