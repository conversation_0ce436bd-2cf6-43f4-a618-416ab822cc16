import 'package:bus/bloc/buses_cubit/buses_cubit.dart';
import 'package:bus/bloc/buses_cubit/buses_states.dart';
import 'package:bus/bloc/delete_bus_cubit/delete_bus_cubit.dart';
import 'package:bus/bloc/student_bus_cubit/student_bus_cubit.dart';
import 'package:bus/utils/helpers.dart';
import 'package:bus/views/screens/add_bus_screen/add_bus_screen.dart';
import 'package:bus/views/screens/add_student_tobus_screen/student_tobus_screen.dart';
import 'package:bus/views/screens/bus_data_screen/bus_data_screen.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';

import '../../../bloc/student_cubit/student_cubit.dart';
import '../../../config/theme_colors.dart';
import '../../../constant/path_route_name.dart';
import 'package:bus/translations/local_keys.g.dart';import '../../../utils/assets_utils.dart';
import '../../../widgets/custom_appbar.dart';
import '../../../widgets/student_widgets/custom_container_dialog_w.dart';
import '../../../widgets/student_widgets/custom_search_w.dart';
import '../../custom_widgets/build_table_row_widget.dart';
import '../../custom_widgets/custom_text.dart';
import '../bus_students_screen/bus_students_screen.dart';

class AllBuScreen extends StatefulWidget {
  static const String routeName = PathRouteName.allBus;

  const AllBuScreen({Key? key}) : super(key: key);

  @override
  State<AllBuScreen> createState() => _AllBuScreenState();
}

class _AllBuScreenState extends State<AllBuScreen> {
  final ScrollController? scrollController = ScrollController();

  @override
  void initState() {
    context.read<BusesCubit>().initScrollController(
        isFirst: false,
        scrollController: scrollController,
        setStates: () {
          setState(() {});
        });
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: CustomAppBar(
        titleWidget: Center(
          child: CustomText(
            text: AppStrings.allBus.tr(),
            fontSize: 18,
            textAlign: TextAlign.center,
            fontW: FontWeight.w600,
            color: TColor.white,
          ),
        ),
        leftWidget: context.locale.toString() == "ar"
            ? InkWell(
                onTap: () {
                  Navigator.pop(context);
                },
                child: SvgPicture.asset(AppAssets.arrowBack),
              )
            : InkWell(
                onTap: () {
                  Navigator.pop(context);
                },
                child: SvgPicture.asset(
                  AppAssets.forwardArrow,
                  colorFilter:
                      const ColorFilter.mode(TColor.white, BlendMode.srcIn),
                  width: 25.w,
                  height: 25.w,
                ),
              ),
      ),
      body: SingleChildScrollView(
        controller: scrollController,
        child: Column(
          children: [
            CustomSearchW(
              hintText: AppStrings.searchBusHint.tr(),
              type: "Bus",
              isSearch: false,
            ),
            BlocBuilder<BusesCubit, BusesState>(
              buildWhen: (previous, current) =>
                  current is BusesPaginationSuccessStates,
              builder: (context, states) {
                if (states is BusesLoadingStates) {
                  return const Center(
                    child: CircularProgressIndicator(
                      color: TColor.mainColor,
                    ),
                  );
                } else if (states is BusesPaginationSuccessStates) {
                  if (context.watch<BusesCubit>().data==[]) {
                    return SizedBox(
                      width: 300.w,
                      height: 300.w,
                      child: Center(
                        child: CustomText(
                          text: AppStrings.busesNotFound.tr(),
                          fontSize: 17,
                          fontW: FontWeight.w600,
                        ),
                      ),
                    );
                  } else {
                    return Column(
                      children: [
                        Padding(
                          padding: const EdgeInsets.all(8.0),
                          child: Container(
                            margin: const EdgeInsets.symmetric(horizontal: 8.0),
                            clipBehavior: Clip.antiAlias,
                            decoration: BoxDecoration(
                              borderRadius: BorderRadius.circular(15.0),
                            ),
                            child: Table(
                              columnWidths: const {
                                0: FlexColumnWidth(2.5),
                                1: FlexColumnWidth(2),
                                2: FlexColumnWidth(1),
                              },
                              border: TableBorder.all(
                                  color: TColor.tabColors,
                                  borderRadius: BorderRadius.circular(15.0)),
                              children: List.generate(
                                  1 + context.watch<BusesCubit>().data.length,
                                  (index) {
                                if (index == 0) {
                                  return BuildTableRowWidget(
                                    cell: [
                                      AppStrings.line.tr(),
                                      AppStrings.busNumber.tr(),
                                      AppStrings.options.tr(),
                                    ],
                                    header: true,
                                  ).build(context);
                                } else {
                                  // debugPrint(states.busesInfoModels);
                                  final newBuses = context
                                      .watch<BusesCubit>()
                                      .data[index - 1];
                                  return BuildTableRowWidget(
                                    isTabDown: true,
                                    cell: [
                                      newBuses.name,
                                      newBuses.car_number,
                                      Icons.more_horiz,
                                    ],
                                    onTapDown: (position) {
                                      Helpers.customBusShowDialog(context,
                                          position: position.globalPosition,
                                          onTapShow: () {
                                        Navigator.of(context)
                                          ..pop()
                                          ..push(
                                              MaterialPageRoute(builder: (ctx) {
                                            return BusDataScreen(
                                                businfoModeldata: newBuses);
                                          }));
                                      }, onTapDelete: () {
                                        Navigator.pop(context);
                                        context
                                            .read<DeleteBusCubit>()
                                            .deleteBus(id: newBuses.id)
                                            .whenComplete(() {
                                          context.read<BusesCubit>().getBuses(
                                              pageNumber: 1, isFirst: true);
                                        });
                                      }, onTapEdit: () async {
                                        Navigator.pop(context);
                                        final busesCubit = context.read<BusesCubit>();
                                        await Navigator.push(context,
                                            MaterialPageRoute(builder: (ctx) {
                                          return AddBusScreen(
                                            isEdit: true,
                                            editBus: newBuses,
                                          );
                                        }));
                                        // تحديث قائمة الباصات عند العودة من شاشة التعديل
                                        if (mounted) {
                                          busesCubit.getBuses(
                                              pageNumber: 1, isFirst: true);
                                        }
                                      }, onTapAddToBus: () {
                                        context
                                            .read<StudentBusCubit>()
                                            .getStudent(
                                              pageNumber: 1,
                                            );
                                        Navigator.of(context)
                                          ..pop()
                                          ..push(
                                              MaterialPageRoute(builder: (ctx) {
                                            return StudentToBusScreen(
                                              // checkTest: true,
                                              busId: newBuses.id.toString(),
                                              // busName: newBuses.name,
                                            );
                                          }));
                                      }, items: [
                                        PopupMenuItem(
                                          child: Center(
                                            child: CustomContainerDialogW(
                                              icons: Icons.people,
                                              name: AppStrings.showStudent.tr(),
                                              onTap: () {
                                                context
                                                    .read<StudentCubit>()
                                                    .getStudentWithBusId(
                                                        busId: newBuses.id);
                                                Navigator.of(context)
                                                  ..pop()
                                                  ..push(MaterialPageRoute(
                                                      builder: (ctx) {
                                                    return BusStudentsScreen(
                                                      busName:
                                                          newBuses.name ?? '',
                                                      id: newBuses.id,
                                                    );
                                                  }));
                                              },
                                            ),
                                          ),
                                        ),
                                      ]);
                                    },
                                  ).build(context);
                                }
                              }),
                            ),
                          ),
                        ),
                        10.verticalSpace,
                        context.watch<BusesCubit>().currentPage! <
                                context.watch<BusesCubit>().last_pages!
                            ? Icon(
                                Icons.arrow_downward_rounded,
                                size: 40.sp,
                              )
                            : const SizedBox.shrink(),
                        50.verticalSpace
                      ],
                    );
                  }
                } else if (states is BusesErrorStates) {
                  if (states.error == "buses not found") {
                    return SizedBox(
                      width: 300.w,
                      height: 300.w,
                      child: Center(
                        child: CustomText(
                          text: AppStrings.busesNotFound.tr(),
                          fontSize: 17,
                          fontW: FontWeight.w600,
                        ),
                      ),
                    );
                  } else {
                    return SizedBox(
                      width: 300.w,
                      height: 300.w,
                      child: Center(
                        child: CustomText(
                          text: AppStrings.busesNotFound.tr(),
                          fontSize: 17,
                          fontW: FontWeight.w600,
                        ),
                      ),
                    );
                  }
                } else {
                  return const SizedBox();
                }
              },
            ),
          ],
        ),
      ),
    );
  }
}
