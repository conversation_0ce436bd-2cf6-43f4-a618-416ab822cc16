import 'package:bus/config/theme_colors.dart';
import 'package:bus/constant/path_route_name.dart';
import 'package:bus/translations/local_keys.g.dart';
import 'package:bus/utils/assets_utils.dart';
import 'package:bus/widgets/custom_appbar.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:file_picker/file_picker.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';

import '../../../utils/sized_box.dart';
import '../../custom_widgets/custom_button.dart';
import '../../custom_widgets/custom_text.dart';

class AddStudentToBusFromFileScreen extends StatefulWidget {
  static const String routeName = PathRouteName.addStudentToBusFromFileScreen;

  const AddStudentToBusFromFileScreen({Key? key}) : super(key: key);

  @override
  State<AddStudentToBusFromFileScreen> createState() =>
      _AddStudentToBusFromFileScreenState();
}

class _AddStudentToBusFromFileScreenState
    extends State<AddStudentToBusFromFileScreen> {
  FilePickerResult? result;
  String? file;

  Future getFile() async {
    final result1 = await FilePicker.platform.pickFiles();
    if (result1 != null) {
      setState(() {
        result = result1;
        file = result1.files.single.path!.split("/").last;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: CustomAppBar(
        titleWidget: CustomText(
          text: AppStrings.addStudentToBusFromFile.tr(),
          color: TColor.white,
          fontSize: 18,
          textAlign: TextAlign.center,
          fontW: FontWeight.w600,
        ),
        rightWidget: InkWell(
          onTap: () {
            Navigator.pop(context);
          },
          child: SvgPicture.asset(AppAssets.arrowBack),
        ),
      ),
      body: SingleChildScrollView(
        child: SizedBox(
          height: 620.w,
          width: 1.sw,
          child: Column(
            children: [
              Padding(
                padding: EdgeInsets.symmetric(horizontal: 37.w),
                child: Container(
                  width: 428.w,
                  height: 45.w,
                  decoration: BoxDecoration(
                    color: Colors.grey[300],
                    borderRadius: context.locale.toString() == "ar"
                        ? BorderRadius.only(
                            topLeft: Radius.circular(10.r),
                            topRight: Radius.circular(10.r),
                            bottomLeft: Radius.circular(10.r))
                        : BorderRadius.only(
                            topLeft: Radius.circular(10.r),
                            topRight: Radius.circular(10.r),
                            bottomRight: Radius.circular(10.r)),
                    border: Border.all(width: 1.w, color: TColor.fillFormFieldB),
                    boxShadow: const [
                      BoxShadow(
                        color: TColor.textForm,
                        offset: Offset(3, 1),
                        blurRadius: 6,
                      ),
                    ],
                  ),
                  child: Row(
                    children: [
                      Container(
                        width: 110.w,
                        height: 45.w,
                        decoration: BoxDecoration(
                          color: TColor.borderContainer,
                          borderRadius: context.locale.toString() == "ar"
                              ? BorderRadius.only(
                                  topRight: Radius.circular(10.r),
                                  bottomLeft: Radius.circular(10.r))
                              : BorderRadius.only(
                                  topLeft: Radius.circular(10.r),
                                  bottomRight: Radius.circular(10.r)),
                          border: Border.all(
                            width: 1.w,
                            color: TColor.borderContainer,
                          ),
                        ),
                        child: InkWell(
                          onTap: () {
                            getFile();
                          },
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              Icon(
                                Icons.drive_folder_upload,
                                color: TColor.white,
                                size: 20.sp,
                              ),
                              const SBox(w: 10),
                              CustomText(
                                text: AppStrings.addFile.tr(),
                                color: TColor.white,
                                fontSize: 13,
                                fontW: FontWeight.w400,
                              ),
                            ],
                          ),
                        ),
                      ),
                      const SBox(w: 20),
                      CustomText(
                        text: file ?? "",
                        color: TColor.dialogName,
                        fontW: FontWeight.w400,
                        fontSize: 13,
                      ),
                    ],
                  ),
                ),
              ),
              Expanded(
                child: Align(
                  alignment: Alignment.bottomCenter,
                  child: Padding(
                    padding: EdgeInsets.symmetric(horizontal: 37.w),
                    child: CustomButton(
                      text: AppStrings.add.tr(),
                      onTap: () {},
                      width: 428,
                      height: 53,
                      radius: 15,
                      borderColor: TColor.mainColor,
                      bgColor: TColor.mainColor,
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
