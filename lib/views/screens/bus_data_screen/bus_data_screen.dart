import 'dart:async';

import 'package:bus/bloc/bus_location_tracker_cubit/bus_location_tracker_cubit.dart';
import 'package:bus/bloc/bus_location_tracker_cubit/bus_location_tracker_states.dart';
import 'package:bus/config/theme_colors.dart';
import 'package:bus/constant/path_route_name.dart';
import 'package:bus/data/models/buses_models/buses_info_models.dart';
import 'package:bus/translations/local_keys.g.dart';
import 'package:bus/utils/assets_utils.dart';
import 'package:bus/utils/sized_box.dart';
import 'package:bus/views/custom_widgets/custom_text.dart';
import 'package:bus/widgets/custom_appbar.dart';
import 'package:bus/widgets/student_data_widget/custom_student_c_w.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';

class BusDataScreen extends StatefulWidget {
  static const String routeName = PathRouteName.busDataScreen;
  final BusesInfoModel businfoModeldata;
  BusDataScreen({Key? key, required this.businfoModeldata}) : super(key: key);

  @override
  State<BusDataScreen> createState() => _BusDataScreenState();
}

class _BusDataScreenState extends State<BusDataScreen> {
  Stream? loader;
  StreamSubscription? supscription;
  bool isNoTrips = true;
  @override
  void initState() {
    super.initState();
    loadLocation();
    loader = Stream.periodic(
      const Duration(seconds: 15),
      (computationCount) async {
        if (isNoTrips) {
          supscription?.pause();
          return;
        }
        await loadLocation();
      },
    );
    supscription = loader?.listen((event) {
      debugPrint(event.toString());
    });
    debugPrint(
        '*--------------------------------------------------${widget.businfoModeldata.supervisor?.name}');
  }

  @override
  void dispose() {
    supscription!.pause();
    supscription?.cancel();
    isNoTrips = true;

    loader = null;
    super.dispose();
  }

  loadLocation() async {
    await BlocProvider.of<BusLocationTrackerCubit>(context)
        .getBusLocationTracker(busid: widget.businfoModeldata.id);
    if (isNoTrips) {
    } else {
      setState(() {});
    }
  }

  @override
  Widget build(BuildContext context) {
    //final args = ModalRoute.of(context)!.settings.arguments as BusesInfoModels;
    List<Widget> studentsWidgets = [];
    int studentsLenght =
        ((widget.businfoModeldata.students?.data?.data?.length) ?? 0);
    debugPrint(widget.businfoModeldata.students?.data?.data![0].name);

    if (studentsLenght > 0) {
      for (int i = 0; i < studentsLenght; i++) {
        studentsWidgets.add(
            Text(widget.businfoModeldata.students?.data?.data![i].name! ?? ""));
      }
    }
    Widget? admins;
    Widget? driver;

    if (widget.businfoModeldata.driver != null) {
      driver = CustomStudentCW(
        name: widget.businfoModeldata.driver?.name ?? "",
        label: AppStrings.driver.tr(),
        isLabel: true,
      );
    }
    if (widget.businfoModeldata.admin != null) {
      admins = CustomStudentCW(
        name: widget.businfoModeldata.admin?.name ?? "",
        label: AppStrings.supervisor.tr(),
        isLabel: true,
      );
      // if (kDebugMode) {
      //   debugPrint(widget.businfoModeldata.supervisor?.name);
      // }
    }

    return Scaffold(
      appBar: CustomAppBar(
        rightWidget: const SizedBox(),
        titleWidget: const Row(
          children: [
            CustomText(
              text: "Bus data",
              fontSize: 18,
              textAlign: TextAlign.center,
              fontW: FontWeight.w600,
              color: TColor.white,
            ),
            SizedBox(width: 10.0),
          ],
        ),
        leftWidget: context.locale.toString() == "ar"
            ? InkWell(
                onTap: () {
                  Navigator.pop(context);
                },
                child: SvgPicture.asset(AppAssets.arrowBack),
              )
            : InkWell(
                onTap: () {
                  Navigator.pop(context);
                },
                child: SvgPicture.asset(
                  AppAssets.forwardArrow,
                  colorFilter:
                      const ColorFilter.mode(TColor.white, BlendMode.srcIn),
                  width: 25.w,
                  height: 25.w,
                ),
              ),
      ),
      body: SingleChildScrollView(
        child: SafeArea(
          child: SizedBox(
            height: 1.2.sh,
            width: 1.sw,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const SBox(h: 30),
                Padding(
                  padding: EdgeInsets.symmetric(horizontal: 37.w),
                  child: Column(
                    children: [
                      CustomStudentCW(
                        name: "${widget.businfoModeldata.name}",
                        label: AppStrings.name.tr(),
                        isLabel: true,
                      ),
                      const SBox(h: 20),
                      CustomStudentCW(
                        name: "${widget.businfoModeldata.car_number}",
                        label: AppStrings.carNumber.tr(),
                        isLabel: true,
                      ),
                      const SBox(h: 20),
                      CustomStudentCW(
                        name: "${widget.businfoModeldata.notes ?? ''}",
                        label: AppStrings.notes.tr(),
                        isLabel: true,
                      ),
                      // SingleChildScrollView(
                      //   child: Row(children: studentsWidgets),
                      // ),
                      // const SBox(h: 20),
                      //  CustomStudentCW(
                      //   name: "${widget.businfoModeldata.name??''}",
                      //   label: AppStrings.notes.tr(),
                      //   isLabel: true,
                      // ),
                      const SBox(h: 20),

                      admins ?? const SizedBox(),
                      const SBox(h: 20),
                      driver ?? const SizedBox(),

                      // CustomStudentCW(
                      //   name: widget.businfoModeldata.supervisor?.name ?? AppStrings.notFound.tr(),
                      //   label: AppStrings.supervisor.tr(),
                      //   isLabel: true,
                      // ),
                      //                 const SBox(h: 20),
                      //                  CustomStudentCW(
                      //   name: widget.businfoModeldata.attendant_driver?.name ?? AppStrings.notFound.tr(),
                      //   label: AppStrings.driver.tr(),
                      //   isLabel: true,
                      // ),
                      const SBox(h: 20),
                      BlocBuilder<BusLocationTrackerCubit,
                          BusLocationTrackerStates>(
                        builder: (context, state) {
                          // if (state is BusLocationTrackerLoadingStates) {
                          //   if (lastLong != null) {
                          //     final markers = Marker(
                          //       width: 30,
                          //       height: 30,
                          //       point: lastLong!,
                          //       builder: (ctx) => Image.asset(
                          //         assetsImages("bus.png"),
                          //       ),
                          //     );
                          //
                          //     return SizedBox(
                          //       width: 500.w,
                          //       height: 400.w,
                          //       child: FlutterMap(
                          //         options: MapOptions(
                          //             center: lastLong, zoom: 13, maxZoom: 18),
                          //         children: [
                          //           TileLayer(
                          //             // zoomOffset: 1,
                          //             maxZoom: 18,
                          //             minZoom: 5,
                          //             urlTemplate:
                          //                 'https://tile.openstreetmap.org/{z}/{x}/{y}.png',
                          //             // userAgentPackageName:
                          //             //     'dev.fleaflet.flutter_map.example',
                          //           ),
                          //           MarkerLayer(markers: [markers]),
                          //         ],
                          //       ),
                          //     );
                          //   } else {
                          //     return const Center(
                          //         child: CircularProgressIndicator());
                          //   }
                          // } else if (state is BusLocationTrackerSuccessStates) {
                          //   isNoTrips = false;
                          //   var long = LatLng(
                          //       state.BusLocationTrackerModels!.latitude,
                          //       state.BusLocationTrackerModels!.longitude);
                          //   lastLong = long;
                          //   final markers = Marker(
                          //     width: 30,
                          //     height: 30,
                          //     point: long,
                          //     builder: (ctx) => Image.asset(
                          //       assetsImages("bus.png"),
                          //     ),
                          //   );
                          //
                          //   return SizedBox(
                          //     width: 500.w,
                          //     height: 400.w,
                          //     child: FlutterMap(
                          //       options: MapOptions(
                          //           center: long, zoom: 13, maxZoom: 18),
                          //       children: [
                          //         TileLayer(
                          //           // zoomOffset: 1,
                          //           maxZoom: 18,
                          //           minZoom: 5,
                          //           urlTemplate:
                          //               'https://tile.openstreetmap.org/{z}/{x}/{y}.png',
                          //           // userAgentPackageName:
                          //           //     'dev.fleaflet.flutter_map.example',
                          //         ),
                          //         MarkerLayer(markers: [markers]),
                          //       ],
                          //     ),
                          //   );
                          // } else if (state is BusLocationTrackerErrorStates) {
                          //   isNoTrips = true;
                          //   return const CustomText(
                          //       text: "No Trips", fontSize: 30);
                          // } else {
                          //   isNoTrips = true;
                          return const CustomText(
                              text: "No Trips", fontSize: 30);
                          // }
                        },
                      )
                    ],
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
