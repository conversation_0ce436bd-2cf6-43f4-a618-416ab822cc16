import 'dart:io';

import 'package:bus/bloc/student_cubit/student_cubit.dart';
import 'package:bus/bloc/student_cubit/student_states.dart';
import 'package:bus/config/theme_colors.dart';
import 'package:bus/constant/path_route_name.dart';
import 'package:bus/utils/assets_utils.dart';
import 'package:bus/utils/sized_box.dart';
import 'package:bus/views/custom_widgets/custom_text.dart';
import 'package:bus/widgets/custom_appbar.dart';
import 'package:bus/widgets/student_widgets/custom_table_w.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:open_file/open_file.dart';
import 'package:path_provider/path_provider.dart';
import 'package:syncfusion_flutter_xlsio/xlsio.dart' as syncfusion;

import '../../../data/models/student_models/student_model.dart';
import '../../../data/repo/download_students_file_repo.dart';
import 'package:bus/translations/local_keys.g.dart';import '../../custom_widgets/group_floating_action_button_widget.dart';
import '../../custom_widgets/student_floating_action_widget.dart';

class BusStudentsScreen extends StatefulWidget {
  final String busName;
  final int? id;
  final bool isFromParents;
  static const String routeName = PathRouteName.student;

  const BusStudentsScreen(
      {Key? key,
      required this.busName,
      required this.id,
      this.isFromParents = false})
      : super(key: key);

  @override
  State<BusStudentsScreen> createState() => _BusStudentsScreenState();
}

class _BusStudentsScreenState extends State<BusStudentsScreen>
    with TickerProviderStateMixin {
  AnimationController? controller;
  AnimationController? controllerIcon;
  Animation<double>? opacity;

  @override
  void initState() {
    super.initState();
    controller = AnimationController(
      duration: const Duration(milliseconds: 200),
      vsync: this,
    );
    controllerIcon = AnimationController(
      duration: const Duration(milliseconds: 500),
      vsync: this,
    );

    opacity = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(controller!);
  }

  @override
  void dispose() {
    controller?.dispose();
    controllerIcon?.dispose();
    super.dispose();
  }

  @override
  void setState(VoidCallback fn) {
    if (mounted) {
      super.setState(fn);
    }
  }

  toggleAnimation() {
    if (controller?.status == AnimationStatus.dismissed) {
      controller?.forward();
    } else if (controller?.status == AnimationStatus.completed) {
      controller?.reverse();
    }
  }

  toggleIcon() {
    if (controllerIcon?.status == AnimationStatus.dismissed) {
      controllerIcon?.forward();
    } else if (controllerIcon?.status == AnimationStatus.completed) {
      controllerIcon?.reverse();
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: CustomAppBar(
        titleWidget: CustomText(
          text:
              "${widget.isFromParents ? AppStrings.sons.tr() : AppStrings.busStudents.tr()} ( ${widget.busName} )",
          fontSize: 18,
          textAlign: TextAlign.center,
          fontW: FontWeight.w600,
          color: TColor.white,
        ),
        leftWidget: context.locale.toString() == "ar"
            ? InkWell(
                onTap: () {
                  Navigator.pop(context);
                },
                child: SvgPicture.asset(AppAssets.arrowBack),
              )
            : InkWell(
                onTap: () {
                  Navigator.pop(context);
                },
                child: SvgPicture.asset(
                  AppAssets.forwardArrow,
                  colorFilter:
                      const ColorFilter.mode(TColor.white, BlendMode.srcIn),
                  width: 25.w,
                  height: 25.w,
                ),
              ),
      ),
      body: SingleChildScrollView(
        child: SafeArea(
          child: Column(
            children: [
              const SBox(h: 20),
              BlocBuilder<StudentCubit, StudentState>(
                builder: (context, state) {
                  if (state is StudentLoadingStates) {
                    return const Center(
                      child: CircularProgressIndicator(
                        color: TColor.mainColor,
                      ),
                    );
                  } else if (state is StudentNoStudentsStates) {
                    return Center(
                      child: CustomText(
                        text: AppStrings.studentNotFound.tr(),
                      ),
                    );
                  } else if (state is StudentInitialStates) {
                    return const Center(
                      child: CircularProgressIndicator(),
                    );
                  } else if (state is StudentSuccessStates) {
                    return Column(
                      children: [
                        const SBox(h: 20),
                        CustomTableW(
                          busId: widget.id,
                          busName: widget.busName,
                          isFromBus: !widget.isFromParents,
                          studentInfoModels: StudentCubit.get(context).sons,
                        ),
                      ],
                    );
                  } else if (state is StudentErrorStates) {
                    if (state.error == "students not found") {
                      return Center(
                        child: CustomText(
                          text: AppStrings.studentNotFound.tr(),
                          fontSize: 17,
                          fontW: FontWeight.w600,
                        ),
                      );
                    } else {
                      return Center(
                        child: CustomText(
                          color: TColor.mainColor,
                          text: AppStrings.studentNotFound.tr(),
                          fontSize: 17,
                          fontW: FontWeight.w600,
                        ),
                      );
                    }
                  } else {
                    return const SizedBox();
                  }
                },
              ),
              const SBox(h: 80),
            ],
          ),
        ),
      ),
      floatingActionButtonLocation: FloatingActionButtonLocation.startFloat,
      floatingActionButton: widget.isFromParents
          ? const SizedBox()
          : BlocBuilder<StudentCubit, StudentState>(
              builder: (context, state) {
                return StudentFloatingActionWidget(
                  onPressed: () {
                    toggleAnimation();
                    toggleIcon();
                  },
                  opacity: opacity!,
                  children: [
                    // GroupFloatingActionButtonWidget(
                    //   btnTitle: AppStrings.extractExcel.tr(),
                    //   iconBtn: Icons.file_copy,
                    //   onTap: () async {
                    //     if (state is StudentSuccessStates) {
                    //       await openFile(StudentCubit.get(context).sons);
                    //     }
                    //   },
                    //   heroTag: '1',
                    // ),
                    // 10.verticalSpace,
                    GroupFloatingActionButtonWidget(
                      btnTitle: AppStrings.downloadPDF.tr(),
                      iconBtn: Icons.picture_as_pdf,
                      onTap: () {
                        DownloadStudentsFileRepo().downloadStudentsPDFFile(
                          busId: widget.id,
                          busName: widget.busName,
                          lang: context.locale.toString(),
                        );
                      },
                      heroTag: '2',
                    ),
                    10.verticalSpace,
                  ],
                );
              },
            ),
    );
  }

  Future<void> openFile(List<StudentModel?>? students) async {
    final bytes = await generateExcelFile(students);
    final directory = await getExternalStorageDirectory();
    final path =
        '${directory?.path}/${widget.busName}_${AppStrings.students.tr()}.xlsx';

    final file = File(path);
    await file.writeAsBytes(bytes);

    OpenFile.open(path);
  }

  Future<List<int>> generateExcelFile(List<StudentModel?>? students) async {
    // Create a new Excel document.
    final workbook = syncfusion.Workbook();
    //Adding a Sheet with name to workbook.
    final sheet = workbook.worksheets[0];

    sheet.getRangeByName('A1').setText('الاسم');
    sheet.getRangeByName('B1').setText('الكود');
    sheet.getRangeByName('C1').setText('كلمة المرور');
    sheet.getRangeByName('D1').setText('المرحلة');
    sheet.getRangeByName('E1').setText('اشتراك الباص');

    for (var i = 0; i < students!.length; i++) {
      final student = students[i];
      final row = i + 2;

      sheet.getRangeByName('A$row').setText(student?.name);
      sheet.getRangeByName('B$row').setText(student?.parent_key);
      sheet.getRangeByName('C$row').setText(student?.parent_secret);
      sheet.getRangeByName('D$row').setText(student?.grade?.name);
      sheet.getRangeByName('E$row').setText(
            student?.trip_type == "start_day"
                ? AppStrings.morningTrip.tr()
                : student?.trip_type == "end_day"
                    ? AppStrings.eveningTrip.tr()
                    : AppStrings.fullDay.tr(),
          );
    }

    ///Create a table with the data in a range
    final syncfusion.ExcelTable table = sheet.tableCollection
        .create('Table1', sheet.getRangeByName('A1:E${students.length + 1}'));

    ///Formatting table with a built-in style
    table.builtInTableStyle =
        syncfusion.ExcelTableBuiltInStyle.tableStyleMedium9;

    table.showTotalRow = true;
    table.columns[0].totalRowLabel = '${students.length}إجمالي عدد الطلبة: ';

    sheet.getRangeByName('A1:E${students.length + 1}').autoFitColumns();

    //Freezepane
    sheet.getRangeByName('A2').freezePanes();

    // Save the document.
    final bytes = workbook.saveAsStream();
    workbook.dispose();

    return bytes;
  }
}
