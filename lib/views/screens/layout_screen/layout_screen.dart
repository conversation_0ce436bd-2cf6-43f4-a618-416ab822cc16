import 'package:bus/bloc/layout_cubit/layout_cubit.dart';
import 'package:bus/bloc/layout_cubit/layout_states.dart';
import 'package:bus/bloc/profile_cubit/profile_cubit.dart';
import 'package:bus/bloc/profile_home_cubit/profile_home_cubit.dart';
import 'package:bus/config/global_variable.dart';
import 'package:bus/config/theme_colors.dart';
import 'package:bus/constant/path_route_name.dart';
import 'package:bus/helper/cache_helper.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:bus/translations/local_keys.g.dart';

class LayoutScreen extends StatefulWidget {
  static const String routeName = PathRouteName.bottomNavigationBar;
  const LayoutScreen({Key? key}) : super(key: key);

  @override
  State<LayoutScreen> createState() => _LayoutScreenState();
}

class _LayoutScreenState extends State<LayoutScreen> {
  @override
  void initState() {
    super.initState();

    // Load Google sign-in flag
    isGoogleSignIn = CacheHelper.getBool("isGoogleSignIn") ?? false;
    debugPrint("LayoutScreen initState - isGoogleSignIn: $isGoogleSignIn");

    // Load profile data when layout screen is first displayed
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (mounted) {
        // Load profile data
        context.read<ProfileCubit>().getProfile();
        // Load home profile data
        ProfileHomeCubit().getProfileHome();
      }
    });
  }
  @override
  Widget build(BuildContext context) {
    return BlocBuilder<LayoutCubit, LayoutStates>(
      builder: (context, states) {
        var cubit = LayoutCubit.get(context);
        return Scaffold(
          body: cubit.screens[cubit.currentIndex],
          bottomNavigationBar: ClipRRect(
            borderRadius: BorderRadius.only(
                topRight: Radius.circular(20.r),
                topLeft: Radius.circular(20.r)),
            child: BottomNavigationBar(
              // showSelectedLabels: false,
              // showUnselectedLabels: false,
              backgroundColor: TColor.mainColor,
              selectedItemColor: TColor.white,
              unselectedItemColor: TColor.white,
              showUnselectedLabels: false,
              items: [
                BottomNavigationBarItem(
                  activeIcon: const Icon(Icons.settings_outlined),
                  icon: const Icon(Icons.settings_outlined),
                  label: AppStrings.setting.tr(),
                ),
                BottomNavigationBarItem(
                  activeIcon: const Icon(Icons.home_outlined),
                  icon: const Icon(Icons.home_outlined),
                  label: AppStrings.home.tr(),
                ),
                BottomNavigationBarItem(
                  activeIcon: const Icon(Icons.help_outline_outlined),
                  icon: const Icon(Icons.help_outline_outlined),
                  label: AppStrings.help.tr(),
                ),
              ],
              currentIndex: cubit.currentIndex,
              onTap: (index) {
                if(index == 1){
                ProfileHomeCubit().getProfileHome();
                ProfileCubit().getProfile();


                }
                cubit.changeBottomNavigationBar(index);

              },
              type: BottomNavigationBarType.fixed,
            ),
          ),
        );
      },
    );
  }
}
