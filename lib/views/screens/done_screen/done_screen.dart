import 'package:bus/config/theme_colors.dart';
import 'package:bus/constant/path_route_name.dart';
import 'package:bus/translations/local_keys.g.dart';
import 'package:bus/utils/assets_utils.dart';
import 'package:bus/utils/sized_box.dart';
import 'package:bus/views/custom_widgets/custom_button.dart';
import 'package:bus/views/custom_widgets/custom_text.dart';
import 'package:bus/widgets/custom_background_image.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class DoneScreen extends StatelessWidget {
  static const String routeName = PathRouteName.done;
  const DoneScreen({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: SingleChildScrollView(
        child: CustomBackgroundImage(
          child: Column(
            children: [
              const SBox(h: 60),
              Image.asset(
                assetsImages("logo.png"),
                height: 121.w,
                width: 124.w,
              ),
              const SBox(h: 100),
              Container(
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(30.r),
                  color: TColor.white,
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withOpacity(0.2),
                      blurRadius: 3,
                      offset: const Offset(0, 4), // changes position of shadow
                    ),
                  ],
                ),
                width: 354.w,
                height: 310.w,
                child: Column(
                  children: [
                    const SBox(h: 40),
                    Image.asset(
                      assetsImages("done.png"),
                      width: 80.w,
                      height: 80.w,
                    ),
                    const SBox(h: 40),
                    CustomText(
                      text: AppStrings.passwordChangeSuccess.tr(),
                      fontSize: 16,
                      fontW: FontWeight.w400,
                    ),
                    const SBox(h: 50),
                    CustomButton(
                      text: AppStrings.goHome.tr(),
                      onTap: () {
                        debugPrint("login");
                      },
                      width: 307,
                      height: 48,
                      radius: 15,
                      borderColor: TColor.mainColor,
                      bgColor: TColor.mainColor,
                    ),
                    const SBox(h: 20),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
