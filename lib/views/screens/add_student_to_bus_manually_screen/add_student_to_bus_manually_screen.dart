import 'package:bus/translations/local_keys.g.dart';
import 'package:bus/utils/assets_utils.dart';
import 'package:bus/widgets/custom_appbar.dart';
import 'package:bus/widgets/student_widgets/custom_search_w.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';

import '../../../config/theme_colors.dart';
import '../../../constant/path_route_name.dart';
import '../../custom_widgets/build_table_row_widget.dart';
import '../../custom_widgets/custom_button.dart';
import '../../custom_widgets/custom_text.dart';

class AddStudentToBusManuallyScreen extends StatefulWidget {
  static const String routeName = PathRouteName.addStudentToBusManuallyScreen;

  const AddStudentToBusManuallyScreen({Key? key}) : super(key: key);

  @override
  State<AddStudentToBusManuallyScreen> createState() =>
      _AddStudentToBusManuallyScreenState();
}

class _AddStudentToBusManuallyScreenState
    extends State<AddStudentToBusManuallyScreen> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: CustomAppBar(
        titleWidget: CustomText(
          text: AppStrings.addStudentToBusManually.tr(),
          fontSize: 18,
          textAlign: TextAlign.center,
          fontW: FontWeight.w600,
          color: TColor.white,
        ),
        rightWidget: InkWell(
          onTap: () {
            Navigator.pop(context);
          },
          child: SvgPicture.asset(AppAssets.arrowBack),
        ),
      ),
      body: SingleChildScrollView(
        child: SizedBox(
          height: 620.w,
          width: 1.sw,
          child: Column(
            children: [
              CustomSearchW(hintText: AppStrings.search.tr()),
              Padding(
                padding: const EdgeInsets.all(8.0),
                child: Container(
                  margin: const EdgeInsets.symmetric(horizontal: 8.0),
                  clipBehavior: Clip.antiAlias,
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(15.0),
                  ),
                  child: Table(
                    columnWidths: const {
                      0: FlexColumnWidth(2),
                      1: FlexColumnWidth(2),
                      2: FlexColumnWidth(2),
                    },
                    border: TableBorder.all(
                        color: TColor.tabColors,
                        borderRadius: BorderRadius.circular(15.0)),
                    children: [
                      const BuildTableRowWidget(
                        cell: ['Student', 'Options'],
                        header: true,
                      ).build(context),
                      const BuildTableRowWidget(
                        cell: [
                          'Ahmed Zaki',
                          Icons.more_horiz,
                        ],
                        // onTap: () => _showDialogBus(context),
                      ).build(context),
                      const BuildTableRowWidget(
                        cell: [
                          'Ahmed Zaki',
                          Icons.more_horiz,
                        ],
                        // onTap: () => _showDialogBus(context),
                      ).build(context),
                      const BuildTableRowWidget(
                        cell: [
                          'Ahmed Zaki',
                          Icons.more_horiz,
                        ],
                        // onTap: () => _showDialogBus(context),
                      ).build(context),
                    ],
                  ),
                ),
              ),
              Expanded(
                child: Align(
                  alignment: Alignment.bottomCenter,
                  child: Padding(
                    padding: EdgeInsets.symmetric(horizontal: 37.w),
                    child: CustomButton(
                      text: AppStrings.add.tr(),
                      onTap: () {},
                      width: 428,
                      height: 53,
                      radius: 15,
                      borderColor: TColor.mainColor,
                      bgColor: TColor.mainColor,
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
