import 'package:bus/config/theme_colors.dart';
import 'package:bus/constant/path_route_name.dart';
import 'package:bus/helper/cache_helper.dart';
import 'package:bus/translations/local_keys.g.dart';
import 'package:bus/utils/assets_utils.dart';
import 'package:bus/utils/sized_box.dart';
import 'package:bus/views/custom_widgets/custom_text.dart';
import 'package:bus/widgets/custom_appbar.dart';
import 'package:bus/widgets/language_widgets/custom_container_l_w.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';

class LanguagesScreen extends StatelessWidget {
  static const String routeName = PathRouteName.languageScreen;
  const LanguagesScreen({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: CustomAppBar(
        rightWidget: const SizedBox(),
        titleWidget: Row(
          children: [
            CustomText(
              text: AppStrings.languages.tr(),
              fontSize: 18,
              textAlign: TextAlign.center,
              fontW: FontWeight.w600,
              color: TColor.white,
            ),
            const SizedBox(width: 10.0),
          ],
        ),
        leftWidget: context.locale.toString() == "ar"
            ? InkWell(
                onTap: () {
                  Navigator.pop(context);
                },
                child: SvgPicture.asset(AppAssets.arrowBack),
              )
            : InkWell(
                onTap: () {
                  Navigator.pop(context);
                },
                child: SvgPicture.asset(
                  AppAssets.forwardArrow,
                  colorFilter: const ColorFilter.mode(TColor.white, BlendMode.srcIn),
                  width: 25.w,
                  height: 25.w,
                ),
              ),
      ),
      body: SingleChildScrollView(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const SBox(h: 30),
            CustomContainerLW(
              name: AppStrings.arabic.tr(),
              url: 'palestine-flag-icon.png',
              icons:  context.locale.toString() == "ar" ? const Icon(Icons.check, color: TColor.mainColor,) : const SizedBox(),
              onTap: () {
               CacheHelper.putString('lang', 'ar');

                context.setLocale(const Locale("ar"));
              },
            ),
            const SBox(h: 15),
            CustomContainerLW(
              name: AppStrings.english.tr(),
              url: 'gb.png',
              icons:context.locale.toString() == "ar" ?  const SizedBox()  : const Icon(Icons.check, color: TColor.mainColor,) ,
              onTap: () {
               CacheHelper.putString('lang', 'en');
                context.setLocale(const Locale("en"));
                debugPrint('aaaaaaaaaaaaaaaaaaaaaaaaaaaa');
              },
            ),
          ],
        ),
      ),
    );
  }
}
