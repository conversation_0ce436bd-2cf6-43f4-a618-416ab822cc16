import 'package:bus/helper/cache_helper.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import '../../../config/theme_colors.dart';
import '../../../constant/path_route_name.dart';
import 'package:bus/translations/local_keys.g.dart';
import '../../../utils/assets_utils.dart';
import '../../../widgets/custom_background_image.dart';
import '../../../widgets/language_widgets/custom_container_l_w.dart';
import '../../custom_widgets/custom_button.dart';
import '../../custom_widgets/custom_text.dart';

class OnBoardingLanguageScreen extends StatelessWidget {
  static const String routeName = PathRouteName.onBoardingLanguage;
  const OnBoardingLanguageScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: SingleChildScrollView(
        child: CustomBackgroundImage(
          child: Column(
            children: [
              10.verticalSpace,
              Image.asset(
                assetsImages("logo.png"),
                height: 121.w,
                width: 124.w,
              ),
              40.verticalSpace,
              const CustomText(
                text: 'اختر اللغة',
                color: TColor.white,
                fontW: FontWeight.w700,
                fontSize: 20,
              ),
              10.verticalSpace,
              const CustomText(
                text: 'Select Language',
                color: TColor.white,
                fontW: FontWeight.w700,
                fontSize: 20,
              ),
              30.verticalSpace,
              Container(
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(30.r),
                  color: TColor.white,
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withOpacity(0.2),
                      blurRadius: 3,
                      offset: const Offset(0, 4), // changes position of shadow
                    ),
                  ],
                ),
                width: 354.w,
                child: Column(
                  children: [
                    50.verticalSpace,
                    CustomContainerLW(
                      name: AppStrings.arabic.tr(),
                      url: 'palestine-flag-icon.png',
                      icons: context.locale.toString() == "ar"
                          ? const Icon(
                              Icons.check,
                              color: TColor.mainColor,
                            )
                          : const SizedBox(),
                      onTap: () {
                        context.setLocale(const Locale("ar"));
                        CacheHelper.putString('lang', 'ar');
                      },
                    ),
                    15.verticalSpace,
                    CustomContainerLW(
                      name: AppStrings.english.tr(),
                      url: 'gb.png',
                      icons: context.locale.toString() == "ar"
                          ? const SizedBox()
                          : const Icon(
                              Icons.check,
                              color: TColor.mainColor,
                            ),
                      onTap: () {
                        context.setLocale(const Locale("en"));
                        CacheHelper.putString('lang', 'en');
                      },
                    ),
                    50.verticalSpace,
                    CustomButton(
                      text: AppStrings.next.tr(),
                      onTap: () {
                        Navigator.pushNamed(context, PathRouteName.login);
                      },
                      width: 307,
                      height: 48,
                      radius: 15,
                      borderColor: TColor.mainColor,
                      bgColor: TColor.mainColor,
                    ),
                    50.verticalSpace,
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
