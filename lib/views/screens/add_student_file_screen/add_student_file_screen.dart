import 'dart:io';
import 'package:bus/bloc/add_student_file_cubit/add_student_file_cubit.dart';
import 'package:bus/bloc/add_student_file_cubit/add_student_file_states.dart';
import 'package:bus/bloc/classroom__cubit/class_room_cubit.dart';
import 'package:bus/bloc/grade_cubit/grade_cubit.dart';
import 'package:bus/bloc/grade_cubit/grade_states.dart';
import 'package:bus/bloc/student_cubit/student_cubit.dart';
import 'package:bus/config/theme_colors.dart';
import 'package:bus/constant/path_route_name.dart';
import 'package:bus/data/models/student_models/student_grade_models.dart';
import 'package:bus/data/repo/file_name_download_repo.dart';
import 'package:bus/translations/local_keys.g.dart';
import 'package:bus/utils/sized_box.dart';
import 'package:bus/views/custom_widgets/custom_button.dart';
import 'package:bus/views/custom_widgets/custom_text.dart';
import 'package:bus/widgets/custom_appbar.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:file_picker/file_picker.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:logger/logger.dart';
import 'package:open_file/open_file.dart';
import '../../../data/repo/download_students_file_repo.dart';
import '../../../utils/assets_utils.dart';
import '../../custom_widgets/custom_drop_down_button.dart';
import '../student_screen/student_screen.dart';

class AddStudentFileScreen extends StatefulWidget {
  static const String routeName = PathRouteName.addStudentFile;

  const AddStudentFileScreen({Key? key}) : super(key: key);

  @override
  State<AddStudentFileScreen> createState() => _AddStudentFileScreenState();
}

class _AddStudentFileScreenState extends State<AddStudentFileScreen> {
  FilePickerResult? result;
  String? filePath;
  int? gradeId = 0;
  int? classroomId;
  bool isGradeChanged = false;
  bool isAddLoading = false;

  Future<void> getFile() async {
    try {
      String initialDirectory = '/storage/emulated/0/Download';

      if (!Directory(initialDirectory).existsSync()) {
        initialDirectory = '/'; // Fallback to the root directory
      }

      final result = await FilePicker.platform.pickFiles(
        dialogTitle: "Select a file",
        type: FileType.any,
        initialDirectory: initialDirectory,
      );

      if (result != null) {
        setState(() {
          this.result = result;
          filePath = result.files.single.path;
        });
      }
    } catch (e) {
      Logger().e("Error opening file picker: $e");
    }
  }

  void _downloadExampleFile() async {
    final fileNameDownload = FileNameDownloadRepo();
    final fileName = await fileNameDownload.repo();
    await DownloadStudentsFileRepo().downloadStudentsExampleFile(
      fileNameAndExtension: fileName.data.toString(),
    );
  }

  void _addStudentFile() async {
    if (gradeId != null && classroomId != null && filePath != null) {
      await context.read<AddStudentFileCubit>().addFileStudent(
        file: filePath,
        uploadFile: File(filePath!),
        gradeId: gradeId,
        classId: classroomId,
      );

      if (context.read<AddStudentFileCubit>().state is AddStudentFileSuccessStates) {
        context.read<StudentCubit>().getStudent(page: 1, isFirst: true);
        Navigator.pushReplacement(
          context,
          MaterialPageRoute(builder: (context) => const StudentScreen()),
        );
      }
    } else {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          backgroundColor: TColor.redAccent,
          content: CustomText(
            text: "Please Choose grade name and class name",
            fontSize: 18,
            maxLine: 5,
            color: TColor.white,
          ),
        ),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: CustomAppBar(
        titleWidget: CustomText(
          text: AppStrings.addStudentFile.tr(),
          fontSize: 18,
          textAlign: TextAlign.center,
          fontW: FontWeight.w600,
          color: TColor.white,
        ),
        leftWidget: context.locale.toString() == "ar"
            ? InkWell(
          onTap: () => Navigator.pop(context),
          child: SvgPicture.asset(AppAssets.arrowBack),
        )
            : InkWell(
          onTap: () => Navigator.pop(context),
          child: SvgPicture.asset(
            AppAssets.forwardArrow,
            colorFilter: const ColorFilter.mode(TColor.white, BlendMode.srcIn),
            width: 25.w,
            height: 25.w,
          ),
        ),
      ),
      body: SingleChildScrollView(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildGradeDropdown(),
            20.verticalSpace,
            _buildClassroomDropdown(),
            20.verticalSpace,
            _buildFilePicker(),
            60.verticalSpace,
            _buildDownloadExampleFileButton(),
            250.verticalSpace,
            _buildAddButton(),
            40.verticalSpace,
          ],
        ),
      ),
    );
  }

  Widget _buildGradeDropdown() {
    return Padding(
      padding: EdgeInsets.only(left: 40.w, right: 40.w, bottom: 10.h),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          CustomText(
            text: AppStrings.grade.tr(),
            fontSize: 15,
            textAlign: TextAlign.center,
            fontW: FontWeight.bold,
            color: TColor.black,
          ),
          BlocBuilder<GradeCubit, GradeStates>(
            builder: (context, state) {
              if (state is GradeLoadingStates) {
                return const Center(
                  child: CircularProgressIndicator(
                    color: TColor.mainColor,
                  ),
                );
              } else if (state is GradeSuccessStates) {
                final grades = state.gradeModels?.data ?? [];
                final gradeList = [
                  StudentGradeModels(id: 0, name: AppStrings.selectGrade.tr()),
                  ...grades,
                ];

                return CustomDropDownButton(
                  items: gradeList.map((grade) {
                    return DropdownMenuItem<int>(
                      value: grade.id,
                      child: Padding(
                        padding: EdgeInsets.symmetric(horizontal: 20.w),
                        child: Text(grade.name!),
                      ),
                    );
                  }).toList(),
                  onChanged: (value) {
                    setState(() {
                      gradeId = value;
                    });
                    if (value != 0) {
                      BlocProvider.of<ClassRoomCubit>(context).getClassRoom(value!);
                      isGradeChanged = true;
                    }
                  },
                  value: gradeId,
                );
              } else {
                return const SizedBox();
              }
            },
          ),
        ],
      ),
    );
  }

  Widget _buildClassroomDropdown() {
    return Padding(
      padding: EdgeInsets.only(left: 40.w, right: 40.w, bottom: 10.h),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          CustomText(
            text: AppStrings.classroom.tr(),
            fontSize: 15,
            textAlign: TextAlign.center,
            fontW: FontWeight.bold,
            color: TColor.black,
          ),
          gradeId == 0
              ? _buildPlaceholder(AppStrings.selectEduLev.tr())
              : BlocBuilder<ClassRoomCubit, ClassRoomState>(
            builder: (context, state) {
              if (state is ClassRoomLoadingStates) {
                return const Center(
                  child: CircularProgressIndicator(
                    color: TColor.mainColor,
                  ),
                );
              } else if (state is ClassRoomSuccessStates) {
                final classrooms = state.classRoomModels?.classRooms ?? [];
                if (isGradeChanged) {
                  isGradeChanged = false;
                  classroomId = classrooms.isNotEmpty ? classrooms[0]?.id : null;
                }

                return CustomDropDownButton(
                  items: classrooms.map((classroom) {
                    return DropdownMenuItem<int>(
                      value: classroom?.id,
                      child: Padding(
                        padding: EdgeInsets.symmetric(horizontal: 20.w),
                        child: Text(classroom?.name ?? ''),
                      ),
                    );
                  }).toList(),
                  onChanged: (value) {
                    setState(() {
                      classroomId = value;
                    });
                  },
                  value: classroomId,
                );
              } else {
                return _buildPlaceholder(AppStrings.selectEduLev.tr());
              }
            },
          ),
        ],
      ),
    );
  }

  Widget _buildFilePicker() {
    return Padding(
      padding: EdgeInsets.only(left: 40.w, right: 40.w, bottom: 10.h),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          CustomText(
            text: AppStrings.studentsFile.tr(),
            fontSize: 15,
            textAlign: TextAlign.center,
            fontW: FontWeight.bold,
            color: TColor.black,
          ),
          Container(
            padding: EdgeInsets.symmetric(horizontal: 30.w),
            height: 45.w,
            decoration: BoxDecoration(
              borderRadius: BorderRadius.only(
                topLeft: Radius.circular(10.r),
                topRight: Radius.circular(10.r),
                bottomLeft: context.locale.toString() == "ar" ? Radius.circular(10.r) : Radius.zero,
                bottomRight: context.locale.toString() == "ar" ? Radius.zero : Radius.circular(10.r),
              ),
              border: Border.all(width: 1.w, color: TColor.fillFormFieldB),
            ),
            child: Row(
              children: [
                Container(
                  width: 110.w,
                  height: 45.w,
                  decoration: BoxDecoration(
                    color: TColor.borderContainer,
                    borderRadius: BorderRadius.only(
                      topRight: Radius.circular(10.r),
                      bottomLeft: context.locale.toString() == "ar" ? Radius.circular(10.r) : Radius.zero,
                      bottomRight: context.locale.toString() == "ar" ? Radius.zero : Radius.circular(10.r),
                    ),
                    border: Border.all(width: 1.w, color: TColor.borderContainer),
                  ),
                  child: InkWell(
                    onTap: getFile,
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Icon(
                          Icons.drive_folder_upload,
                          color: TColor.white,
                          size: 20.sp,
                        ),
                        const SBox(w: 10),
                        CustomText(
                          text: AppStrings.addFile.tr(),
                          color: TColor.white,
                          fontSize: 13,
                          fontW: FontWeight.w400,
                        ),
                      ],
                    ),
                  ),
                ),
                const SBox(w: 20),
                Expanded(
                  child: SingleChildScrollView(
                    scrollDirection: Axis.horizontal,
                    child: CustomText(
                      maxLine: 2,
                      text: filePath ?? "",
                      color: TColor.dialogName,
                      fontW: FontWeight.w400,
                      fontSize: 13,
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildDownloadExampleFileButton() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        Container(
          width: 200.w,
          height: 45.w,
          decoration: BoxDecoration(
            color: TColor.borderContainer,
            borderRadius: BorderRadius.only(
              topRight: Radius.circular(10.r),
              bottomLeft: context.locale.toString() == "ar" ? Radius.circular(10.r) : Radius.zero,
              bottomRight: context.locale.toString() == "ar" ? Radius.zero : Radius.circular(10.r),
            ),
            border: Border.all(width: 1.w, color: TColor.borderContainer),
          ),
          child: InkWell(
            onTap: _downloadExampleFile,
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(
                  Icons.download,
                  color: TColor.white,
                  size: 20.sp,
                ),
                const SBox(w: 10),
                CustomText(
                  text: AppStrings.downloadExampleFile.tr(),
                  color: TColor.white,
                  fontSize: 13,
                  fontW: FontWeight.w400,
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildAddButton() {
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: 37.w),
      child: BlocConsumer<AddStudentFileCubit, AddStudentFileStates>(
        listener: (context, state) {
          if (state is AddStudentFileSuccessStates) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                backgroundColor: TColor.greenSuccess,
                content: CustomText(
                  text: state.addStudentFileModels!.message,
                  fontSize: 18,
                  maxLine: 5,
                  color: TColor.white,
                ),
              ),
            );
            setState(() {
              filePath = null;
            });
          } else if (state is AddStudentFileErrorStates) {
            showModalBottomSheet(
              context: context,
              builder: (context) {
                return Container(
                  height: MediaQuery.of(context).size.height * 0.3,
                  width: double.infinity,
                  color: TColor.redAccent,
                  padding: const EdgeInsets.all(16.0),
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      CustomText(
                        text: state.error,
                        fontSize: 18,
                        maxLine: 5,
                        color: TColor.white,
                      ),
                      SizedBox(height: 50.h),
                      ElevatedButton(
                        style: ElevatedButton.styleFrom(
                          backgroundColor: TColor.text,
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(10.r),
                          ),
                        ),
                        onPressed: () {
                          Navigator.pop(context);
                          if (filePath != null) {
                            OpenFile.open(filePath!);
                            Logger().i("Opening file for editing: $filePath");
                          }
                        },
                        child: const CustomText(
                          text: "Open File to Edit",
                          fontSize: 16,
                          color: TColor.white,
                        ),
                      ),
                    ],
                  ),
                );
              },
            );
          }
        },
        builder: (context, state) {
          if (state is AddStudentFileLoadingStates) {
            return const Center(
              child: CircularProgressIndicator(),
            );
          } else {
            return CustomButton(
              text: AppStrings.add.tr(),
              onTap: _addStudentFile,
              width: 428,
              height: 53,
              radius: 15,
              borderColor: TColor.mainColor,
              bgColor: TColor.mainColor,
            );
          }
        },
      ),
    );
  }

  Widget _buildPlaceholder(String text) {
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: 37.w),
      child: Container(
        width: 428.w,
        height: 53.w,
        decoration: BoxDecoration(
          color: TColor.fillFormFieldB,
          borderRadius: BorderRadius.circular(15.r),
        ),
        child: Center(
          child: Text(text),
        ),
      ),
    );
  }
}