import 'package:bus/bloc/absences_cubit/absences_states.dart';
import 'package:bus/config/theme_colors.dart';
import 'package:bus/translations/local_keys.g.dart';
import 'package:bus/utils/assets_utils.dart';
import 'package:bus/utils/sized_box.dart';
import 'package:bus/views/custom_widgets/custom_text.dart';
import 'package:bus/widgets/custom_appbar.dart';
import 'package:bus/widgets/custom_label_w.dart';
import 'package:bus/widgets/request_change_address_widgets/custom_c_request_address_w.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';

class RequestAbsencesScreen extends StatefulWidget {
  RequestAbsencesScreen({Key? key, this.args}) : super(key: key);
  final AbsencesModel? args;

  @override
  State<RequestAbsencesScreen> createState() => _RequestAbsencesScreenState();
}

class _RequestAbsencesScreenState extends State<RequestAbsencesScreen> {
  @override
  Widget build(BuildContext context) {
    //final args = ModalRoute.of(context)!.settings.arguments as AbsencesModel;
    return Scaffold(
      appBar: CustomAppBar(
        rightWidget: Container(),
        titleWidget: const Row(
          children: [
            CustomText(
              text: "Request Absence",
              fontSize: 18,
              textAlign: TextAlign.center,
              fontW: FontWeight.w600,
              color: TColor.white,
            ),
            SizedBox(width: 10.0),
          ],
        ),
        leftWidget: context.locale.toString() == "ar"
            ? InkWell(
                onTap: () {
                  Navigator.pop(context);
                },
                child: SvgPicture.asset(AppAssets.arrowBack),
              )
            : InkWell(
                onTap: () {
                  Navigator.pop(context);
                },
                child: SvgPicture.asset(
                  AppAssets.forwardArrow,
                  colorFilter:
                      const ColorFilter.mode(TColor.white, BlendMode.srcIn),
                  width: 25.w,
                  height: 25.w,
                ),
              ),
      ),
      body: SingleChildScrollView(
        child: SizedBox(
          width: 1.sw,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const SBox(),
                  Padding(
                    padding: EdgeInsets.symmetric(horizontal: 4.0.w),
                    child: CustomLabelW(
                      label: AppStrings.studentName.tr(),
                    ),
                  ),
                  const SBox(h: 3),
                  CustomCRequestAddressW(
                    name: "${widget.args?.student_name}",
                  ),
                  const SBox(h: 20),
                  Padding(
                    padding: EdgeInsets.symmetric(horizontal: 4.0.w),
                    child: CustomLabelW(
                      label: AppStrings.parents.tr(),
                    ),
                  ),
                  const SBox(h: 3),
                  CustomCRequestAddressW(
                      name: "${widget.args?.my__parent_name}"),
                  const SBox(h: 20),
                  Padding(
                    padding: EdgeInsets.symmetric(horizontal: 4.0.w),
                    child: const CustomLabelW(
                      label: "attebdance date",
                    ),
                  ),
                  const SBox(h: 3),
                  CustomCRequestAddressW(
                      name: "${widget.args?.attendence_date}"),
                  const SBox(h: 20),
                  Padding(
                    padding: EdgeInsets.symmetric(horizontal: 4.0.w),
                    child: const CustomLabelW(
                      label: "attebdance type",
                    ),
                  ),
                  const SBox(h: 3),
                  CustomCRequestAddressW(
                      name: "${widget.args?.attendence_type}"),
                  const SBox(h: 20),
                  Padding(
                    padding: EdgeInsets.symmetric(horizontal: 4.0.w),
                    child: CustomLabelW(
                      label: AppStrings.busName.tr(),
                    ),
                  ),
                  const SBox(h: 3),
                  CustomCRequestAddressW(
                    name: widget.args?.bus_name,
                  ),
                  const SBox(h: 20),

                  // const SBox(h: 3),
                  // CustomCRequestAddressW(
                  //     height: 76, name: "${args.sc}"),
                  // const SBox(h: 20),
                  Padding(
                    padding: EdgeInsets.symmetric(horizontal: 4.0.w),
                    child: const CustomLabelW(
                      label: "schoolname",
                    ),
                  ),
                  const SBox(h: 3),
                  CustomCRequestAddressW(name: widget.args?.school_name),
                  const SBox(
                    h: 30,
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }
}
