import 'package:bus/bloc/absences_cubit/absences_cubit.dart';
import 'package:bus/bloc/absences_cubit/absences_states.dart';
import 'package:bus/bloc/buses_cubit/buses_cubit.dart';
import 'package:bus/bloc/buses_cubit/buses_states.dart';
import 'package:bus/constant/path_route_name.dart';
import 'package:bus/data/models/buses_models/buses_info_models.dart';
import 'package:bus/utils/helpers.dart';
import 'package:bus/views/custom_widgets/custom_button.dart';
import 'package:bus/views/custom_widgets/custom_form_field_border.dart';
import 'package:bus/views/screens/requests_absences/request_absences_screen.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_datetime_picker_plus/flutter_datetime_picker_plus.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';

import '../../../config/theme_colors.dart';
import 'package:bus/translations/local_keys.g.dart';import '../../../utils/assets_utils.dart';
import '../../../utils/sized_box.dart';
import '../../../widgets/custom_appbar.dart';
import '../../../widgets/page_number_widget.dart';
import '../../custom_widgets/build_table_row_widget.dart';
import '../../custom_widgets/custom_text.dart';

class RequestsAbsencesScreen extends StatefulWidget {
  static const String routeName = PathRouteName.requestsAbsences;

  const RequestsAbsencesScreen({Key? key}) : super(key: key);

  @override
  State<RequestsAbsencesScreen> createState() => _RequestsAbsencesScreenState();
}

class _RequestsAbsencesScreenState extends State<RequestsAbsencesScreen> {
  DateTime? dateBirth;
  String attend_type = "";
  String studentName = "";

  String? bussesid;

  String dateBirthS(DateTime? datetime) {
    if (datetime != null) {
      String year = datetime.year.toString();
      int month = datetime.month;
      int day = datetime.day;
      String daystring = day >= 10 ? day.toString() : "0$day";
      String monthstring = month >= 10 ? month.toString() : "0$month";
      String dateFormat = "$year-$monthstring-$daystring";

      debugPrint(dateFormat);
      return dateFormat;
    } else {
      return "";
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: CustomAppBar(
        titleWidget: CustomText(
          text: AppStrings.absenceRequests.tr(),
          fontSize: 18,
          textAlign: TextAlign.center,
          fontW: FontWeight.w600,
          color: TColor.white,
        ),
        leftWidget: context.locale.toString() == "ar"
            ? InkWell(
                onTap: () {
                  Navigator.pop(context);
                },
                child: SvgPicture.asset(AppAssets.arrowBack),
              )
            : InkWell(
                onTap: () {
                  Navigator.pop(context);
                },
                child: SvgPicture.asset(
                  AppAssets.forwardArrow,
                  colorFilter:
                      const ColorFilter.mode(TColor.white, BlendMode.srcIn),
                  width: 25.w,
                  height: 25.w,
                ),
              ),
      ),
      body: SingleChildScrollView(
        child: BlocBuilder<AbsencesCubit, AbsencesStates>(
          builder: (context, states) {
            debugPrint(states.toString());
            if (states is AbsencesLoadingStates) {
              if (kDebugMode) {
                print(states.toString());
              }
              return const Center(
                child: CircularProgressIndicator(),
              );
            } else if (states is AbsencesSuccessStates) {
              return Column(
                children: [
                  const SBox(h: 15),
                  //! BUSSES
                  Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Padding(
                        padding: EdgeInsets.symmetric(horizontal: 10.w),
                        child: Container(
                          width: 160.w,
                          height: 53.w,
                          decoration: BoxDecoration(
                            color: TColor.fillFormFieldB,
                            borderRadius: BorderRadius.circular(15.r),
                          ),
                          child: DropdownButtonHideUnderline(
                            child: ButtonTheme(
                              child: DropdownButton<String?>(
                                  value: attend_type,
                                  elevation: 16,
                                  style:
                                      const TextStyle(color: Colors.deepPurple),
                                  underline: Container(),
                                  iconSize: 0.0,
                                  onChanged: (String? value) {
                                    setState(() {
                                      attend_type = value!;
                                    });
                                    // This is called when the user selects an item.
                                  },
                                  items: [
                                    DropdownMenuItem<String>(
                                      value: "",
                                      child: Padding(
                                        padding: EdgeInsets.symmetric(
                                            horizontal: 10.w),
                                        child:
                                            const Text("Select Absence Type"),
                                      ),
                                    ),
                                    DropdownMenuItem<String>(
                                      value: "start_day",
                                      child: Padding(
                                        padding: EdgeInsets.symmetric(
                                            horizontal: 10.w),
                                        child: const Text("start day"),
                                      ),
                                    ),
                                    DropdownMenuItem<String>(
                                      value: "end_day",
                                      child: Padding(
                                        padding: EdgeInsets.symmetric(
                                            horizontal: 10.w),
                                        child: const Text("end_day"),
                                      ),
                                    ),
                                    DropdownMenuItem<String>(
                                      value: "full_day",
                                      child: Padding(
                                        padding: EdgeInsets.symmetric(
                                            horizontal: 10.w),
                                        child: const Text("full_day"),
                                      ),
                                    )
                                  ]),
                            ),
                          ),
                        ),
                      ),
                      BlocBuilder<BusesCubit, BusesState>(
                        builder: (context, states) {
                          if (kDebugMode) {
                            print(states.toString());
                          }
                          if (states is BusesLoadingStates) {
                            return const Center(
                              child: CircularProgressIndicator(),
                            );
                          } else if (states is BusesSuccessStates) {
                            List<DropdownMenuItem<String>> dropdowns = [];
                            dropdowns.add(DropdownMenuItem<String>(
                              value: "",
                              child: Padding(
                                padding: EdgeInsets.symmetric(horizontal: 5.w),
                                child: const Text("Select bus"),
                              ),
                            ));
                            dropdowns.addAll(states.busesModels!.data!.data!
                                .map<DropdownMenuItem<String>>(
                                    (BusesInfoModel value) {
                              return DropdownMenuItem<String>(
                                value: value.id.toString(),
                                child: Padding(
                                  padding:
                                      EdgeInsets.symmetric(horizontal: 20.w),
                                  child: Text(value.name!),
                                ),
                              );
                            }).toList());
                            return Padding(
                              padding: EdgeInsets.symmetric(horizontal: 20.w),
                              child: Container(
                                width: 150.w,
                                height: 53.w,
                                decoration: BoxDecoration(
                                  color: TColor.fillFormFieldB,
                                  borderRadius: BorderRadius.circular(15.r),
                                ),
                                child: DropdownButtonHideUnderline(
                                  child: ButtonTheme(
                                    child: DropdownButton<String>(
                                        hint: Padding(
                                          padding: EdgeInsets.symmetric(
                                              horizontal: 20.w),
                                          child: CustomText(
                                            text: AppStrings.bus.tr(),
                                            color: TColor.tabColors,
                                          ),
                                        ),
                                        value: bussesid ?? "",
                                        elevation: 16,
                                        style: const TextStyle(
                                            color: Colors.deepPurple),
                                        underline: Container(),
                                        iconSize: 0.0,
                                        onChanged: (String? value) {
                                          setState(() {
                                            bussesid = value;
                                          });
                                        },
                                        items: dropdowns),
                                  ),
                                ),
                              ),
                            );
                          } else if (states is BusesErrorStates) {
                            return const SizedBox(
                              child: Center(child: CustomText(text: "Error")),
                            );
                          } else {
                            return const SizedBox();
                          }
                        },
                      ),
                    ],
                  ),
                  const SBox(h: 15),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      CustomFormFieldWithBorder(
                        requierdNumber: 0,
                        formFieldWidth: 150,
                        validation: "Name is requierd",
                        heightA: 53,
                        hintText: AppStrings.studentName.tr(),
                        borderColor: TColor.fillFormFieldB,
                        fillColor: TColor.fillFormFieldB,
                        radiusNumber: 15.0,
                        paddingRight: 1.w,
                        onChanged: (p0) {
                          studentName = p0;
                        },
                        paddingLeft: 10.w,
                        contentPaddingVertical: 15,
                        contentPaddingHorizontal: 15,
                      ),
                      Padding(
                        padding: EdgeInsets.symmetric(horizontal: 37.w),
                        child: InkWell(
                          onTap: () {
                            DatePicker.showDatePicker(context,
                                locale: LocaleType.en,
                                showTitleActions: true, onChanged: (date) {
                              print("onChange $date");
                            }, onConfirm: (date) {
                              print("onConfirm date $date");
                              setState(() {
                                dateBirth = date;
                              });
                            });
                          },
                          child: Container(
                            width: 150.w,
                            height: 53.w,
                            decoration: BoxDecoration(
                              border: Border.all(color: TColor.fillFormFieldB),
                              borderRadius: BorderRadius.circular(15.0.r),
                              color: TColor.fillFormFieldB,
                            ),
                            child: Padding(
                              padding: context.locale.toString() == "ar"
                                  ? EdgeInsets.only(top: 15.w, right: 14.w)
                                  : EdgeInsets.only(top: 15.w, left: 14.w),
                              child: CustomText(
                                text: dateBirth != null
                                    ? dateBirthS(dateBirth)
                                    : "Select date",
                                fontW: FontWeight.w500,
                                fontSize: 15,
                                color: TColor.tabColors,
                              ),
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                  const SBox(h: 15),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      CustomButton(
                        width: 100.w,
                        height: 40.w,
                        onTap: () {
                          BlocProvider.of<AbsencesCubit>(context)
                              .getAbsencesWithFilters(
                                  studentName: studentName,
                                  date: dateBirthS(dateBirth),
                                  attendType: attend_type,
                                  busid: bussesid);
                          setState(() {});
                        },
                        fontSize: 12,
                        bgColor: TColor.mainColor,
                        text: "Apply Filtter",
                      )
                    ],
                  ),
                  Padding(
                    padding: const EdgeInsets.all(8.0),
                    child: Container(
                      margin: const EdgeInsets.symmetric(horizontal: 8.0),
                      clipBehavior: Clip.antiAlias,
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(15.0),
                      ),
                      child: Table(
                        columnWidths: const {
                          0: FlexColumnWidth(4),
                          1: FlexColumnWidth(3),
                          2: FlexColumnWidth(3),
                          3: FlexColumnWidth(2),
                        },
                        border: TableBorder.all(
                          color: TColor.tabColors,
                          borderRadius: BorderRadius.circular(15.0),
                        ),
                        children: List.generate(
                            1 + states.absencesModels.length, (index) {
                          if (index == 0) {
                            return const BuildTableRowWidget(
                              cell: [
                                'Student Name',
                                'Parint Name',
                                'Absence Date',
                                'Options'
                              ],
                              header: true,
                            ).build(context);
                          } else {
                            final newAbsences =
                                states.absencesModels[index - 1];
                            return BuildTableRowWidget(
                                cell: [
                                  newAbsences.student_name ??
                                      "student dont have name",
                                  newAbsences.my__parent_name ?? "",
                                  newAbsences.attendence_date,
                                  Icons.more_horiz,
                                ],
                                onTapDown: (position) {
                                  Helpers.customAbsenceShowDialog(
                                    context,
                                    position: position.globalPosition,
                                    onTapShow: () {
                                      Navigator.of(context)
                                        ..pop()
                                        ..push(
                                            MaterialPageRoute(builder: (ctx) {
                                          return RequestAbsencesScreen(
                                            args: newAbsences,
                                          );
                                        }));
                                    },
                                  );
                                }).build(context);
                          }
                        }),
                      ),
                    ),
                  ),
                  const SBox(h: 10.0),
                  PageNumberWidget(
                    lastPage: states.lastpage,
                    currentPage: states.currentpage,
                    type: "Absencesrequest",
                  ),
                ],
              );
              // }
            } else if (states is AbsencesErrorStates) {
              print(states.toString());
              if (states.error == "order Absence not found") {
                return SizedBox(
                  width: 600.w,
                  height: 300.w,
                  child: Center(
                    child: CustomText(
                      text: AppStrings.requestAbsencesNotFound.tr(),
                      fontSize: 17,
                      fontW: FontWeight.w600,
                    ),
                  ),
                );
              } else {
                return SizedBox(
                  width: 600.w,
                  height: 300.w,
                  child: Center(
                    child: CustomText(
                      text: AppStrings.requestAbsencesNotFound.tr(),
                      fontSize: 17,
                      fontW: FontWeight.w600,
                    ),
                  ),
                );
              }
            } else {
              return const SizedBox();
            }
          },
        ),
      ),
    );
  }
}
