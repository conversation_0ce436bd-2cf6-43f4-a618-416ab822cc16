import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:logger/logger.dart';
import 'dart:async';

// Bloc imports
import 'package:bus/bloc/add_remove_student_to_bus_cubit/add_remove_student_to_bus_cubit.dart';
import 'package:bus/bloc/add_remove_student_to_bus_cubit/add_remove_student_to_bus_states.dart';
import 'package:bus/bloc/buses_cubit/buses_cubit.dart';
import 'package:bus/bloc/student_bus_cubit/student_bus_cubit.dart';
import 'package:bus/bloc/student_bus_cubit/student_bus_states.dart';

// Model imports
import 'package:bus/data/models/student_models/student_model.dart';

// Config imports
import 'package:bus/config/theme_colors.dart';
import 'package:bus/translations/local_keys.g.dart';


// Widget imports
import 'widgets/bus_filter_widget.dart';
import 'widgets/student_search_widget.dart';
import 'widgets/student_card_widget.dart';
import 'widgets/bus_info_header.dart';

class StudentToBusScreen extends StatefulWidget {
  final String busId;

  const StudentToBusScreen({
    Key? key,
    required this.busId,
  }) : super(key: key);

  @override
  State<StudentToBusScreen> createState() => _StudentToBusScreenState();
}

class _StudentToBusScreenState extends State<StudentToBusScreen> {
  // Controllers and Services
  late ScrollController _scrollController;
  late TextEditingController _searchController;
  final Logger logger = Logger();
  Timer? _searchDebounce;

  // State variables
  List<StudentModel> selectedStudents = [];
  List<StudentModel> filteredStudents = []; // الطلاب المفلترين للعرض
  int? selectedBusId;
  int currentPage = 1;
  bool isLoadingMore = false;
  bool hasMoreData = true;

  @override
  void initState() {
    super.initState();
    _initializeControllers();
    _loadInitialData();
  }

  void _initializeControllers() {
    _scrollController = ScrollController();
    _scrollController.addListener(_onScroll);
    _searchController = TextEditingController();
  }

  void _loadInitialData() {
    context.read<BusesCubit>().getAllBuses();
    _loadEmptyBusStudents(page: 1);
  }

  @override
  void dispose() {
    _scrollController.dispose();
    _searchController.dispose();
    _searchDebounce?.cancel();
    super.dispose();
  }

  // Data loading methods
  Future<void> _loadEmptyBusStudents({required int page}) async {
    logger.i("🔄 Loading students for page $page (selectedBusId: $selectedBusId)");

    if (page == 1) {
      setState(() {
        currentPage = 1;
        hasMoreData = true;
      });
      logger.i("🔄 Reset to page 1 - clearing pagination state");
    }

    context.read<StudentBusCubit>().getEmptyBusStudents(
      withoutBusId: int.parse(widget.busId),
      withBusId: selectedBusId,
      page: page,
    );
  }

  void _onScroll() {
    if (_scrollController.position.pixels >=
            _scrollController.position.maxScrollExtent - 200 &&
        !isLoadingMore &&
        hasMoreData) {
      _loadMoreStudents();
    }
  }

  Future<void> _loadMoreStudents() async {
    if (isLoadingMore || !hasMoreData) return;

    setState(() {
      isLoadingMore = true;
    });

    final nextPage = currentPage + 1;
    await _loadEmptyBusStudents(page: nextPage);

    setState(() {
      currentPage = nextPage;
      isLoadingMore = false;
    });
  }

  // Event handlers
  void _onBusFilterChanged(int? busId) async {
    setState(() {
      selectedStudents.clear();
      selectedBusId = busId == 0 ? null : busId;
      filteredStudents.clear();
      currentPage = 1;
      hasMoreData = true;
    });

    // مسح البيانات في StudentBusCubit عند تغيير الفلتر
    context.read<StudentBusCubit>().clearData();

    HapticFeedback.selectionClick();
    await _loadEmptyBusStudents(page: 1);
  }

  void _onSearchChanged(String query) {
    // Cancel previous timer
    if (_searchDebounce?.isActive ?? false) _searchDebounce!.cancel();

    // Start new timer for local search
    _searchDebounce = Timer(const Duration(milliseconds: 300), () {
      // الحصول على البيانات الحالية من StudentBusCubit
      final currentState = context.read<StudentBusCubit>().state;
      if (currentState is StudentBusSuccessStates) {
        final allStudents = (currentState.studentModel ?? []).where((s) => s != null).cast<StudentModel>().toList();
        _performLocalSearch(allStudents);
      }
    });
  }

  void _performLocalSearch(List<StudentModel> allStudents) {
    setState(() {
      if (_searchController.text.isEmpty) {
        // إذا كان البحث فارغ، اعرض جميع الطلاب
        filteredStudents = List.from(allStudents);
      } else {
        // فلتر الطلاب بناءً على النص المدخل
        final searchQuery = _searchController.text.toLowerCase();
        filteredStudents = allStudents.where((student) {
          final studentName = (student.name ?? '').toLowerCase();
          return studentName.contains(searchQuery);
        }).toList();
      }

      logger.i("🔍 Search applied: ${filteredStudents.length} students shown (from ${allStudents.length} total)");
    });
  }



  void _onClearSearch() {
    // Cancel any pending search
    if (_searchDebounce?.isActive ?? false) _searchDebounce!.cancel();

    _searchController.clear();

    // الحصول على البيانات الحالية من StudentBusCubit
    final currentState = context.read<StudentBusCubit>().state;
    if (currentState is StudentBusSuccessStates) {
      final allStudents = (currentState.studentModel ?? []).where((s) => s != null).cast<StudentModel>().toList();
      _performLocalSearch(allStudents);
    }
  }

  void _onToggleStudentSelection(StudentModel student) {
    setState(() {
      if (selectedStudents.any((s) => s.id == student.id)) {
        selectedStudents.removeWhere((s) => s.id == student.id);
      } else {
        selectedStudents.add(student);
      }
    });
    HapticFeedback.lightImpact();
  }

  void _onClearAllSelections() {
    setState(() {
      selectedStudents.clear();
    });
    HapticFeedback.lightImpact();
  }

  Future<void> _onConfirmSelection() async {
    if (selectedStudents.isEmpty) return;

    final studentIds = selectedStudents.map((s) => s.id.toString()).toList();

    context.read<AddRemoveStudentToBusCubit>().addStudents(
      busId: widget.busId,
      studentIds: studentIds,
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey.shade50,
      appBar: _buildAppBar(),
      body: _buildBody(),
      floatingActionButton: _buildFloatingActionButton(),
    );
  }

  PreferredSizeWidget _buildAppBar() {
    return AppBar(
      title: Text(
        AppStrings.addStudentToBus.tr(),
        style: const TextStyle(
          fontWeight: FontWeight.bold,
          fontSize: 20,
        ),
      ),
      backgroundColor: TColor.mainColor,
      foregroundColor: Colors.white,
      elevation: 0,
      actions: [
        if (selectedStudents.isNotEmpty)
          Container(
            margin: const EdgeInsets.only(right: 16),
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
            decoration: BoxDecoration(
              color: Colors.white.withValues(alpha: 0.2),
              borderRadius: BorderRadius.circular(20),
            ),
            child: Text(
              '${selectedStudents.length}',
              style: const TextStyle(
                fontWeight: FontWeight.bold,
                fontSize: 16,
              ),
            ),
          ),
      ],
    );
  }

  Widget _buildBody() {
    return Column(
      children: [
        BusInfoHeader(busId: widget.busId),
        _buildFiltersSection(),
        Expanded(child: _buildStudentsList()),
      ],
    );
  }

  Widget _buildFiltersSection() {
    return Container(
      color: Colors.white,
      child: Column(
        children: [
          const SizedBox(height: 16),
          BusFilterWidget(
            currentBusId: widget.busId,
            selectedBusId: selectedBusId,
            onBusSelected: _onBusFilterChanged,
            logger: logger,
          ),
          const SizedBox(height: 16),
          StudentSearchWidget(
            controller: _searchController,
            onSearchChanged: _onSearchChanged,
            onClearSearch: _onClearSearch,
          ),
          const SizedBox(height: 16),
        ],
      ),
    );
  }

  Widget _buildStudentsList() {
    return BlocConsumer<StudentBusCubit, StudentBusStates>(
      listener: _handleStudentBusStateChanges,
      builder: (context, state) {
        if (state is StudentBusLoadingStates && currentPage == 1) {
          return _buildLoadingState();
        } else if (state is StudentBusSuccessStates) {
          return _buildSuccessState(state);
        } else if (state is StudentBusErrorStates) {
          return _buildErrorState();
        }
        return const SizedBox.shrink();
      },
    );
  }

  void _handleStudentBusStateChanges(BuildContext context, StudentBusStates state) {
    if (state is StudentBusSuccessStates) {
      final students = state.studentModel ?? [];

      // إضافة logging لمراقبة البيانات
      logger.i("📊 Received ${students.length} students from API for page $currentPage");

      setState(() {
        // الحصول على جميع الطلاب من الـ state
        final allStudents = students.where((s) => s != null).cast<StudentModel>().toList();
        logger.i("🔄 Total students in state: ${allStudents.length}");

        // تطبيق البحث المحلي على البيانات
        _performLocalSearch(allStudents);

        // التحقق من وجود المزيد من البيانات
        if (currentPage > 1 && students.length < 10) {
          hasMoreData = false;
        }
      });
    }
  }

  Widget _buildLoadingState() {
    return const Center(
      child: Padding(
        padding: EdgeInsets.all(32),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            CircularProgressIndicator(color: TColor.mainColor),
            SizedBox(height: 16),
            Text(
              'Loading students...',
              style: TextStyle(
                fontSize: 16,
                color: Colors.grey,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSuccessState(StudentBusSuccessStates state) {
    final allStudents = (state.studentModel ?? []).where((s) => s != null).cast<StudentModel>().toList();

    // استخدام filteredStudents بدلاً من البيانات من الـ state
    if (filteredStudents.isEmpty && allStudents.isNotEmpty) {
      return _buildNoSearchResultsState();
    }

    if (allStudents.isEmpty) {
      return _buildEmptyState();
    }

    return RefreshIndicator(
      onRefresh: () async {
        // مسح البيانات في StudentBusCubit عند الـ refresh
        context.read<StudentBusCubit>().clearData();
        setState(() {
          filteredStudents.clear();
          currentPage = 1;
          hasMoreData = true;
        });
        await _loadEmptyBusStudents(page: 1);
      },
      color: TColor.mainColor,
      child: ListView.builder(
        controller: _scrollController,
        padding: const EdgeInsets.only(bottom: 100),
        itemCount: filteredStudents.length + (isLoadingMore ? 1 : 0),
        itemBuilder: (context, index) {
          if (index == filteredStudents.length) {
            return _buildLoadingMoreIndicator();
          }

          final student = filteredStudents[index];

          final isSelected = selectedStudents.any((s) => s.id == student.id!);

          return StudentCardWidget(
            student: student,
            isSelected: isSelected,
            onToggleSelection: _onToggleStudentSelection,
          );
        },
      ),
    );
  }

  Widget _buildNoSearchResultsState() {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(32),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Container(
              padding: const EdgeInsets.all(24),
              decoration: BoxDecoration(
                color: Colors.orange.shade50,
                borderRadius: BorderRadius.circular(50),
              ),
              child: Icon(
                Icons.search_off,
                size: 64,
                color: Colors.orange.shade400,
              ),
            ),
            const SizedBox(height: 24),
            Text(
              AppStrings.noSearchResults.tr(),
              style: TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.bold,
                color: Colors.grey.shade600,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              'No students found matching "${_searchController.text}"',
              style: TextStyle(
                fontSize: 14,
                color: Colors.grey.shade500,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 24),
            ElevatedButton.icon(
              onPressed: _onClearSearch,
              icon: const Icon(Icons.clear),
              label: const Text('Clear Search'),
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.orange.shade400,
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(
                  horizontal: 24,
                  vertical: 12,
                ),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(32),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Container(
              padding: const EdgeInsets.all(24),
              decoration: BoxDecoration(
                color: Colors.grey.shade100,
                borderRadius: BorderRadius.circular(50),
              ),
              child: Icon(
                Icons.school_outlined,
                size: 64,
                color: Colors.grey.shade400,
              ),
            ),
            const SizedBox(height: 24),
            Text(
              'No students found',
              style: TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.bold,
                color: Colors.grey.shade600,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              'Try adjusting your filter criteria',
              style: TextStyle(
                fontSize: 14,
                color: Colors.grey.shade500,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 24),
            ElevatedButton.icon(
              onPressed: () {
                // مسح البيانات في StudentBusCubit عند الـ refresh
                context.read<StudentBusCubit>().clearData();
                setState(() {
                  filteredStudents.clear();
                  currentPage = 1;
                  hasMoreData = true;
                });
                _loadEmptyBusStudents(page: 1);
              },
              icon: const Icon(Icons.refresh),
              label: const Text('Refresh'),
              style: ElevatedButton.styleFrom(
                backgroundColor: TColor.mainColor,
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(
                  horizontal: 24,
                  vertical: 12,
                ),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildErrorState() {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(32),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Container(
              padding: const EdgeInsets.all(24),
              decoration: BoxDecoration(
                color: Colors.red.shade50,
                borderRadius: BorderRadius.circular(50),
              ),
              child: Icon(
                Icons.error_outline,
                size: 64,
                color: Colors.red.shade400,
              ),
            ),
            const SizedBox(height: 24),
            Text(
              'Failed to load students',
              style: TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.bold,
                color: Colors.red.shade600,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              'Please check your connection and try again',
              style: TextStyle(
                fontSize: 14,
                color: Colors.grey.shade500,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 24),
            ElevatedButton.icon(
              onPressed: () {
                // مسح البيانات في StudentBusCubit عند إعادة المحاولة
                context.read<StudentBusCubit>().clearData();
                setState(() {
                  filteredStudents.clear();
                  currentPage = 1;
                  hasMoreData = true;
                });
                _loadEmptyBusStudents(page: 1);
              },
              icon: const Icon(Icons.refresh),
              label: const Text('Try Again'),
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.red.shade400,
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(
                  horizontal: 24,
                  vertical: 12,
                ),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildLoadingMoreIndicator() {
    return Container(
      padding: const EdgeInsets.all(16),
      child: const Center(
        child: CircularProgressIndicator(
          color: TColor.mainColor,
          strokeWidth: 2,
        ),
      ),
    );
  }



  Widget _buildFloatingActionButton() {
    return BlocListener<AddRemoveStudentToBusCubit, AddRemoveStudentToBusStates>(
      listener: _handleAddRemoveStudentStateChanges,
      child: selectedStudents.isNotEmpty
          ? FloatingActionButton.extended(
              onPressed: _onConfirmSelection,
              backgroundColor: TColor.mainColor,
              foregroundColor: Colors.white,
              icon: const Icon(Icons.check_circle),
              label: Text('Add ${selectedStudents.length} Students'),
            )
          : const SizedBox.shrink(),
    );
  }

  void _handleAddRemoveStudentStateChanges(
    BuildContext context,
    AddRemoveStudentToBusStates state,
  ) {
    if (state is AddRemoveStudentToBusSuccessStates) {
      _showSuccessMessage();
      _onClearAllSelections();
      // مسح البيانات في StudentBusCubit بعد إضافة الطلاب بنجاح
      context.read<StudentBusCubit>().clearData();
      setState(() {
        filteredStudents.clear();
        currentPage = 1;
        hasMoreData = true;
      });
      _loadEmptyBusStudents(page: 1);
    } else if (state is AddRemoveStudentToBusErrorStates) {
      _showErrorMessage(state.error?.toString() ?? 'Unknown error occurred');
    }
  }

  void _showSuccessMessage() {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Row(
          children: [
            const Icon(Icons.check_circle, color: Colors.white),
            const SizedBox(width: 12),
            Text('Successfully added ${selectedStudents.length} students to bus'),
          ],
        ),
        backgroundColor: Colors.green,
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      ),
    );
  }

  void _showErrorMessage(String error) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Row(
          children: [
            const Icon(Icons.error, color: Colors.white),
            const SizedBox(width: 12),
            Expanded(child: Text('Error: $error')),
          ],
        ),
        backgroundColor: Colors.red,
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
        action: SnackBarAction(
          label: 'Retry',
          textColor: Colors.white,
          onPressed: _onConfirmSelection,
        ),
      ),
    );
  }
}
