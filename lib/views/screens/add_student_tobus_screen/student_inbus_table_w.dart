import 'package:bus/config/theme_colors.dart';
import 'package:bus/data/models/student_models/student_model.dart';
import 'package:bus/views/screens/add_student_tobus_screen/student_inbus_table_row.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:bus/translations/local_keys.g.dart';

class StudentInBusTable extends StatefulWidget {
  final List<StudentModel?>? studentInfoModels;
  final Function(StudentModel)? onTapDowntorow;
  final bool isAdd;
  StudentInBusTable({
    Key? key,
    this.onTapDowntorow,
    this.studentInfoModels,
    this.isAdd = false,
  }) : super(key: key);

  @override
  State<StudentInBusTable> createState() => _StudentInBusTableState();
}

class _StudentInBusTableState extends State<StudentInBusTable> {
  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: 10.w),
      child: ClipRRect(
        borderRadius: BorderRadius.all(Radius.circular(10.r)),
        child: Table(
          columnWidths: const {
            0: FlexColumnWidth(3),
            1: FlexColumnWidth(2),
            2: FlexColumnWidth(2),
            3: FlexColumnWidth(1),
          },
          border: TableBorder.all(
            color: TColor.tabColors,
            borderRadius: BorderRadius.circular(15.r),
          ),
          children:
              List.generate(1 + widget.studentInfoModels!.length, (index) {
            if (index == 0) {
              return StudentInBusTableRow(cell: [
                AppStrings.name.tr(),
                AppStrings.busName.tr(),
                AppStrings.grade.tr(),
                widget.isAdd ? AppStrings.add.tr() : AppStrings.delete.tr(),
              ], header: true)
                  .build(context);
            } else {
              final newStudentInfoModels = widget.studentInfoModels![index - 1];
              return StudentInBusTableRow(
                isAdd: widget.isAdd,
                studentModel: newStudentInfoModels,
                onTapDown: (p0) {
                  widget.onTapDowntorow!(p0);
                },
                cell: [
                  newStudentInfoModels?.name,
                  newStudentInfoModels?.bus?.name,
                  newStudentInfoModels?.grade?.name,
                  widget.isAdd ? Icons.add : Icons.remove,
                ],
              ).build(context);
            }
          }),
        ),
      ),
    );
  }
}
