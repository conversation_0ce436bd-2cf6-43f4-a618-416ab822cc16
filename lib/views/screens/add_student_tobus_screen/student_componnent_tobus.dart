import 'package:bus/config/theme_colors.dart';
import 'package:bus/data/models/student_models/student_model.dart';
import 'package:flutter/material.dart';

class StudentComponnentToBus extends StatelessWidget {
  StudentComponnentToBus(
      {super.key, required this.student, required this.onPressedfunc});
  final Function(StudentModel) onPressedfunc;
  final StudentModel student;
  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        border: Border.all(),
        // color: Colors.grey.withOpacity(.3),
      ),
      margin: const EdgeInsets.only(bottom: 5, left: 50, right: 50),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceAround,
        children: [
          Container(
            child: Text(
              student.name!,
            ),
          ),
          IconButton(
              onPressed: () {
                try {
                  this.onPressedfunc(student);
                } catch (e) {
                  print(e);
                }
              },
              icon: Container(
                height: 50,
                width: 50,
                decoration: BoxDecoration(
                    color: TColor.mainColor,
                    borderRadius: BorderRadius.circular(50)),
                child: const Icon(
                  Icons.add,
                  color: Colors.white,
                ),
              ))
        ],
      ),
    );
  }
}
