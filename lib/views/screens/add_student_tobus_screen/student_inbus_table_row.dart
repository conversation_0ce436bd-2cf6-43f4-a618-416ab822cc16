import 'package:bus/data/models/student_models/student_model.dart';
import 'package:bus/views/custom_widgets/custom_text.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import '../../../config/theme_colors.dart';

class StudentInBusTableRow extends TableRow {
  const StudentInBusTableRow(
      {Key? key,
      this.cell,
      this.header = false,
      this.isIcon = true,
      this.onTapDown,
      this.studentModel,
      this.isAdd=false,
      });

  final List<dynamic>? cell;
  final bool header;
  final bool isIcon;
  final StudentModel? studentModel;
  final void Function(StudentModel)? onTapDown;
  final bool isAdd;

  TableRow build(BuildContext context) {
    return TableRow(
      children: cell!.map(
        (e) {
          int index = cell!.indexOf(e);
          return header == true
              ? Container(
                  height: 59.w,
                  padding: EdgeInsets.symmetric(horizontal: 5.w),
                  decoration: BoxDecoration(
                    color: TColor.mainColor,
                    border: Border.all(color: TColor.mainColor, width: 0.1),
                  ),
                  child: Center(
                    child: FittedBox(
                      child: CustomText(
                        text: e,
                        color: TColor.white,
                        fontSize: 18,
                        fontW: FontWeight.w400,
                      ),
                    ),
                  ),
                )
              : index == cell!.length - 1 && isIcon == true
                  ? SizedBox(
                      height: 59.w,
                      child: IconButton(
                          onPressed: () {
                            if (onTapDown != null) {
                              onTapDown!(studentModel!);
                            }
                          },
                          icon: Icon(
                            e,
                            color: isAdd? Colors.green :Colors.red,
                          )))
                  : Container(
                      height: 59.w,
                      color: TColor.white,
                      child: Center(
                        child: CustomText(
                          text: e,
                          color: TColor.tabColors,
                          fontSize: 13,
                          fontW: FontWeight.w400,
                        ),
                      ),
                    );
        },
      ).toList(),
    );
  }
}
