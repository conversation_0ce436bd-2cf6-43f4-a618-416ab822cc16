import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:bus/data/models/student_models/student_model.dart';

class SelectedStudentsSummary extends StatefulWidget {
  final List<StudentModel> selectedStudents;
  final VoidCallback onClearAll;
  final VoidCallback onConfirmSelection;
  final bool isLoading;

  const SelectedStudentsSummary({
    Key? key,
    required this.selectedStudents,
    required this.onClearAll,
    required this.onConfirmSelection,
    this.isLoading = false,
  }) : super(key: key);

  @override
  State<SelectedStudentsSummary> createState() => _SelectedStudentsSummaryState();
}

class _SelectedStudentsSummaryState extends State<SelectedStudentsSummary>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _slideAnimation;
  late Animation<double> _fadeAnimation;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );
    _slideAnimation = Tween<double>(
      begin: 1.0,
      end: 0.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));
    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));

    if (widget.selectedStudents.isNotEmpty) {
      _animationController.forward();
    }
  }

  @override
  void didUpdateWidget(SelectedStudentsSummary oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (widget.selectedStudents.isNotEmpty && oldWidget.selectedStudents.isEmpty) {
      _animationController.forward();
    } else if (widget.selectedStudents.isEmpty && oldWidget.selectedStudents.isNotEmpty) {
      _animationController.reverse();
    }
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    if (widget.selectedStudents.isEmpty) {
      return const SizedBox.shrink();
    }

    return AnimatedBuilder(
      animation: _animationController,
      builder: (context, child) {
        return Transform.translate(
          offset: Offset(0, 100 * _slideAnimation.value),
          child: Opacity(
            opacity: _fadeAnimation.value,
            child: _buildSummaryCard(),
          ),
        );
      },
    );
  }

  Widget _buildSummaryCard() {
    return Container(
      margin: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            Colors.green.shade400,
            Colors.green.shade600,
          ],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(20),
        boxShadow: [
          BoxShadow(
            color: Colors.green.withValues(alpha: 0.3),
            blurRadius: 15,
            offset: const Offset(0, 5),
          ),
        ],
      ),
      child: Material(
        color: Colors.transparent,
        borderRadius: BorderRadius.circular(20),
        child: InkWell(
          borderRadius: BorderRadius.circular(20),
          onTap: _showSelectedStudentsDialog,
          child: Padding(
            padding: const EdgeInsets.all(20),
            child: Column(
              children: [
                _buildHeader(),
                const SizedBox(height: 16),
                _buildStudentsList(),
                const SizedBox(height: 16),
                _buildActionButtons(),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildHeader() {
    return Row(
      children: [
        Container(
          padding: const EdgeInsets.all(12),
          decoration: BoxDecoration(
            color: Colors.white.withValues(alpha: 0.2),
            borderRadius: BorderRadius.circular(12),
          ),
          child: const Icon(
            Icons.group,
            color: Colors.white,
            size: 24,
          ),
        ),
        const SizedBox(width: 16),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                '${widget.selectedStudents.length} Students Selected',
                style: const TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: Colors.white,
                ),
              ),
              Text(
                'Ready to transfer to bus',
                style: TextStyle(
                  fontSize: 14,
                  color: Colors.white.withValues(alpha: 0.9),
                ),
              ),
            ],
          ),
        ),
        IconButton(
          onPressed: () {
            widget.onClearAll();
            HapticFeedback.lightImpact();
          },
          icon: const Icon(
            Icons.clear_all,
            color: Colors.white,
            size: 24,
          ),
          tooltip: 'Clear all selections',
        ),
      ],
    );
  }

  Widget _buildStudentsList() {
    final displayStudents = widget.selectedStudents.take(3).toList();
    final remainingCount = widget.selectedStudents.length - displayStudents.length;

    return Column(
      children: [
        ...displayStudents.map((student) => _buildStudentItem(student)),
        if (remainingCount > 0)
          Container(
            padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 16),
            margin: const EdgeInsets.only(top: 8),
            decoration: BoxDecoration(
              color: Colors.white.withValues(alpha: 0.2),
              borderRadius: BorderRadius.circular(12),
            ),
            child: Row(
              children: [
                const Icon(
                  Icons.more_horiz,
                  color: Colors.white,
                  size: 20,
                ),
                const SizedBox(width: 8),
                Text(
                  'and $remainingCount more students',
                  style: TextStyle(
                    color: Colors.white.withValues(alpha: 0.9),
                    fontSize: 14,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ],
            ),
          ),
      ],
    );
  }

  Widget _buildStudentItem(StudentModel student) {
    return Container(
      padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 16),
      margin: const EdgeInsets.only(bottom: 8),
      decoration: BoxDecoration(
        color: Colors.white.withValues(alpha: 0.2),
        borderRadius: BorderRadius.circular(12),
      ),
      child: Row(
        children: [
          CircleAvatar(
            radius: 16,
            backgroundColor: Colors.white.withValues(alpha: 0.3),
            child: Text(
              (student.name ?? '?').substring(0, 1).toUpperCase(),
              style: const TextStyle(
                color: Colors.white,
                fontWeight: FontWeight.bold,
                fontSize: 14,
              ),
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  student.name ?? 'Unknown',
                  style: const TextStyle(
                    color: Colors.white,
                    fontWeight: FontWeight.w600,
                    fontSize: 14,
                  ),
                ),
                if (student.grade?.name != null)
                  Text(
                    student.grade!.name!,
                    style: TextStyle(
                      color: Colors.white.withValues(alpha: 0.8),
                      fontSize: 12,
                    ),
                  ),
              ],
            ),
          ),
          IconButton(
            onPressed: () {
              // Remove this specific student
              final updatedList = List<StudentModel>.from(widget.selectedStudents);
              updatedList.removeWhere((s) => s.id == student.id!);
              // This would need to be handled by parent widget
              HapticFeedback.lightImpact();
            },
            icon: const Icon(
              Icons.remove_circle_outline,
              color: Colors.white,
              size: 20,
            ),
            padding: EdgeInsets.zero,
            constraints: const BoxConstraints(
              minWidth: 32,
              minHeight: 32,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildActionButtons() {
    return Row(
      children: [
        Expanded(
          child: ElevatedButton.icon(
            onPressed: widget.isLoading ? null : widget.onClearAll,
            icon: const Icon(Icons.clear_all, size: 18),
            label: const Text('Clear All'),
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.white.withValues(alpha: 0.2),
              foregroundColor: Colors.white,
              padding: const EdgeInsets.symmetric(vertical: 12),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12),
              ),
            ),
          ),
        ),
        const SizedBox(width: 12),
        Expanded(
          flex: 2,
          child: ElevatedButton.icon(
            onPressed: widget.isLoading ? null : widget.onConfirmSelection,
            icon: widget.isLoading
                ? const SizedBox(
                    width: 18,
                    height: 18,
                    child: CircularProgressIndicator(
                      color: Colors.green,
                      strokeWidth: 2,
                    ),
                  )
                : const Icon(Icons.check_circle, size: 18),
            label: Text(
              widget.isLoading ? 'Processing...' : 'Confirm Transfer',
            ),
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.white,
              foregroundColor: Colors.green.shade600,
              padding: const EdgeInsets.symmetric(vertical: 12),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12),
              ),
            ),
          ),
        ),
      ],
    );
  }

  void _showSelectedStudentsDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('Selected Students (${widget.selectedStudents.length})'),
        content: SizedBox(
          width: double.maxFinite,
          child: ListView.builder(
            shrinkWrap: true,
            itemCount: widget.selectedStudents.length,
            itemBuilder: (context, index) {
              final student = widget.selectedStudents[index];
              return ListTile(
                leading: CircleAvatar(
                  backgroundColor: Colors.green.shade100,
                  child: Text(
                    (student.name ?? '?').substring(0, 1).toUpperCase(),
                    style: TextStyle(
                      color: Colors.green.shade700,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
                title: Text(student.name ?? 'Unknown'),
                subtitle: student.grade?.name != null
                    ? Text('Grade: ${student.grade!.name}')
                    : null,
                trailing: IconButton(
                  onPressed: () {
                    // Handle individual removal
                    Navigator.pop(context);
                  },
                  icon: const Icon(Icons.remove_circle_outline),
                ),
              );
            },
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Close'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              widget.onConfirmSelection();
            },
            child: const Text('Confirm Transfer'),
          ),
        ],
      ),
    );
  }
}
