import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:bus/data/models/student_models/student_model.dart';
import 'package:bus/config/theme_colors.dart';
import '../components/info_chip_widget.dart';

class StudentCardWidget extends StatefulWidget {
  final StudentModel student;
  final bool isSelected;
  final Function(StudentModel) onToggleSelection;
  final VoidCallback? onTap;

  const StudentCardWidget({
    Key? key,
    required this.student,
    required this.isSelected,
    required this.onToggleSelection,
    this.onTap,
  }) : super(key: key);

  @override
  State<StudentCardWidget> createState() => _StudentCardWidgetState();
}

class _StudentCardWidgetState extends State<StudentCardWidget>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _scaleAnimation;
  late Animation<double> _rotationAnimation;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );
    _scaleAnimation = Tween<double>(
      begin: 1.0,
      end: 0.95,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));
    _rotationAnimation = Tween<double>(
      begin: 0.0,
      end: 0.05,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: _animationController,
      builder: (context, child) {
        return Transform.scale(
          scale: _scaleAnimation.value,
          child: Transform.rotate(
            angle: _rotationAnimation.value,
            child: _buildCard(),
          ),
        );
      },
    );
  }

  Widget _buildCard() {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 4),
      child: Material(
        elevation: widget.isSelected ? 8 : 3,
        borderRadius: BorderRadius.circular(20),
        shadowColor: widget.isSelected
            ? TColor.mainColor.withValues(alpha: 0.3)
            : Colors.black.withValues(alpha: 0.1),
        child: Container(
          decoration: BoxDecoration(
            gradient: widget.isSelected
                ? LinearGradient(
                    colors: [
                      Colors.green.shade50,
                      Colors.green.shade100.withValues(alpha: 0.7),
                    ],
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                  )
                : LinearGradient(
                    colors: [
                      Colors.white,
                      Colors.grey.shade50,
                    ],
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                  ),
            borderRadius: BorderRadius.circular(20),
            border: Border.all(
              color: widget.isSelected
                  ? Colors.green.withValues(alpha: 0.4)
                  : Colors.grey.withValues(alpha: 0.2),
              width: widget.isSelected ? 2 : 1,
            ),
          ),
          child: _buildSimpleCard(),
        ),
      ),
    );
  }

  Widget _buildSimpleCard() {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
      child: Row(
        children: [
          _buildAvatar(),
          const SizedBox(width: 12),
          _buildTitle(),
          const SizedBox(width: 12),
          _buildActionButton(),
        ],
      ),
    );
  }

  Widget _buildAvatar() {
    return Hero(
      tag: 'student_${widget.student.id}',
      child: Container(
        width: 45,
        height: 45,
        decoration: BoxDecoration(
          gradient: LinearGradient(
            colors: widget.isSelected
                ? [Colors.green.shade400, Colors.green.shade600]
                : [Colors.blue.shade400, Colors.blue.shade600],
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
          ),
          borderRadius: BorderRadius.circular(22.5),
          boxShadow: [
            BoxShadow(
              color: widget.isSelected
                  ? Colors.green.withValues(alpha: 0.3)
                  : Colors.blue.withValues(alpha: 0.3),
              blurRadius: 6,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Center(
          child: Text(
            (widget.student.name ?? '?').substring(0, 1).toUpperCase(),
            style: const TextStyle(
              fontWeight: FontWeight.bold,
              fontSize: 18,
              color: Colors.white,
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildTitle() {
    return Expanded(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            widget.student.name ?? "",
            style: TextStyle(
              fontSize: 15,
              fontWeight: FontWeight.w600,
              color: widget.isSelected
                  ? Colors.green.shade700
                  : Colors.black87,
            ),
            overflow: TextOverflow.ellipsis,
            maxLines: 1,
          ),
          const SizedBox(height: 3),
          _buildQuickInfo(),
        ],
      ),
    );
  }

  Widget _buildQuickInfo() {
    return Row(
      children: [
        if (widget.student.grade?.name != null) ...[
          Flexible(
            child: InfoChipWidget(
              icon: Icons.school,
              text: widget.student.grade!.name!,
              color: Colors.blue,
              fontSize: 10,
            ),
          ),
          const SizedBox(width: 3),
        ],
        if (widget.student.bus?.name != null)
          Flexible(
            child: InfoChipWidget(
              icon: Icons.directions_bus,
              text: widget.student.bus!.name!,
              color: Colors.orange,
              fontSize: 10,
            ),
          ),
      ],
    );
  }

  Widget _buildActionButton() {
    return Container(
      width: 40,
      height: 40,
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: widget.isSelected
              ? [Colors.green.shade400, Colors.green.shade600]
              : [Colors.grey.shade300, Colors.grey.shade400],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(20),
        boxShadow: [
          BoxShadow(
            color: widget.isSelected
                ? Colors.green.withValues(alpha: 0.3)
                : Colors.grey.withValues(alpha: 0.3),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Material(
        color: Colors.transparent,
        borderRadius: BorderRadius.circular(20),
        child: InkWell(
          borderRadius: BorderRadius.circular(20),
          onTap: () {
            widget.onToggleSelection(widget.student);
            HapticFeedback.lightImpact();
          },
          child: Center(
            child: AnimatedSwitcher(
              duration: const Duration(milliseconds: 300),
              child: Icon(
                widget.isSelected ? Icons.check_circle : Icons.add_circle_outline,
                key: ValueKey(widget.isSelected),
                color: Colors.white,
                size: 18,
              ),
            ),
          ),
        ),
      ),
    );
  }


}
