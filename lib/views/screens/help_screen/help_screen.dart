import 'package:bus/bloc/question_helper_cubit/question_helper_cubit.dart';
import 'package:bus/bloc/question_helper_cubit/question_helper_states.dart';
import 'package:bus/config/theme_colors.dart';
import 'package:bus/translations/local_keys.g.dart';
import 'package:bus/utils/assets_utils.dart';
import 'package:bus/views/custom_widgets/custom_text.dart';
import 'package:bus/widgets/custom_appbar.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:bus/constant/path_route_name.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';

class HelpScreen extends StatelessWidget {
  static const String routeName = PathRouteName.helpScreen;
  const HelpScreen({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (context) => QuestionHelperCubit()..getQuestion(),
      child: Scaffold(
        appBar: CustomAppBar(
          titleWidget: CustomText(
            text: AppStrings.help.tr(),
            fontSize: 18,
            textAlign: TextAlign.center,
            fontW: FontWeight.w600,
            color: TColor.white,
          ),
          leftWidget: context.locale.toString() == "ar"
              ? InkWell(
                  onTap: () {
                    Navigator.pop(context);
                  },
                  child: SvgPicture.asset(AppAssets.arrowBack),
                )
              : InkWell(
                  onTap: () {
                    Navigator.pop(context);
                  },
                  child: SvgPicture.asset(
                    AppAssets.forwardArrow,
                    colorFilter:
                        const ColorFilter.mode(TColor.white, BlendMode.srcIn),
                    width: 25.w,
                    height: 25.w,
                  ),
                ),
        ),
        body: BlocBuilder<QuestionHelperCubit, QuestionHelperStates>(
            builder: (context, states) {
          if (states is QuestionHelperLoadingStates) {
            return SizedBox(
              width: 1.sw,
              height: 1.sh,
              child: const Center(
                child: CircularProgressIndicator(),
              ),
            );
          } else if (states is QuestionHelperSuccessStates) {
            return ListView.builder(
              itemCount: states.questionHelp!.data!.data!.length,
              shrinkWrap: true,
              itemBuilder: (context, index) {
                return Padding(
                  padding: EdgeInsets.symmetric(horizontal: 20.w),
                  child: ListTileTheme(
                    dense: true,
                    contentPadding: const EdgeInsets.all(0),
                    child: Theme(
                      data: Theme.of(context).copyWith(
                        dividerColor: Colors.transparent,
                      ),
                      child: ExpansionTile(
                        expandedCrossAxisAlignment: CrossAxisAlignment.start,
                        expandedAlignment: context.locale.toString() == "ar"
                            ? Alignment.topRight
                            : Alignment.topLeft,
                        title: CustomText(
                          text:
                              states.questionHelp!.data!.data![index].question,
                          maxLine: 10,
                          fontSize: 16,
                          fontW: FontWeight.w600,
                          color: Colors.black87,
                        ),
                        children: [
                          CustomText(
                            text:
                                states.questionHelp!.data!.data![index].answer,
                            fontSize: 16,
                            maxLine: 100,
                            fontW: FontWeight.w500,
                            color: Colors.black45,
                          ),
                        ],
                      ),
                    ),
                  ),
                );
              },
            );
          } else if (states is QuestionHelperErrorStates) {
            return const SizedBox.shrink();
          } else {
            return const SizedBox.shrink();
          }
        }),
      ),
    );
  }
}
