
import 'package:bus/bloc/attendance_cubit/attendance_cubit.dart';
import 'package:bus/bloc/attendance_cubit/attendance_states.dart';
import 'package:bus/bloc/route_cubit/route_cubit.dart';
import 'package:bus/bloc/route_cubit/route_states.dart';
import 'package:bus/config/theme_colors.dart';
import 'package:bus/data/models/attendance_models/student_attendance_model.dart';
import 'package:bus/translations/local_keys.g.dart';
import 'package:bus/utils/assets_utils.dart';
import 'package:bus/views/custom_widgets/custom_text.dart';
import 'package:bus/widgets/attendance_widgets/student_attendance_item.dart';
import 'package:bus/widgets/custom_appbar.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';

class TripDetailsScreen extends StatefulWidget {
  static const String routeName = '/trip-details';

  final int tripId;
  final String tripDate;
  final String busName;

  const TripDetailsScreen({
    super.key,
    required this.tripId,
    required this.tripDate,
    required this.busName,
  });

  @override
  State<TripDetailsScreen> createState() => _TripDetailsScreenState();
}

class _TripDetailsScreenState extends State<TripDetailsScreen>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;
  GoogleMapController? _mapController;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);

    // Load data for both tabs
    context.read<RouteCubit>().getTripRoute(tripId: widget.tripId);
    context.read<AttendanceCubit>().getTripAttendance(tripId: widget.tripId);
  }

  @override
  void dispose() {
    _tabController.dispose();
    _mapController?.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFF5F7FA),
      extendBodyBehindAppBar: true,
      appBar: CustomAppBar(
        titleWidget: CustomText(
          text: AppStrings.tripDetails.tr(),
          fontSize: 20,
          textAlign: TextAlign.center,
          fontW: FontWeight.w600,
          color: TColor.white,
        ),
        leftWidget: _buildBackArrow(context),
      ),
      body: Column(
        children: [
          // Header with trip info
          _buildHeader(),

          // Tab bar
          Container(
            margin: EdgeInsets.symmetric(horizontal: 16.w),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(30.r),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withOpacity(0.05),
                  spreadRadius: 1,
                  blurRadius: 10,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            child: TabBar(
              controller: _tabController,
              labelColor: Colors.white,
              unselectedLabelColor: TColor.dialogName,
              indicatorSize: TabBarIndicatorSize.tab,
              dividerColor: Colors.transparent,
              indicator: BoxDecoration(
                borderRadius: BorderRadius.circular(30.r),
                color: TColor.mainColor,
              ),
              labelStyle: TextStyle(
                fontSize: 14.sp,
                fontWeight: FontWeight.w600,
              ),
              unselectedLabelStyle: TextStyle(
                fontSize: 14.sp,
                fontWeight: FontWeight.w500,
              ),
              tabs: [
                Tab(text: AppStrings.routeMap.tr()),
                Tab(text: AppStrings.present.tr()),
                Tab(text: AppStrings.absent.tr()),
              ],
              padding: EdgeInsets.all(4.w),
            ),
          ),

          SizedBox(height: 16.h),

          // Tab content
          Expanded(
            child: TabBarView(
              controller: _tabController,
              children: [
                // Route Map Tab
                _buildRouteMapTab(),

                // Present Students Tab
                _buildPresentStudentsTab(),

                // Absent Students Tab
                _buildAbsentStudentsTab(),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildHeader() {
    return Container(
      width: double.infinity,
      // Use minimum height instead of fixed height
      constraints: BoxConstraints(minHeight: 180.h),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topCenter,
          end: Alignment.bottomCenter,
          colors: [
            TColor.mainColor,
            TColor.mainColor.withAlpha(204), // ~0.8 opacity
          ],
        ),
        borderRadius: BorderRadius.only(
          bottomLeft: Radius.circular(30.r),
          bottomRight: Radius.circular(30.r),
        ),
      ),
      child: SafeArea(
        child: Padding(
          padding: EdgeInsets.symmetric(vertical: 16.h),
          child: Column(
            mainAxisSize: MainAxisSize.min, // Use min size to prevent overflow
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              // Bus icon in a circle
              Container(
                width: 60.w, // Slightly smaller
                height: 60.w,
                decoration: BoxDecoration(
                  color: Colors.white.withAlpha(51), // ~0.2 opacity
                  shape: BoxShape.circle,
                ),
                child: Icon(
                  Icons.directions_bus_rounded,
                  size: 35.sp,
                  color: Colors.white,
                ),
              ),
              SizedBox(height: 10.h),

              // Bus name
              CustomText(
                text: widget.busName,
                fontSize: 20, // Slightly smaller
                fontW: FontWeight.w700,
                color: Colors.white,
              ),
              SizedBox(height: 6.h),

              // Trip date
              Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(
                    Icons.calendar_today_rounded,
                    size: 14.sp, // Slightly smaller
                    color: Colors.white.withAlpha(230), // ~0.9 opacity
                  ),
                  SizedBox(width: 6.w),
                  CustomText(
                    text: widget.tripDate,
                    fontSize: 14, // Slightly smaller
                    fontW: FontWeight.w500,
                    color: Colors.white.withAlpha(230), // ~0.9 opacity
                  ),
                ],
              ),

              SizedBox(height: 12.h),

              // Attendance summary
              BlocBuilder<AttendanceCubit, AttendanceStates>(
                builder: (context, state) {
                  if (state is AttendanceSuccessState) {
                    final attendanceCubit = context.read<AttendanceCubit>();
                    final presentCount = attendanceCubit.presentStudents.length;
                    final absentCount = attendanceCubit.absentStudents.length;
                    final totalCount = presentCount + absentCount;

                    return Container(
                      padding:
                          EdgeInsets.symmetric(horizontal: 12.w, vertical: 6.h),
                      decoration: BoxDecoration(
                        color: Colors.white.withAlpha(51), // ~0.2 opacity
                        borderRadius: BorderRadius.circular(20.r),
                      ),
                      child: Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Icon(
                            Icons.people_alt_rounded,
                            size: 16.sp,
                            color: Colors.white,
                          ),
                          SizedBox(width: 6.w),
                          CustomText(
                            text: "$totalCount ${AppStrings.studentsList.tr()}",
                            fontSize: 12,
                            fontW: FontWeight.w500,
                            color: Colors.white,
                          ),
                          SizedBox(width: 6.w),
                          Container(
                            width: 1,
                            height: 14.h,
                            color: Colors.white.withAlpha(128), // ~0.5 opacity
                          ),
                          SizedBox(width: 6.w),
                          Row(
                            children: [
                              Icon(
                                Icons.check_circle,
                                size: 14.sp,
                                color: Colors.green[200],
                              ),
                              SizedBox(width: 4.w),
                              CustomText(
                                text: "$presentCount",
                                fontSize: 12,
                                fontW: FontWeight.w500,
                                color: Colors.white,
                              ),
                            ],
                          ),
                          SizedBox(width: 6.w),
                          Row(
                            children: [
                              Icon(
                                Icons.cancel,
                                size: 14.sp,
                                color: Colors.red[200],
                              ),
                              SizedBox(width: 4.w),
                              CustomText(
                                text: "$absentCount",
                                fontSize: 12,
                                fontW: FontWeight.w500,
                                color: Colors.white,
                              ),
                            ],
                          ),
                        ],
                      ),
                    );
                  }

                  return const SizedBox();
                },
              ),
            ],
          ),
        ),
      ),
    );
  }


  // Route Map Tab
  Widget _buildRouteMapTab() {
    return BlocBuilder<RouteCubit, RouteStates>(
      builder: (context, state) {
        if (state is RouteLoadingState) {
          return Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                SizedBox(
                  width: 50.w,
                  height: 50.w,
                  child: const CircularProgressIndicator(
                    color: TColor.mainColor,
                    strokeWidth: 3,
                  ),
                ),
                SizedBox(height: 16.h),
                const CustomText(
                  text: "جاري تحميل بيانات المسار...",
                  fontSize: 16,
                  fontW: FontWeight.w500,
                  color: TColor.dialogName,
                ),
              ],
            ),
          );
        } else if (state is RouteSuccessState) {
          return Column(
            children: [
              // Map
              Expanded(
                flex: 3,
                child: Container(
                  margin: EdgeInsets.symmetric(horizontal: 16.w),
                  decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(20.r),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.black.withOpacity(0.05),
                        spreadRadius: 1,
                        blurRadius: 10,
                        offset: const Offset(0, 2),
                      ),
                    ],
                  ),
                  clipBehavior: Clip.antiAlias,
                  child: _buildMap(state),
                ),
              ),

              SizedBox(height: 8.h),

              // Route details
              Expanded(
                flex: 1,
                child: _buildRouteDetails(state),
              ),
            ],
          );
        } else if (state is RouteErrorState) {
          return Center(
            child: Container(
              padding: EdgeInsets.all(24.w),
              margin: EdgeInsets.all(16.w),
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(20.r),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withOpacity(0.05),
                    spreadRadius: 1,
                    blurRadius: 10,
                    offset: const Offset(0, 2),
                  ),
                ],
              ),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Container(
                    width: 80.w,
                    height: 80.w,
                    decoration: BoxDecoration(
                      color: Colors.red.withOpacity(0.1),
                      shape: BoxShape.circle,
                    ),
                    child: Icon(
                      Icons.error_outline_rounded,
                      size: 40.sp,
                      color: Colors.red,
                    ),
                  ),
                  SizedBox(height: 16.h),
                  CustomText(
                    text: "حدث خطأ",
                    fontSize: 18,
                    fontW: FontWeight.w600,
                    color: Colors.red,
                  ),
                  SizedBox(height: 8.h),
                  CustomText(
                    text: state.error,
                    fontSize: 14,
                    fontW: FontWeight.w500,
                    color: TColor.dialogName,
                    textAlign: TextAlign.center,
                  ),
                ],
              ),
            ),
          );
        }

        return Center(
          child: Container(
            padding: EdgeInsets.all(24.w),
            margin: EdgeInsets.all(16.w),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(20.r),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withOpacity(0.05),
                  spreadRadius: 1,
                  blurRadius: 10,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Container(
                  width: 80.w,
                  height: 80.w,
                  decoration: BoxDecoration(
                    color: TColor.mainColor.withOpacity(0.1),
                    shape: BoxShape.circle,
                  ),
                  child: Icon(
                    Icons.map_outlined,
                    size: 40.sp,
                    color: TColor.mainColor,
                  ),
                ),
                SizedBox(height: 16.h),
                CustomText(
                  text: AppStrings.noRouteData.tr(),
                  fontSize: 16,
                  fontW: FontWeight.w500,
                  color: TColor.dialogName,
                  textAlign: TextAlign.center,
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildMap(RouteSuccessState state) {
    if (state.routePoints.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Container(
              width: 80.w,
              height: 80.w,
              decoration: BoxDecoration(
                color: TColor.mainColor.withOpacity(0.1),
                shape: BoxShape.circle,
              ),
              child: Icon(
                Icons.map_outlined,
                size: 40.sp,
                color: TColor.mainColor,
              ),
            ),
            SizedBox(height: 16.h),
            CustomText(
              text: AppStrings.noRouteData.tr(),
              fontSize: 16,
              fontW: FontWeight.w500,
              color: TColor.dialogName,
              textAlign: TextAlign.center,
            ),
          ],
        ),
      );
    }

    // Calculate the center of the route
    final bounds = _calculateBounds(state.routePoints);
    final center = LatLng(
      (bounds.northeast.latitude + bounds.southwest.latitude) / 2,
      (bounds.northeast.longitude + bounds.southwest.longitude) / 2,
    );

    return Stack(
      children: [
        GoogleMap(
          initialCameraPosition: CameraPosition(
            target: center,
            zoom: 13,
          ),
          markers: state.markers,
          polylines: state.polylines,
          mapType: MapType.normal,
          myLocationEnabled: true,
          myLocationButtonEnabled: false,
          zoomControlsEnabled: false,
          onMapCreated: (GoogleMapController controller) {
            _mapController = controller;

            // Zoom to fit the route
            controller.animateCamera(
              CameraUpdate.newLatLngBounds(bounds, 50),
            );
          },
        ),

        // Custom zoom controls
        Positioned(
          right: 16,
          bottom: 16,
          child: Column(
            children: [
              _buildMapButton(
                icon: Icons.add,
                onPressed: () {
                  _mapController?.animateCamera(CameraUpdate.zoomIn());
                },
              ),
              SizedBox(height: 8.h),
              _buildMapButton(
                icon: Icons.remove,
                onPressed: () {
                  _mapController?.animateCamera(CameraUpdate.zoomOut());
                },
              ),
            ],
          ),
        ),

        // My location button
        Positioned(
          right: 16,
          top: 16,
          child: _buildMapButton(
            icon: Icons.my_location,
            onPressed: () {
              _mapController?.animateCamera(
                CameraUpdate.newLatLngBounds(bounds, 50),
              );
            },
          ),
        ),
      ],
    );
  }

  Widget _buildMapButton({
    required IconData icon,
    required VoidCallback onPressed,
  }) {
    return Container(
      width: 40.w,
      height: 40.w,
      decoration: BoxDecoration(
        color: Colors.white,
        shape: BoxShape.circle,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            spreadRadius: 1,
            blurRadius: 5,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: IconButton(
        icon: Icon(icon, size: 20.sp),
        onPressed: onPressed,
        color: TColor.mainColor,
        padding: EdgeInsets.zero,
      ),
    );
  }

  Widget _buildRouteDetails(RouteSuccessState state) {
    return Container(
      margin: EdgeInsets.symmetric(horizontal: 16.w),
      padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 8.h),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(20.r),
        boxShadow: [
          BoxShadow(
            color: Colors.black
                .withAlpha(13), // Using withAlpha instead of withOpacity
            spreadRadius: 1,
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Row(
        children: [
          Expanded(
            child: _buildDetailItem(
              icon: Icons.route,
              title: AppStrings.totalDistance.tr(),
              value:
                  "${state.routeData.total_distance?.toStringAsFixed(1) ?? '0'} ${AppStrings.kilometers.tr()}",
              color: Colors.blue,
            ),
          ),
          Container(
            height: 40.h,
            width: 1,
            color: Colors.grey
                .withAlpha(51), // Using withAlpha instead of withOpacity
          ),
          Expanded(
            child: _buildDetailItem(
              icon: Icons.access_time,
              title: AppStrings.estimatedTime.tr(),
              value:
                  "${state.routeData.estimated_time ?? '0'} ${AppStrings.minutes.tr()}",
              color: Colors.orange,
            ),
          ),
        ],
      ),
    );
  }

  // Present Students Tab
  Widget _buildPresentStudentsTab() {
    return BlocBuilder<AttendanceCubit, AttendanceStates>(
      builder: (context, state) {
        if (state is AttendanceLoadingState) {
          return Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                SizedBox(
                  width: 50.w,
                  height: 50.w,
                  child: const CircularProgressIndicator(
                    color: TColor.mainColor,
                    strokeWidth: 3,
                  ),
                ),
                SizedBox(height: 16.h),
                const CustomText(
                  text: "جاري تحميل بيانات الحضور...",
                  fontSize: 16,
                  fontW: FontWeight.w500,
                  color: TColor.dialogName,
                ),
              ],
            ),
          );
        } else if (state is AttendanceSuccessState) {
          final attendanceCubit = context.read<AttendanceCubit>();
          return Container(
            margin: EdgeInsets.symmetric(horizontal: 16.w),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(20.r),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withOpacity(0.05),
                  spreadRadius: 1,
                  blurRadius: 10,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            clipBehavior: Clip.antiAlias,
            child: _buildStudentsList(
              attendanceCubit.presentStudents,
              AppStrings.noStudentsPresent.tr(),
              Colors.green,
            ),
          );
        } else if (state is AttendanceErrorState) {
          return Center(
            child: Container(
              padding: EdgeInsets.all(24.w),
              margin: EdgeInsets.all(16.w),
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(20.r),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withOpacity(0.05),
                    spreadRadius: 1,
                    blurRadius: 10,
                    offset: const Offset(0, 2),
                  ),
                ],
              ),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Container(
                    width: 80.w,
                    height: 80.w,
                    decoration: BoxDecoration(
                      color: Colors.red.withOpacity(0.1),
                      shape: BoxShape.circle,
                    ),
                    child: Icon(
                      Icons.error_outline_rounded,
                      size: 40.sp,
                      color: Colors.red,
                    ),
                  ),
                  SizedBox(height: 16.h),
                  const CustomText(
                    text: "حدث خطأ",
                    fontSize: 18,
                    fontW: FontWeight.w600,
                    color: Colors.red,
                  ),
                  SizedBox(height: 8.h),
                  CustomText(
                    text: state.error,
                    fontSize: 14,
                    fontW: FontWeight.w500,
                    color: TColor.dialogName,
                    textAlign: TextAlign.center,
                  ),
                ],
              ),
            ),
          );
        }

        return const SizedBox();
      },
    );
  }

  // Absent Students Tab
  Widget _buildAbsentStudentsTab() {
    return BlocBuilder<AttendanceCubit, AttendanceStates>(
      builder: (context, state) {
        if (state is AttendanceLoadingState) {
          return Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                SizedBox(
                  width: 50.w,
                  height: 50.w,
                  child: const CircularProgressIndicator(
                    color: TColor.mainColor,
                    strokeWidth: 3,
                  ),
                ),
                SizedBox(height: 16.h),
                const CustomText(
                  text: "جاري تحميل بيانات الغياب...",
                  fontSize: 16,
                  fontW: FontWeight.w500,
                  color: TColor.dialogName,
                ),
              ],
            ),
          );
        } else if (state is AttendanceSuccessState) {
          final attendanceCubit = context.read<AttendanceCubit>();
          return Container(
            margin: EdgeInsets.symmetric(horizontal: 16.w),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(20.r),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withOpacity(0.05),
                  spreadRadius: 1,
                  blurRadius: 10,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            clipBehavior: Clip.antiAlias,
            child: _buildStudentsList(
              attendanceCubit.absentStudents,
              AppStrings.noStudentsAbsent.tr(),
              Colors.red,
            ),
          );
        } else if (state is AttendanceErrorState) {
          return Center(
            child: Container(
              padding: EdgeInsets.all(24.w),
              margin: EdgeInsets.all(16.w),
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(20.r),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withOpacity(0.05),
                    spreadRadius: 1,
                    blurRadius: 10,
                    offset: const Offset(0, 2),
                  ),
                ],
              ),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Container(
                    width: 80.w,
                    height: 80.w,
                    decoration: BoxDecoration(
                      color: Colors.red.withOpacity(0.1),
                      shape: BoxShape.circle,
                    ),
                    child: Icon(
                      Icons.error_outline_rounded,
                      size: 40.sp,
                      color: Colors.red,
                    ),
                  ),
                  SizedBox(height: 16.h),
                  const CustomText(
                    text: "حدث خطأ",
                    fontSize: 18,
                    fontW: FontWeight.w600,
                    color: Colors.red,
                  ),
                  SizedBox(height: 8.h),
                  CustomText(
                    text: state.error,
                    fontSize: 14,
                    fontW: FontWeight.w500,
                    color: TColor.dialogName,
                    textAlign: TextAlign.center,
                  ),
                ],
              ),
            ),
          );
        }

        return const SizedBox();
      },
    );
  }

  Widget _buildStudentsList(
      List<StudentAttendanceModel> students, String emptyMessage,
      [Color? statusColor]) {
    if (students.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Container(
              width: 80.w,
              height: 80.w,
              decoration: BoxDecoration(
                color: (statusColor ?? TColor.mainColor).withOpacity(0.1),
                shape: BoxShape.circle,
              ),
              child: Icon(
                Icons.people_outline,
                size: 40.sp,
                color: statusColor ?? TColor.mainColor,
              ),
            ),
            SizedBox(height: 16.h),
            CustomText(
              text: emptyMessage,
              fontSize: 16,
              fontW: FontWeight.w500,
              color: TColor.dialogName,
              textAlign: TextAlign.center,
            ),
          ],
        ),
      );
    }

    return ListView.builder(
      padding: EdgeInsets.all(16.w),
      itemCount: students.length,
      itemBuilder: (context, index) {
        final student = students[index];
        return Container(
          margin: EdgeInsets.only(bottom: 12.h),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(12.r),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withOpacity(0.03),
                spreadRadius: 1,
                blurRadius: 5,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: StudentAttendanceItem(student: student),
        );
      },
    );
  }

  Widget _buildDetailItem({
    required IconData icon,
    required String title,
    required String value,
    Color? color,
  }) {
    final iconColor = color ?? TColor.mainColor;
    // Create a transparent version of the color
    final bgColor =
        iconColor.withAlpha(26); // Approximately 0.1 opacity (26/255)

    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      mainAxisSize: MainAxisSize.min,
      children: [
        Container(
          width: 40.w,
          height: 40.w,
          decoration: BoxDecoration(
            color: bgColor,
            shape: BoxShape.circle,
          ),
          child: Icon(
            icon,
            size: 20.sp,
            color: iconColor,
          ),
        ),
        SizedBox(height: 6.h),
        CustomText(
          text: title,
          fontSize: 14,
          fontW: FontWeight.w500,
          color: TColor.dialogName,
          textAlign: TextAlign.center,
        ),
        SizedBox(height: 2.h),
        CustomText(
          text: value,
          fontSize: 16,
          fontW: FontWeight.w600,
          color: TColor.text,
          textAlign: TextAlign.center,
        ),
      ],
    );
  }

  LatLngBounds _calculateBounds(List<LatLng> points) {
    double minLat = points.first.latitude;
    double maxLat = points.first.latitude;
    double minLng = points.first.longitude;
    double maxLng = points.first.longitude;

    for (final point in points) {
      if (point.latitude < minLat) minLat = point.latitude;
      if (point.latitude > maxLat) maxLat = point.latitude;
      if (point.longitude < minLng) minLng = point.longitude;
      if (point.longitude > maxLng) maxLng = point.longitude;
    }

    return LatLngBounds(
      southwest: LatLng(minLat, minLng),
      northeast: LatLng(maxLat, maxLng),
    );
  }

  Widget _buildBackArrow(BuildContext context) {
    final isArabic = context.locale.toString() == "ar";
    return InkWell(
      onTap: () => Navigator.pop(context),
      child: SvgPicture.asset(
        isArabic ? AppAssets.arrowBack : AppAssets.forwardArrow,
        colorFilter: isArabic
            ? null
            : const ColorFilter.mode(TColor.white, BlendMode.srcIn),
        width: 25.w,
        height: 25.w,
      ),
    );
  }
}
