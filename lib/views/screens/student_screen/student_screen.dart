import 'package:bus/bloc/student_cubit/student_cubit.dart';
import 'package:bus/bloc/student_cubit/student_filter_cubit.dart';
import 'package:bus/bloc/student_cubit/student_states.dart';
import 'package:bus/config/theme_colors.dart';
import 'package:bus/constant/path_route_name.dart';
import 'package:bus/utils/assets_utils.dart';
import 'package:bus/views/custom_widgets/custom_form_field_border.dart';
import 'package:bus/views/custom_widgets/custom_text.dart';
import 'package:bus/widgets/custom_appbar.dart';
import 'package:bus/widgets/student_widgets/custom_table_w.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:logger/logger.dart';

import '../../../bloc/student_cubit/student_filter_states.dart';
import 'package:bus/translations/local_keys.g.dart';import '../../../utils/sized_box.dart';
import '../../../widgets/student_widgets/custom_icons_c_w.dart';

class StudentScreen extends StatefulWidget {
  static const String routeName = PathRouteName.student;

  const StudentScreen({Key? key}) : super(key: key);

  @override
  State<StudentScreen> createState() => _StudentScreenState();
}

class _StudentScreenState extends State<StudentScreen>
    with TickerProviderStateMixin {
  AnimationController? controller;
  AnimationController? controllerIcon;
  Animation<double>? opacity;
  TextEditingController? cSearchStudentName;
  final ScrollController? scrollController = ScrollController();
  bool searchCheck = false;

  @override
  void initState() {
    cSearchStudentName = TextEditingController();

    cSearchStudentName!.addListener(() {
      if (cSearchStudentName!.text.isEmpty) {
        searchCheck = false;
        setState(() {});
      }
    });

    super.initState();
    controller = AnimationController(
      duration: const Duration(milliseconds: 200),
      vsync: this,
    );
    controllerIcon = AnimationController(
      duration: const Duration(milliseconds: 500),
      vsync: this,
    );

    opacity = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(controller!);
    context.read<StudentCubit>().initScrollController(
        isFirst: false,
        scrollController: scrollController,
        setStates: () {
          setState(() {});
        });
  }

  @override
  void dispose() {
    controller?.dispose();
    controllerIcon?.dispose();
    super.dispose();
  }

  @override
  void setState(VoidCallback fn) {
    if (mounted) {
      super.setState(fn);
    }
  }

  toggleAnimation() {
    if (controller?.status == AnimationStatus.dismissed) {
      controller?.forward();
    } else if (controller?.status == AnimationStatus.completed) {
      controller?.reverse();
    }
  }

  toggleIcon() {
    if (controllerIcon?.status == AnimationStatus.dismissed) {
      controllerIcon?.forward();
    } else if (controllerIcon?.status == AnimationStatus.completed) {
      controllerIcon?.reverse();
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: CustomAppBar(
        titleWidget: CustomText(
          text: AppStrings.students.tr(),
          fontSize: 18,
          textAlign: TextAlign.center,
          fontW: FontWeight.w600,
          color: TColor.white,
        ),
        leftWidget: context.locale.toString() == "ar"
            ? InkWell(
                onTap: () {
                  Navigator.pop(context);
                },
                child: SvgPicture.asset(AppAssets.arrowBack),
              )
            : InkWell(
                onTap: () {
                  Navigator.pop(context);
                },
                child: SvgPicture.asset(
                  AppAssets.forwardArrow,
                  colorFilter:
                      const ColorFilter.mode(TColor.white, BlendMode.srcIn),
                  width: 25.w,
                  height: 25.w,
                ),
              ),
      ),
      body: SingleChildScrollView(
        controller: scrollController,
        child: SafeArea(
          child: Column(
            children: [
              Padding(
                padding: EdgeInsets.symmetric(horizontal: 20.w),
                child: Row(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    const CustomIconsCW(
                      type: "student",
                    ),
                    SBox(w: 20.w),
                    CustomFormFieldWithBorder(
                      controller: cSearchStudentName,
                      paddingLeft: 0,
                      paddingRight: 0,
                      formFieldWidth: 296.w,
                      height: true,
                      onFieldSubmitted: (String? value) {
                        searchCheck = true;
                        context
                            .read<StudentFilterCubit>()
                            .getStudentWithFilters(studentName: value ?? "");
                        setState(() {});
                      },
                      // hintText: hintText,
                      fillColor: TColor.fillFormFieldB,
                      borderColor: TColor.fillFormFieldB,
                      suffix: InkWell(
                        onTap: () {
                          searchCheck = true;

                          debugPrint(cSearchStudentName?.text);
                          context
                              .read<StudentFilterCubit>()
                              .getStudentWithFilters(
                                  studentName: cSearchStudentName?.text ?? "");
                          setState(() {});
                        },
                        child: const Icon(Icons.search),
                      ),
                    )
                  ],
                ),
              ),
              20.verticalSpace,
              searchCheck == true
                  ? BlocBuilder<StudentFilterCubit, StudentFilterState>(
                builder: (context, state) {
                  if (state is StudentFilterLoadingStates) {
                    return const Center(
                      child: CircularProgressIndicator(
                        color: TColor.mainColor,
                      ),
                    );
                  } else if (state is StudentFilterSuccessStates) {
                    Logger().w(state.searchData);
                    return Column(
                      children: [
                        20.verticalSpace,
                        CustomTableW(
                          studentInfoModels: state.searchData,
                        ),
                        20.verticalSpace,
                      ],
                    );
                  } else if (state is StudentFilterErrorStates) {
                    return SizedBox(
                      width: 300.w,
                      height: 300.w,
                      child: Center(
                        child: CustomText(
                          text: AppStrings.studentNotFound.tr(),
                          fontSize: 17,
                          fontW: FontWeight.w600,
                          color: TColor.mainColor,
                        ),
                      ),
                    );
                  } else {
                    return const SizedBox();
                  }
                },
              )

                  : BlocBuilder<StudentCubit, StudentState>(
                      buildWhen: (previous, current) =>
                          current is StudentSuccessStates,
                      builder: (context, state) {
                        if (state is StudentLoadingStates) {
                          return const Center(
                            child: CircularProgressIndicator(
                              color: TColor.mainColor,
                            ),
                          );
                        } else if (state is StudentNoStudentsStates) {
                          return Center(
                            child: CustomText(
                                text: AppStrings.studentNotFound.tr()),
                          );
                        } else if (state is StudentInitialStates) {
                          return const Center(
                            child: CircularProgressIndicator(),
                          );
                        } else if (state is StudentSuccessStates) {
                          return Column(
                            children: [
                              20.verticalSpace,
                              CustomTableW(
                                studentInfoModels:
                                    context.watch<StudentCubit>().data,
                              ),
                              20.verticalSpace,
                              context.watch<StudentCubit>().current_page! <
                                      context.watch<StudentCubit>().last_pages!
                                  ? Icon(
                                      Icons.arrow_downward_rounded,
                                      size: 40.sp,
                                      // color: Colors.r,
                                    )
                                  : const SizedBox.shrink(),
                              20.verticalSpace,
                            ],
                          );
                        } else if (state is StudentErrorStates) {
                          if (state.error == "students not found") {
                            return SizedBox(
                              width: 300.w,
                              height: 300.w,
                              child: Center(
                                child: CustomText(
                                  text: AppStrings.studentNotFound.tr(),
                                  fontSize: 17,
                                  fontW: FontWeight.w600,
                                ),
                              ),
                            );
                          } else {
                            return SizedBox(
                              width: 300.w,
                              height: 300.w,
                              child: Center(
                                child: CustomText(
                                  color: TColor.mainColor,
                                  text: AppStrings.studentNotFound.tr(),
                                  fontSize: 17,
                                  fontW: FontWeight.w600,
                                ),
                              ),
                            );
                          }
                        } else {
                          return const SizedBox();
                        }
                      },
                    ),
              30.verticalSpace,
            ],
          ),
        ),
      ),
    );
  }
}
