import 'package:bus/bloc/buses_cubit/buses_cubit.dart';
import 'package:bus/bloc/previous_trips_cubit/previous_trips_cubit.dart';
import 'package:bus/bloc/previous_trips_cubit/previous_trips_states.dart';
import 'package:bus/config/theme_colors.dart';
import 'package:bus/constant/path_route_name.dart';
import 'package:bus/data/models/trip_models/previous_trip_model.dart';
import 'package:bus/translations/local_keys.g.dart';
import 'package:bus/utils/assets_utils.dart';
import 'package:bus/utils/sized_box.dart';
import 'package:bus/views/custom_widgets/custom_text.dart';
import 'package:bus/views/screens/trip_details_screen/trip_details_screen.dart';
import 'package:bus/widgets/custom_appbar.dart';
import 'package:bus/widgets/previous_trips_widgets/previous_trip_card.dart';
import 'package:bus/widgets/previous_trips_widgets/previous_trips_filter.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';

class PreviousTripsScreen extends StatefulWidget {
  static const String routeName = PathRouteName.previousTripsScreen;

  const PreviousTripsScreen({super.key});

  @override
  State<PreviousTripsScreen> createState() => _PreviousTripsScreenState();
}

class _PreviousTripsScreenState extends State<PreviousTripsScreen> {
  final ScrollController _scrollController = ScrollController();

  @override
  void initState() {
    super.initState();
    // Initialize the cubits
    context.read<BusesCubit>().getBuses(pageNumber: 1, isFirst: true);
    context.read<PreviousTripsCubit>().getPreviousTrips(isFirst: true);

    // Set up scroll controller for pagination
    context.read<PreviousTripsCubit>().initScrollController(
          scrollController: _scrollController,
          setStates: () => setState(() {}),
        );
  }

  @override
  void dispose() {
    _scrollController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: CustomAppBar(
        titleWidget: CustomText(
          text: AppStrings.previousTrips.tr(),
          fontSize: 18,
          textAlign: TextAlign.center,
          fontW: FontWeight.w600,
          color: TColor.white,
        ),
        leftWidget: _buildBackArrow(context),
        rightWidget: IconButton(
          icon: const Icon(
            Icons.refresh,
            color: Colors.white,
          ),
          onPressed: () {
            context.read<PreviousTripsCubit>().getPreviousTrips(isFirst: true);
          },
        ),
      ),
      body: SafeArea(
        child: Column(
          children: [
            // Filter section
            const PreviousTripsFilter(),

            // Trips list
            Expanded(
              child: BlocBuilder<PreviousTripsCubit, PreviousTripsStates>(
                builder: (context, state) {
                  if (state is PreviousTripsLoadingState) {
                    return const Center(
                      child: CircularProgressIndicator(color: TColor.mainColor),
                    );
                  } else if (state is PreviousTripsSuccessState ||
                      state is PreviousTripsPaginationSuccessState ||
                      state is PreviousTripsFilterSuccessState) {
                    final trips = context.read<PreviousTripsCubit>().trips;

                    if (trips.isEmpty) {
                      return Center(
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Icon(
                              Icons.history,
                              size: 80.sp,
                              color: TColor.mainColor
                                  .withAlpha(179), // 0.7 * 255 = 179
                            ),
                            const SBox(h: 20),
                            CustomText(
                              text: AppStrings.noTripsYet.tr(),
                              fontSize: 18,
                              fontW: FontWeight.w500,
                              color: TColor.titleColor,
                            ),
                          ],
                        ),
                      );
                    }

                    return ListView.builder(
                      controller: _scrollController,
                      itemCount: trips.length +
                          (context.read<PreviousTripsCubit>().isLoading
                              ? 1
                              : 0),
                      padding: EdgeInsets.only(bottom: 20.h),
                      itemBuilder: (context, index) {
                        if (index >= trips.length) {
                          return Center(
                            child: Padding(
                              padding: EdgeInsets.symmetric(vertical: 20.h),
                              child: const CircularProgressIndicator(
                                  color: TColor.mainColor),
                            ),
                          );
                        }

                        // Reverse the order by accessing trips from the end
                        final reversedIndex = trips.length - 1 - index;
                        final trip = trips[reversedIndex];
                        return PreviousTripCard(
                          trip: trip,
                          onViewDetailsPressed: () => _viewTripDetails(trip),
                        );
                      },
                    );
                  } else if (state is PreviousTripsErrorState) {
                    return Center(
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Icon(
                            Icons.error_outline,
                            size: 60.sp,
                            color: TColor.error,
                          ),
                          const SBox(h: 20),
                          CustomText(
                            text: state.error,
                            fontSize: 16,
                            fontW: FontWeight.w500,
                            color: TColor.error,
                          ),
                        ],
                      ),
                    );
                  }

                  return const SizedBox();
                },
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildBackArrow(BuildContext context) {
    final isArabic = context.locale.toString() == "ar";
    return InkWell(
      onTap: () => Navigator.pop(context),
      child: SvgPicture.asset(
        isArabic ? AppAssets.arrowBack : AppAssets.forwardArrow,
        colorFilter: isArabic
            ? null
            : const ColorFilter.mode(TColor.white, BlendMode.srcIn),
        width: 25.w,
        height: 25.w,
      ),
    );
  }

  void _viewTripDetails(PreviousTripModel trip) {
    // Navigate to the trip details screen
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => TripDetailsScreen(
          tripId: trip.id ?? 0,
          tripDate: trip.date ?? '',
          busName: trip.busName ?? trip.bus?.name ?? '',
        ),
      ),
    );
  }
}
