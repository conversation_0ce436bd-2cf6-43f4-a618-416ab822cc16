import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';

import '../../../bloc/notifications_cubit/notifications_cubit.dart';
import '../../../config/theme_colors.dart';
import '../../../constant/path_route_name.dart';
import '../../../helper/custom_date_time.dart';
import 'package:bus/translations/local_keys.g.dart';import '../../../utils/assets_utils.dart';
import '../../../widgets/custom_appbar.dart';
import '../../custom_widgets/custom_text.dart';
import '../../custom_widgets/list_tile_widget.dart';

class NotificationsScreen extends StatefulWidget {
  const NotificationsScreen({Key? key}) : super(key: key);

  static const routeName = PathRouteName.notificationsScreen;

  @override
  State<NotificationsScreen> createState() => _NotificationsScreenState();
}

class _NotificationsScreenState extends State<NotificationsScreen> {
  final ScrollController? scrollController = ScrollController();

  @override
  void initState() {
    super.initState();
    context.read<NotificationsCubit>().initScrollController(
          isFirst: false,
          scrollController: scrollController,
          setStates: () => setState(() {}),
        );
  }

  @override
  void dispose() {
    scrollController?.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: CustomAppBar(
        titleWidget: CustomText(
          text: AppStrings.notifications.tr(),
          fontSize: 18,
          textAlign: TextAlign.center,
          fontW: FontWeight.w600,
          color: TColor.white,
        ),
        leftWidget: InkWell(
          onTap: () => Navigator.pop(context),
          child: SvgPicture.asset(
            context.locale.toString() == "ar"
                ? AppAssets.arrowBack
                : AppAssets.forwardArrow,
            colorFilter: context.locale.toString() == "ar"
                ? null
                : const ColorFilter.mode(TColor.white, BlendMode.srcIn),
            width: 25.w,
            height: 25.w,
          ),
        ),
      ),
      body: BlocBuilder<NotificationsCubit, NotificationsState>(
        buildWhen: (previous, current) => current is NotificationsSuccess,
        builder: (context, state) {
          if (state is NotificationsLoading) {
            return const Center(
              child: CircularProgressIndicator(color: TColor.mainColor),
            );
          } else if (state is NotificationsSuccess) {
            final notifications = context.watch<NotificationsCubit>().notificationData;
            if (notifications == null || notifications.isEmpty) {
              return const Center(child: Text("No Notifications"));
            }
            return Column(
              children: [
                Expanded(
                  child: ListView.separated(
                    physics: const BouncingScrollPhysics(),
                    shrinkWrap: true,
                    primary: false,
                    controller: scrollController,
                    itemCount: notifications.length + 1,
                    padding: EdgeInsets.symmetric(horizontal: 20.w),
                    itemBuilder: (context, index) {
                      if (index < notifications.length) {
                        final notification = notifications[index];
                        return ListTileWidget(
                          arrowIcon: false,
                          onTap: () {
                            // Handle notification tap
                          },
                          title:
                              "${notification.title}, ${notification.body}\n",
                          subtitle: CustomDateTime.formatTimeDifference(
                            context,
                            notification.createdAt!,
                          ),
                          leading: CircleAvatar(
                            backgroundColor: TColor.mainColor,
                            radius: 30.w,
                            child: Image.asset(
                              assetsImages("group1.png"),
                              width: 50.w,
                              height: 50.w,
                            ),
                          ),
                          leadingUrl: "",
                        );
                      } else {
                        return context
                                    .watch<NotificationsCubit>()
                                    .hasMoreData ==
                                true
                            ? const Center(
                                child: CircularProgressIndicator(
                                  color: TColor.mainColor,
                                ),
                              )
                            : const SizedBox();
                      }
                    },
                    separatorBuilder: (context, index) =>
                        const Divider(color: TColor.mainColor, thickness: 0.5),
                  ),
                ),
                SizedBox(height: 5.h),
                // Uncomment and configure PageNumberWidget if needed
                // PageNumberWidget(
                //   lastPage: cubit.notifications?.data?.lastPage ?? 0,
                //   currentPage: cubit.notifications?.data?.currentPage ?? 0,
                //   type: 'notification',
                // ),
                SizedBox(height: 20.h),
              ],
            );
          } else if (state is NotificationsError ||
              state is NotificationsInitial) {
            return const Center(child: Text("No Notifications"));
          } else {
            return const Center(child: Text("No Notifications"));
          }
        },
      ),
    );
  }
}
