import 'dart:math' as math;

import 'package:flutter/material.dart';

double _degreeToRad(double degrees) => degrees / 180.0 * math.pi;

extension Rotate on Widget {
  Widget rotate(
    double degrees, {
    Key? key,
    Alignment alignment = Alignment.center,
    Offset? origin,
  }) =>
      Transform.rotate(
        key: key,
        angle: _degreeToRad(degrees),
        alignment: alignment,
        origin: origin,
        child: this,
      );

  Widget rotate0({Key? key}) => Transform.rotate(
        key: key,
        angle: 0,
        child: this,
      );

  Widget rotate45({Key? key}) => Transform.rotate(
        key: key,
        angle: math.pi / 4,
        child: this,
      );

  Widget rotate60({Key? key}) => Transform.rotate(
        key: key,
        angle: math.pi / 3,
        child: this,
      );

  Widget rotate90({Key? key}) => Transform.rotate(
        key: key,
        angle: math.pi / 2,
        child: this,
      );

  Widget rotate180({Key? key}) => Transform.rotate(
        key: key,
        angle: math.pi,
        child: this,
      );

  Widget rotateN180({Key? key}) => Transform.rotate(
        key: key,
        angle: -math.pi,
        child: this,
      );

  Widget rotateN90({Key? key}) => Transform.rotate(
        key: key,
        angle: -math.pi / 2,
        child: this,
      );

  Widget rotateN45({Key? key}) => Transform.rotate(
        key: key,
        angle: -math.pi / 4,
        child: this,
      );

  Widget rotateN60({Key? key}) => Transform.rotate(
        key: key,
        angle: -math.pi / 3,
        child: this,
      );
}


extension Hide on String {
  String hidePartial({int begin = 0, int? end, String replace = '*'}) {
    final buffer = StringBuffer();
    if (length <= 1) {
      return this;
    }
    if (end == null) {
      end = (length / 2).round();
    } else {
      if (end > length) {
        end = length;
      }
    }
    for (var i = 0; i < length; i++) {
      if (i >= end) {
        buffer.write(String.fromCharCode(runes.elementAt(i)));
        continue;
      }
      if (i >= begin) {
        buffer.write(replace);
        continue;
      }
      buffer.write(String.fromCharCode(runes.elementAt(i)));
    }
    return buffer.toString();
  }
}