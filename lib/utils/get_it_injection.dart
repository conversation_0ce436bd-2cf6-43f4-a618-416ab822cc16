import 'package:bus/data/repo/create_password_repo.dart';
import 'package:bus/helper/network_serviecs.dart';
import 'package:bus/services/notification_service/fcm_notification_service.dart';
import 'package:bus/services/notification_service/local_notification_service.dart';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter_local_notifications/flutter_local_notifications.dart';
import 'package:get_it/get_it.dart';
import 'package:shared_preferences/shared_preferences.dart';

import 'navigation_helper.dart';

final getIt = GetIt.instance;

Future<void> init() async {

  //! ----------- app -----------
  final sharedPreferences = await SharedPreferences.getInstance();
  getIt.registerLazySingleton<SharedPreferences>(() => sharedPreferences);
  getIt.registerLazySingleton<FCMNotificationService>(() => FCMNotificationService( FirebaseMessaging.instance),);
  getIt.registerLazySingleton<LocalNotificationService>(() => LocalNotificationService(FlutterLocalNotificationsPlugin()),);
  getIt.registerSingleton<NavHelper>(NavHelper());

  // Register NetworkService
  final networkService = NetworkService();
  getIt.registerLazySingleton(() => networkService);

  // Register CreatePasswordRepo
  getIt.registerLazySingleton(() => CreatePasswordRepo());
}